import collections
import csv
from functools import cached_property
import json
import logging
import os
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional
from aetion.adip.airflow.integrations.s3.s3_support import AetionS3FileSystem
from airflow.exceptions import AirflowException
from tools.git_credentials import GitCredentials

logger = logging.getLogger(__name__)

REPORT_DIR = "reportTemplates/"
UUID_MAP_CSV = "uuidMap.csv"
SHARD_METADATA_JSON = "shard_metadata.json"


@dataclass
class ReportTemplate:
    file: Path
    uuid: str


@dataclass(order=True, eq=True)
class PackageFileMetadata:
    filename: str
    metadatastore_path: str
    bucket_override: Optional[str] = None
    shard_uuid_override: Optional[str] = None

    def __eq__(self, value):
        if not isinstance(value, PackageFileMetadata):
            return False
        return self.__dict__ == value.__dict__


@dataclass(order=True, eq=True)
class ShardDeploymentMetadata:
    client_environment_name: str
    deployment_bucket: str
    shard_uuid: str


@dataclass(order=True, eq=True)
class DeploymentBranchMetadata:
    branch_suffix: str
    shard_files: List[ShardDeploymentMetadata]


class DeployCommonHook:
    """
    Common logic for create_deployment_package and deploy_package_build.
    """

    def __init__(self, git_creds: GitCredentials, aws_conn_id: str = "aws_default"):
        self.git_creds = git_creds
        self.aws_conn_id = aws_conn_id

    @cached_property
    def fs(self):
        return AetionS3FileSystem(self.aws_conn_id)

    def validate_report_template_exists(
        self, tmp_git: str, dataset_name: str, tag: str, revision: str
    ) -> None:
        # ensure there's a report template
        report_path = self.find_report_template(
            Path(tmp_git), dataset_name, tag, revision
        )
        logger.info(f"found report template at {report_path}")

    @staticmethod
    def get_report(
        metadata: Path, name: str, tag: str, revision: str
    ) -> ReportTemplate | None:
        """
        Iterate though the mapping file and find the matching report.
        Entries in uuidMap.csv are like:
        name / tag / revision / file.ftl, uuid
        We search for one that has the most specific match for name / tag / revision.
        """
        mapping_file = metadata / REPORT_DIR.rstrip("/") / UUID_MAP_CSV

        candidate_key_elements = [name, tag, revision]
        candidates: Dict[str, ReportTemplate] = {}
        for report_field, uuid_field in csv.reader(
            mapping_file.open("r", encoding="UTF-8", newline="")
        ):
            report_parts = Path(report_field).parts
            report_parts_size = len(report_parts)

            dataset_name: str = report_parts[0]
            dataset_tag: Optional[str] = (
                report_parts[1] if report_parts_size > 2 else None
            )
            dataset_revision: Optional[str] = (
                report_parts[2] if report_parts_size > 3 else None
            )

            candidate_key = [dataset_name]
            if dataset_tag:
                candidate_key = [*candidate_key, dataset_tag]
            if dataset_revision:
                candidate_key = [*candidate_key, dataset_revision]

            candidate_key_str = "/".join(candidate_key)
            if candidate_key_elements[: len(candidate_key)] == candidate_key:
                if candidate_key_str not in candidates:
                    candidates[candidate_key_str] = ReportTemplate(
                        file=Path(report_field), uuid=uuid_field
                    )
                else:
                    raise AirflowException(
                        f"Duplicate row found in {str(mapping_file)}:\n"
                        f" - Current: {report_field},{uuid_field}.\n"
                        f" - Previous: {str(candidates[candidate_key_str].file)},"
                        f"{candidates[candidate_key_str].uuid}"
                    )

        match_level = 3
        while match_level > 0:
            candidate_key_str = "/".join(candidate_key_elements[0:match_level])
            if candidate_key_str in candidates:
                return candidates[candidate_key_str]
            match_level -= 1

    def find_report_template(
        self, metadatastore_repo_dir: Path, name: str, tag: str, revision: str
    ):
        logger.info("searching for report template UUID mapping in uuidMap.csv")
        report: ReportTemplate | None = self.get_report(
            metadatastore_repo_dir, name, tag, revision
        )
        if not report:
            err = (
                f"ERROR : Could not find the report template UUID mapping for dataset: {name} in uuidMap.csv. "
                "Please add an entry in uuidMap.csv."
            )
            logger.error(err)
            raise AirflowException(err)
        logger.info(f"found report template UUID mapping for dataset: {name}")
        if not (metadatastore_repo_dir / REPORT_DIR.rstrip("/") / report.file).exists():
            err = f"The report template file {str(report.file)} not found in the repository."
            logger.error(err)
            raise AirflowException(err)
        return REPORT_DIR.rstrip("/") / report.file

    def update_aetion_pkg_file(
        self,
        file_s3_path: str,
        file_local_path: str,
        package_file: PackageFileMetadata,
        dataset_name: str,
        tag: str,
        revision: str,
        etl_dir: str,
    ) -> Dict[str, Any]:
        with self.fs.open(file_s3_path, "r") as pkg_file:
            shard_file_content: Dict[str, Any] = json.loads(
                pkg_file.read(),
                object_pairs_hook=collections.OrderedDict,  # preserves original field order
            )

        if package_file.bucket_override is not None:
            # we append a slash for deployment automation
            shard_file_content.update(
                {
                    "runtimeDir": f"s3://{package_file.bucket_override}/runtime/{dataset_name}/{tag}/{revision}/"
                }
            )
        if package_file.shard_uuid_override is not None:
            shard_file_content.update({"shardGUID": package_file.shard_uuid_override})

        # we append a slash for deployment automation
        shard_file_content.update({"etlDir": f"s3://{etl_dir.rstrip('/')}/"})
        logger.info("writing file: " + file_local_path)

        os.makedirs(os.path.dirname(file_local_path), exist_ok=True)
        with open(file_local_path, "w") as f:
            f.write(json.dumps(shard_file_content, indent=2))
        return shard_file_content

    @staticmethod
    def create_shard_metadata_json(
        metadatastore_path: Path, shard_package_data=Dict[str, Any]
    ) -> None:
        keys = (
            "name",
            "tag",
            "revision",
            "shardNum",
            "patientNum",
            "instanceNameOverride",
            "sampleShardNum",
            "sampleShardPct",
            "supportSampleData",
            "sampleDataPatientNum",
            "dataModel",
            "dataModelVersion",
            "etlDir",
            "startDate",
            "endDate",
            "createDate",
            "eventsDiscardedOutsideGDR",
        )
        dataset_path = metadatastore_path / SHARD_METADATA_JSON
        logger.info("Creating file: %s", str(dataset_path))
        dataset_json = {
            key: shard_package_data[key] for key in keys if key in shard_package_data
        }
        with dataset_path.open("w", encoding="UTF-8") as dataset_file:
            json.dump(obj=dataset_json, fp=dataset_file, indent=2)
