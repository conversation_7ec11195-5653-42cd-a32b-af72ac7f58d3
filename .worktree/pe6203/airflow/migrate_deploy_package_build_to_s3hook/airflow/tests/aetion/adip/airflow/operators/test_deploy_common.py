import json
from pathlib import Path
from typing import Optional

import pytest
from tools.deploy_common import (
    SHARD_METADATA_JSON,
    DeployCommonHook,
)

EVENTS_DISCARDED_OUTSIDE_GDR = "eventsDiscardedOutsideGDR"


@pytest.mark.parametrize("events_discarded_outside_gdr", [True, False, None])
def test_shard_metadata_json_for_eventsDiscardedOutsideGDR(
    events_discarded_outside_gdr: Optional[bool], tmp_path: Path
):
    # Given a shard package data with events_discarded_outside_gdr
    shard_package_data = {}
    if events_discarded_outside_gdr is not None:
        shard_package_data[EVENTS_DISCARDED_OUTSIDE_GDR] = events_discarded_outside_gdr

    # When create_shard_metadata_json is called
    DeployCommonHook.create_shard_metadata_json(tmp_path, shard_package_data)

    # Then the shard_metadata.json file should be created with the eventsDiscardedOutsideGDR field
    dataset_path = tmp_path / SHARD_METADATA_JSO<PERSON>
    assert dataset_path.exists()
    actual_shard_metadata = json.loads(dataset_path.read_text())
    if events_discarded_outside_gdr is None:
        assert EVENTS_DISCARDED_OUTSIDE_GDR not in actual_shard_metadata
    else:
        assert EVENTS_DISCARDED_OUTSIDE_GDR in actual_shard_metadata
        assert (
            actual_shard_metadata[EVENTS_DISCARDED_OUTSIDE_GDR]
            == events_discarded_outside_gdr
        )
