{"data_mtime": 1756971275, "dep_lines": [87, 43, 85, 11, 21, 22, 23, 24, 25, 30, 31, 32, 33, 34, 53, 6, 8, 9, 10, 12, 13, 14, 15, 62, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 18, 19, 17], "dep_prios": [25, 5, 25, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 10], "dependencies": ["git.objects.submodule.base", "git.repo.fun", "git.refs.symbolic", "os.path", "git.cmd", "git.compat", "git.config", "git.db", "git.exc", "git.index", "git.objects", "git.refs", "git.remote", "git.util", "git.types", "__future__", "gc", "logging", "os", "pathlib", "re", "shlex", "warnings", "typing", "builtins", "_frozen_importlib", "abc", "configparser", "enum", "git.diff", "git.index.base", "git.objects.base", "git.objects.blob", "git.objects.commit", "git.objects.submodule", "git.objects.tag", "git.objects.tree", "git.objects.util", "git.refs.head", "git.refs.reference", "git.refs.tag", "types", "typing_extensions"], "hash": "b2bcc8d680718db0d44738fe48196365754108ec", "id": "git.repo.base", "ignore_all": true, "interface_hash": "b6d53f38afd975c56dec338b188896739b6c09fe", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/repo/base.py", "plugin_data": null, "size": 56529, "suppressed": ["gitdb.db.loose", "gitdb.exc", "gitdb"], "version_id": "1.15.0"}