{"data_mtime": 1756971275, "dep_lines": [12, 13, 34, 36, 37, 8, 9, 10, 31, 35, 6, 18, 38, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 25, 5, 5, 5, 5, 25, 10, 5, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.blob", "git.objects.util", "git.objects.tree", "git.repo.base", "git.objects.base", "git.cmd", "git.compat", "git.util", "git.types", "git.objects", "re", "typing", "subprocess", "git", "builtins", "_frozen_importlib", "abc", "enum", "git.objects.commit", "git.objects.submodule", "git.objects.submodule.base", "git.repo", "os", "types", "typing_extensions"], "hash": "d5852a0b121b4810cef7173ccbff7884c0f1d39f", "id": "git.diff", "ignore_all": true, "interface_hash": "c8e51b6990004de1da708e415cbf760c95109ffc", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/diff.py", "plugin_data": null, "size": 23583, "suppressed": [], "version_id": "1.15.0"}