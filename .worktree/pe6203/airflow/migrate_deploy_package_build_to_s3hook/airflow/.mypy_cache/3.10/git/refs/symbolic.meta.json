{"data_mtime": 1756971275, "dep_lines": [8, 19, 6, 7, 9, 34, 37, 38, 40, 4, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 17], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 20, 25, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["git.objects.commit", "git.refs.log", "git.compat", "git.objects", "git.util", "git.types", "git.repo", "git.refs", "git.config", "os", "typing", "builtins", "_frozen_importlib", "_io", "abc", "git.diff", "git.objects.base", "git.objects.blob", "git.objects.tag", "git.objects.tree", "git.objects.util", "git.refs.head", "git.refs.reference", "git.refs.remote", "git.refs.tag", "git.repo.base", "io", "typing_extensions"], "hash": "9d603f74b744cbe2d3f1c3e7bd638a5dc92e6628", "id": "git.refs.symbolic", "ignore_all": true, "interface_hash": "5ef4998f0f2f0205a6fea1889cecb304799a1f23", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/refs/symbolic.py", "plugin_data": null, "size": 33371, "suppressed": ["gitdb.exc"], "version_id": "1.15.0"}