{"data_mtime": 1756971275, "dep_lines": [18, 5, 6, 15, 20, 21, 4, 7, 8, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 25, 25, 10, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.submodule.base", "git.exc", "git.config", "git.types", "git.repo", "git.refs", "git", "io", "weakref", "typing", "builtins", "_frozen_importlib", "_io", "abc", "configparser", "git.objects.base", "git.objects.util", "git.refs.head", "git.refs.reference", "git.refs.remote", "git.refs.symbolic", "git.remote", "git.repo.base", "git.util", "os"], "hash": "b036052eb4d8b175dceb139750524168ac2d9450", "id": "git.objects.submodule.util", "ignore_all": true, "interface_hash": "3206ce391fd9c446d5f72ba7be8bf08b29924be3", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/objects/submodule/util.py", "plugin_data": null, "size": 3459, "suppressed": [], "version_id": "1.15.0"}