{"data_mtime": 1756971275, "dep_lines": [9, 8, 14, 24, 31, 35, 36, 4, 5, 6, 24, 29, 34, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 25, 25, 5, 10, 10, 20, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.util", "git.compat", "git.util", "os.path", "git.types", "git.refs", "git.config", "mmap", "re", "time", "os", "typing", "io", "builtins", "_frozen_importlib", "_io", "abc", "configparser", "enum", "git.objects", "git.refs.symbolic"], "hash": "60ab89af283ad0fd294a57a80a2935bcbf75f60d", "id": "git.refs.log", "ignore_all": true, "interface_hash": "a3f5ae14304e7f5ccff46ec9a88e8af8768e38bc", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/refs/log.py", "plugin_data": null, "size": 12072, "suppressed": [], "version_id": "1.15.0"}