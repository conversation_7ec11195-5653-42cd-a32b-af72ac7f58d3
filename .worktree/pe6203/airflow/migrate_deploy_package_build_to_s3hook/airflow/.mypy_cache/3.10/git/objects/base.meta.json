{"data_mtime": 1756971275, "dep_lines": [26, 12, 24, 25, 27, 6, 7, 10, 19, 22, 10, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9, 23, 9], "dep_prios": [25, 5, 25, 25, 25, 5, 5, 10, 5, 25, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 25, 20], "dependencies": ["git.objects.submodule.base", "git.objects.util", "git.objects.tree", "git.objects.blob", "git.refs.reference", "git.exc", "git.util", "os.path", "git.types", "git.repo", "os", "typing", "builtins", "_frozen_importlib", "abc", "git.db", "git.diff", "git.objects.commit", "git.objects.tag", "git.refs", "git.refs.symbolic", "git.repo.base"], "hash": "96e1ad41c6d6f1542a6077b2602a04e5979ef36e", "id": "git.objects.base", "ignore_all": true, "interface_hash": "28441d70de742c21155eb1b254d2c9c62cc1b471", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/objects/base.py", "plugin_data": null, "size": 7935, "suppressed": ["gitdb.typ", "gitdb.base", "gitdb"], "version_id": "1.15.0"}