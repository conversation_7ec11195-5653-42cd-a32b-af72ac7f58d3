{"data_mtime": 1756971275, "dep_lines": [33, 22, 23, 8, 13, 14, 15, 16, 24, 46, 49, 50, 51, 4, 5, 6, 7, 9, 10, 12, 43, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 25, 25, 25, 10, 5, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.submodule.util", "git.objects.base", "git.objects.util", "os.path", "git.cmd", "git.compat", "git.config", "git.exc", "git.util", "git.types", "git.index", "git.repo", "git.refs", "gc", "io", "logging", "os", "stat", "uuid", "git", "typing", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "configparser", "git.diff", "git.index.base", "git.index.typ", "git.objects.blob", "git.objects.commit", "git.objects.tag", "git.objects.tree", "git.refs.head", "git.refs.reference", "git.refs.symbolic", "git.remote", "git.repo.base", "types", "typing_extensions"], "hash": "47a312340830c2a65a46e0677ddcc41294be495d", "id": "git.objects.submodule.base", "ignore_all": true, "interface_hash": "d54f4e2c421779475428c2fc17208ba7035f5318", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/objects/submodule/base.py", "plugin_data": null, "size": 61728, "suppressed": [], "version_id": "1.15.0"}