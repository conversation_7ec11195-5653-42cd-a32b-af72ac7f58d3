{"data_mtime": 1756971275, "dep_lines": [32, 34, 36, 13, 14, 15, 16, 22, 23, 28, 33, 6, 8, 9, 10, 11, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 17], "dep_prios": [25, 25, 25, 5, 5, 5, 5, 10, 5, 5, 25, 5, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["git.repo.base", "git.refs.reference", "git.refs.tag", "git.exc", "git.objects", "git.refs", "git.util", "os.path", "git.cmd", "git.types", "git.db", "__future__", "os", "stat", "pathlib", "string", "typing", "builtins", "_frozen_importlib", "abc", "git.diff", "git.objects.base", "git.objects.blob", "git.objects.commit", "git.objects.tag", "git.objects.tree", "git.objects.util", "git.refs.symbolic"], "hash": "4d547dc446b4b51174972fec226e3c62a8905d04", "id": "git.repo.fun", "ignore_all": true, "interface_hash": "a6134854e55c159201667860e02db5bdf47dfd96", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/repo/fun.py", "plugin_data": null, "size": 13083, "suppressed": ["gitdb.exc"], "version_id": "1.15.0"}