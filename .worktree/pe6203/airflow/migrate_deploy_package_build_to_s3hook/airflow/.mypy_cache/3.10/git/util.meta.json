{"data_mtime": 1756971275, "dep_lines": [49, 12, 21, 48, 50, 53, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 22, 26, 51, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 67], "dep_prios": [25, 10, 5, 25, 25, 5, 5, 10, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["git.repo.base", "os.path", "urllib.parse", "git.remote", "git.config", "git.types", "abc", "contextlib", "functools", "getpass", "logging", "os", "pathlib", "platform", "re", "shutil", "stat", "subprocess", "sys", "time", "warnings", "typing", "git", "builtins", "_frozen_importlib", "_io", "_typeshed", "configparser", "enum", "git.cmd", "git.repo", "io", "types", "typing_extensions"], "hash": "d619cdda1773cf7d41981b7d18316320877a8619", "id": "git.util", "ignore_all": true, "interface_hash": "359fea0b5e388acd2951cdac8dcd1399e18882be", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/util.py", "plugin_data": null, "size": 42299, "suppressed": ["gitdb.util"], "version_id": "1.15.0"}