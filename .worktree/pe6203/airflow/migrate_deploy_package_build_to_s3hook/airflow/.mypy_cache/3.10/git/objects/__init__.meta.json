{"data_mtime": 1756971275, "dep_lines": [11, 12, 13, 8, 9, 10, 11, 14, 15, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 20, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.submodule.util", "git.objects.submodule.base", "git.objects.submodule.root", "git.objects.base", "git.objects.blob", "git.objects.commit", "git.objects.submodule", "git.objects.tag", "git.objects.tree", "inspect", "builtins", "_collections_abc", "_frozen_importlib", "abc", "git.repo", "git.repo.base", "os", "types", "typing"], "hash": "48ee3295ac3b893cde51ba7ef33254fe1d33b067", "id": "git.objects", "ignore_all": true, "interface_hash": "1f6603bc1beb212a07ceb3c6250b45efac1654a3", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/objects/__init__.py", "plugin_data": null, "size": 936, "suppressed": [], "version_id": "1.15.0"}