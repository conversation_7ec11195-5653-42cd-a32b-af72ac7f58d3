{"data_mtime": 1756971275, "dep_lines": [30, 46, 56, 61, 88, 18, 22, 23, 31, 43, 44, 83, 87, 9, 10, 11, 12, 13, 14, 15, 16, 43, 65, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 40, 41], "dep_prios": [5, 5, 5, 5, 25, 5, 5, 5, 5, 10, 10, 5, 25, 10, 10, 10, 5, 10, 5, 10, 10, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["git.objects.util", "git.index.fun", "git.index.typ", "git.index.util", "git.refs.reference", "git.compat", "git.exc", "git.objects", "git.util", "git.diff", "os.path", "git.types", "git.repo", "contextlib", "datetime", "glob", "io", "os", "stat", "subprocess", "tempfile", "git", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "git.db", "git.objects.base", "git.objects.blob", "git.objects.commit", "git.objects.submodule", "git.objects.submodule.base", "git.objects.tag", "git.objects.tree", "git.refs", "git.refs.head", "git.refs.symbolic", "git.repo.base", "typing_extensions"], "hash": "e74f15404ab2568cb1110f7bb583550d90c6421d", "id": "git.index.base", "ignore_all": true, "interface_hash": "e3a9b1650b4ef3a5e76ebffa6a4e1ea763af8686", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/index/base.py", "plugin_data": null, "size": 58674, "suppressed": ["gitdb.base", "gitdb.db"], "version_id": "1.15.0"}