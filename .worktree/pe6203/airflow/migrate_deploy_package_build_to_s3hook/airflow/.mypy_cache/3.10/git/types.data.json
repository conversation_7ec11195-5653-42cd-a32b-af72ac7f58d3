{".class": "MypyFile", "_fullname": "git.types", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Blob": {".class": "SymbolTableNode", "cross_ref": "git.objects.blob.Blob", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CallableProgress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.types.CallableProgress", "line": 62, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.str", "builtins.float"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "Commit": {".class": "SymbolTableNode", "cross_ref": "git.objects.commit.Commit", "kind": "Gdef"}, "Commit_ish": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.types.Commit_ish", "line": 53, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["git.objects.commit.Commit", "git.objects.tag.TagObject", "git.objects.blob.Blob", "git.objects.tree.Tree"], "uses_pep604_syntax": false}}}, "ConfigLevels_Tup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.types.ConfigLevels_Tup", "line": 69, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "system"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "user"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "global"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "repository"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Files_TD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.types.Files_TD", "name": "Files_TD", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.types.Files_TD", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.types", "mro": ["git.types.Files_TD", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["insertions", "builtins.int"], ["deletions", "builtins.int"], ["lines", "builtins.int"]], "readonly_keys": [], "required_keys": ["deletions", "insertions", "lines"]}}}, "HSH_TD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.types.HSH_TD", "name": "HSH_TD", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.types.HSH_TD", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.types", "mro": ["git.types.HSH_TD", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["total", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.Total_TD"}], ["files", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.Files_TD"}], "extra_attrs": null, "type_ref": "builtins.dict"}]], "readonly_keys": [], "required_keys": ["files", "total"]}}}, "Has_Repo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["repo", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.types.Has_Repo", "name": "<PERSON>_<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "git.types.Has_Repo", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "git.types", "mro": ["git.types.Has_Repo", "builtins.object"], "names": {".class": "SymbolTable", "repo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "git.types.Has_Repo.repo", "name": "repo", "type": "git.repo.base.Repo"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.types.Has_Repo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.types.Has_Repo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Has_id_attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_id_attribute_", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.types.Has_id_attribute", "name": "Has_id_attribute", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "git.types.Has_id_attribute", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "git.types", "mro": ["git.types.Has_id_attribute", "builtins.object"], "names": {".class": "SymbolTable", "_id_attribute_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_abstract_var", "is_ready"], "fullname": "git.types.Has_id_attribute._id_attribute_", "name": "_id_attribute_", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.types.Has_id_attribute.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.types.Has_id_attribute", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Lit_commit_ish": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.types.Lit_commit_ish", "line": 54, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "commit"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tag"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blob"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tree"}], "uses_pep604_syntax": false}}}, "Lit_config_levels": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.types.Lit_config_levels", "line": 58, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "system"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "global"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "user"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "repository"}], "uses_pep604_syntax": false}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PathLike": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.types.PathLike", "line": 41, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}], "uses_pep604_syntax": false}}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "Repo": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.Repo", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef"}, "TBD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.types.TBD", "line": 49, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TagObject": {".class": "SymbolTableNode", "cross_ref": "git.objects.tag.TagObject", "kind": "Gdef"}, "Total_TD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.types.Total_TD", "name": "Total_TD", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.types.Total_TD", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.types", "mro": ["git.types.Total_TD", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["insertions", "builtins.int"], ["deletions", "builtins.int"], ["lines", "builtins.int"], ["files", "builtins.int"]], "readonly_keys": [], "required_keys": ["deletions", "files", "insertions", "lines"]}}}, "Tree": {".class": "SymbolTableNode", "cross_ref": "git.objects.tree.Tree", "kind": "Gdef"}, "Tree_ish": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "git.types.Tree_ish", "line": 52, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["git.objects.commit.Commit", "git.objects.tree.Tree"], "uses_pep604_syntax": false}}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.types._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.types.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.types.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.types.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.types.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.types.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.types.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "assert_never": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["inp", "raise_error", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.types.assert_never", "name": "assert_never", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["inp", "raise_error", "exc"], "arg_types": [{".class": "UninhabitedType"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.Exception", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_never", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "runtime_checkable": {".class": "SymbolTableNode", "cross_ref": "typing.runtime_checkable", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/types.py"}