{".class": "MypyFile", "_fullname": "git.exc", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AmbiguousObjectName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.exc.AmbiguousObjectName", "name": "AmbiguousObjectName", "type": {".class": "AnyType", "missing_import_name": "git.exc.AmbiguousObjectName", "source_any": null, "type_of_any": 3}}}, "BadName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.exc.BadName", "name": "BadName", "type": {".class": "AnyType", "missing_import_name": "git.exc.BadName", "source_any": null, "type_of_any": 3}}}, "BadObject": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.exc.BadObject", "name": "BadObject", "type": {".class": "AnyType", "missing_import_name": "git.exc.BadObject", "source_any": null, "type_of_any": 3}}}, "BadObjectType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.exc.BadObjectType", "name": "BadObjectType", "type": {".class": "AnyType", "missing_import_name": "git.exc.BadObjectType", "source_any": null, "type_of_any": 3}}}, "CacheError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.exc.GitError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.Cache<PERSON>rror", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.Cache<PERSON>rror", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.Cache<PERSON>rror", "git.exc.GitError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.CacheError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.Cache<PERSON>rror", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CheckoutError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.exc.GitError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.CheckoutError", "name": "CheckoutError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.CheckoutError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.CheckoutError", "git.exc.GitError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "message", "failed_files", "valid_files", "failed_reasons"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.exc.CheckoutError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "message", "failed_files", "valid_files", "failed_reasons"], "arg_types": ["git.exc.CheckoutError", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CheckoutError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.exc.CheckoutError.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["git.exc.CheckoutError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of CheckoutError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "failed_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.exc.CheckoutError.failed_files", "name": "failed_files", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "failed_reasons": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.exc.CheckoutError.failed_reasons", "name": "failed_reasons", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "valid_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.exc.CheckoutError.valid_files", "name": "valid_files", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.CheckoutError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.CheckoutError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CommandError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.exc.GitError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.CommandError", "name": "CommandError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.CommandError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.CommandError", "git.exc.GitError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "command", "status", "stderr", "stdout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.exc.CommandError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "command", "status", "stderr", "stdout"], "arg_types": ["git.exc.CommandError", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}, "builtins.Exception"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CommandError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.exc.CommandError.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["git.exc.CommandError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of CommandError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cause": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.exc.CommandError._cause", "name": "_cause", "type": "builtins.str"}}, "_cmd": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.exc.CommandError._cmd", "name": "_cmd", "type": "builtins.str"}}, "_cmdline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.exc.CommandError._cmdline", "name": "_cmdline", "type": "builtins.str"}}, "_msg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "git.exc.CommandError._msg", "name": "_msg", "type": "builtins.str"}}, "command": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.exc.CommandError.command", "name": "command", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.exc.CommandError.status", "name": "status", "type": {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}, "builtins.Exception"], "uses_pep604_syntax": false}}}, "stderr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.exc.CommandError.stderr", "name": "stderr", "type": "builtins.str"}}, "stdout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.exc.CommandError.stdout", "name": "stdout", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.CommandError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.CommandError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GitCommandError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.exc.CommandError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.GitCommandError", "name": "GitCommandError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.GitCommandError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.GitCommandError", "git.exc.CommandError", "git.exc.GitError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "command", "status", "stderr", "stdout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.exc.GitCommandError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "command", "status", "stderr", "stdout"], "arg_types": ["git.exc.GitCommandError", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}, "builtins.Exception"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GitCommandError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.GitCommandError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.GitCommandError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GitCommandNotFound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.exc.CommandError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.GitCommandNotFound", "name": "GitCommandNotFound", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.GitCommandNotFound", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.GitCommandNotFound", "git.exc.CommandError", "git.exc.GitError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "command", "cause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.exc.GitCommandNotFound.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "command", "cause"], "arg_types": ["git.exc.GitCommandNotFound", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.Exception"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GitCommandNotFound", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.GitCommandNotFound.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.GitCommandNotFound", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GitError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.GitError", "name": "GitError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.GitError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.GitError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.GitError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.GitError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HookExecutionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.exc.CommandError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.HookExecutionError", "name": "HookExecutionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.HookExecutionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.HookExecutionError", "git.exc.CommandError", "git.exc.GitError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "command", "status", "stderr", "stdout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.exc.HookExecutionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "command", "status", "stderr", "stdout"], "arg_types": ["git.exc.HookExecutionError", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}, "builtins.Exception"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HookExecutionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.HookExecutionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.HookExecutionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidDBRoot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.exc.InvalidDBRoot", "name": "InvalidDBRoot", "type": {".class": "AnyType", "missing_import_name": "git.exc.InvalidDBRoot", "source_any": null, "type_of_any": 3}}}, "InvalidGitRepositoryError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.exc.GitError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.InvalidGitRepositoryError", "name": "InvalidGitRepositoryError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.InvalidGitRepositoryError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.InvalidGitRepositoryError", "git.exc.GitError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.InvalidGitRepositoryError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.InvalidGitRepositoryError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "NoSuchPathError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.exc.GitError", "builtins.OSError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.NoSuchPathError", "name": "NoSuchPathError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.NoSuchPathError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.NoSuchPathError", "git.exc.GitError", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.NoSuchPathError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.NoSuchPathError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ODBError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.exc.ODBError", "name": "ODBError", "type": {".class": "AnyType", "missing_import_name": "git.exc.ODBError", "source_any": null, "type_of_any": 3}}}, "ParseError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.exc.ParseError", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "git.exc.ParseError", "source_any": null, "type_of_any": 3}}}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "git.types.PathLike", "kind": "Gdef", "module_public": false}, "Repo": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.Repo", "kind": "Gdef", "module_public": false}, "RepositoryDirtyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.exc.GitError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.RepositoryDirtyError", "name": "RepositoryDirtyError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.RepositoryDirtyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.RepositoryDirtyError", "git.exc.GitError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "repo", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.exc.RepositoryDirtyError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "repo", "message"], "arg_types": ["git.exc.RepositoryDirtyError", "git.repo.base.Repo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RepositoryDirtyError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.exc.RepositoryDirtyError.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["git.exc.RepositoryDirtyError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of RepositoryDirtyError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.exc.RepositoryDirtyError.message", "name": "message", "type": "builtins.str"}}, "repo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.exc.RepositoryDirtyError.repo", "name": "repo", "type": "git.repo.base.Repo"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.RepositoryDirtyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.RepositoryDirtyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UnmergedEntriesError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.exc.Cache<PERSON>rror"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.UnmergedEntriesError", "name": "UnmergedEntriesError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.UnmergedEntriesError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.UnmergedEntriesError", "git.exc.Cache<PERSON>rror", "git.exc.GitError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.UnmergedEntriesError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.UnmergedEntriesError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsafeOptionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.exc.GitError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.UnsafeOptionError", "name": "UnsafeOptionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.UnsafeOptionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.UnsafeOptionError", "git.exc.GitError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.UnsafeOptionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.UnsafeOptionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsafeProtocolError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.exc.GitError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.UnsafeProtocolError", "name": "UnsafeProtocolError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.UnsafeProtocolError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.UnsafeProtocolError", "git.exc.GitError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.UnsafeProtocolError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.UnsafeProtocolError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedOperation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.exc.UnsupportedOperation", "name": "UnsupportedOperation", "type": {".class": "AnyType", "missing_import_name": "git.exc.UnsupportedOperation", "source_any": null, "type_of_any": 3}}}, "WorkTreeRepositoryUnsupported": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.exc.InvalidGitRepositoryError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.exc.WorkTreeRepositoryUnsupported", "name": "WorkTreeRepositoryUnsupported", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.exc.WorkTreeRepositoryUnsupported", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.exc", "mro": ["git.exc.WorkTreeRepositoryUnsupported", "git.exc.InvalidGitRepositoryError", "git.exc.GitError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.exc.WorkTreeRepositoryUnsupported.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.exc.WorkTreeRepositoryUnsupported", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.exc.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.exc.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.exc.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.exc.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.exc.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.exc.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.exc.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "remove_password_if_present": {".class": "SymbolTableNode", "cross_ref": "git.util.remove_password_if_present", "kind": "Gdef", "module_public": false}, "safe_decode": {".class": "SymbolTableNode", "cross_ref": "git.compat.safe_decode", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/exc.py"}