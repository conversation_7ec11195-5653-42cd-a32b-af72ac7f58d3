{".class": "MypyFile", "_fullname": "git.repo.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Actor": {".class": "SymbolTableNode", "cross_ref": "git.util.Actor", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BadObject": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.repo.base.BadObject", "name": "BadObject", "type": {".class": "AnyType", "missing_import_name": "git.repo.base.BadObject", "source_any": null, "type_of_any": 3}}}, "BinaryIO": {".class": "SymbolTableNode", "cross_ref": "typing.BinaryIO", "kind": "Gdef", "module_public": false}, "BlameEntry": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.repo.base.BlameEntry", "name": "BlameEntry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "git.repo.base.BlameEntry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["commit", "linenos", "orig_path", "orig_<PERSON>os"]}}, "module_name": "git.repo.base", "mro": ["git.repo.base.BlameEntry", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.repo.base.BlameEntry.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.repo.base.BlameEntry.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.repo.base.BlameEntry.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "commit"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "linenos"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orig_path"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "orig_<PERSON>os"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["_cls", "commit", "linenos", "orig_path", "orig_<PERSON>os"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "git.repo.base.BlameEntry.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["_cls", "commit", "linenos", "orig_path", "orig_<PERSON>os"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of BlameEntry", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.BlameEntry._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of BlameEntry", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.repo.base.BlameEntry._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.repo.base.BlameEntry._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.repo.base.BlameEntry._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.repo.base.BlameEntry._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of BlameEntry", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "git.repo.base.BlameEntry._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of BlameEntry", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["_self", "commit", "linenos", "orig_path", "orig_<PERSON>os"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.BlameEntry._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["_self", "commit", "linenos", "orig_path", "orig_<PERSON>os"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of BlameEntry", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry._NT", "id": -1, "name": "_NT", "namespace": "git.repo.base.BlameEntry._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.repo.base.BlameEntry._source", "name": "_source", "type": "builtins.str"}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.repo.base.BlameEntry.commit", "name": "commit", "type": {".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "commit-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.BlameEntry.commit", "kind": "<PERSON><PERSON><PERSON>"}, "linenos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.repo.base.BlameEntry.linenos", "name": "linenos", "type": "builtins.range"}}, "linenos-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.BlameEntry.linenos", "kind": "<PERSON><PERSON><PERSON>"}, "orig_linenos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.repo.base.BlameEntry.orig_linenos", "name": "orig_<PERSON>os", "type": "builtins.range"}}, "orig_linenos-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.BlameEntry.orig_linenos", "kind": "<PERSON><PERSON><PERSON>"}, "orig_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "git.repo.base.BlameEntry.orig_path", "name": "orig_path", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "orig_path-redefinition": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.BlameEntry.orig_path", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.BlameEntry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": "git.repo.base.BlameEntry"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.range"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.range", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "CallableProgress": {".class": "SymbolTableNode", "cross_ref": "git.types.CallableProgress", "kind": "Gdef", "module_public": false}, "Commit": {".class": "SymbolTableNode", "cross_ref": "git.objects.commit.Commit", "kind": "Gdef", "module_public": false}, "Commit_ish": {".class": "SymbolTableNode", "cross_ref": "git.types.Commit_ish", "kind": "Gdef", "module_public": false}, "ConfigLevels_Tup": {".class": "SymbolTableNode", "cross_ref": "git.types.ConfigLevels_Tup", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "Git": {".class": "SymbolTableNode", "cross_ref": "git.cmd.Git", "kind": "Gdef", "module_public": false}, "GitCmdObjectDB": {".class": "SymbolTableNode", "cross_ref": "git.db.GitCmdObjectDB", "kind": "Gdef", "module_public": false}, "GitCommandError": {".class": "SymbolTableNode", "cross_ref": "git.exc.GitCommandError", "kind": "Gdef", "module_public": false}, "GitConfigParser": {".class": "SymbolTableNode", "cross_ref": "git.config.GitConfigParser", "kind": "Gdef", "module_public": false}, "HEAD": {".class": "SymbolTableNode", "cross_ref": "git.refs.head.HEAD", "kind": "Gdef", "module_public": false}, "Head": {".class": "SymbolTableNode", "cross_ref": "git.refs.head.Head", "kind": "Gdef", "module_public": false}, "IndexFile": {".class": "SymbolTableNode", "cross_ref": "git.index.base.IndexFile", "kind": "Gdef", "module_public": false}, "InvalidGitRepositoryError": {".class": "SymbolTableNode", "cross_ref": "git.exc.InvalidGitRepositoryError", "kind": "Gdef", "module_public": false}, "IterableList": {".class": "SymbolTableNode", "cross_ref": "git.util.IterableList", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Lit_config_levels": {".class": "SymbolTableNode", "cross_ref": "git.types.Lit_config_levels", "kind": "Gdef", "module_public": false}, "LooseObjectDB": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.repo.base.LooseObjectDB", "name": "LooseObjectDB", "type": {".class": "AnyType", "missing_import_name": "git.repo.base.LooseObjectDB", "source_any": null, "type_of_any": 3}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef", "module_public": false}, "NoSuchPathError": {".class": "SymbolTableNode", "cross_ref": "git.exc.NoSuchPathError", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_public": false}, "PathLike": {".class": "SymbolTableNode", "cross_ref": "git.types.PathLike", "kind": "Gdef", "module_public": false}, "Reference": {".class": "SymbolTableNode", "cross_ref": "git.refs.reference.Reference", "kind": "Gdef", "module_public": false}, "Remote": {".class": "SymbolTableNode", "cross_ref": "git.remote.Remote", "kind": "Gdef", "module_public": false}, "RemoteProgress": {".class": "SymbolTableNode", "cross_ref": "git.util.RemoteProgress", "kind": "Gdef", "module_public": false}, "Repo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.repo.base.Repo", "name": "Repo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.repo.base", "mro": ["git.repo.base.Repo", "builtins.object"], "names": {".class": "SymbolTable", "DAEMON_EXPORT_FILE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.DAEMON_EXPORT_FILE", "name": "DAEMON_EXPORT_FILE", "type": "builtins.str"}}, "GitCommandWrapperType": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.GitCommandWrapperType", "name": "GitCommandWrapperType", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["working_dir"], "arg_types": [{".class": "UnionType", "items": [{".class": "NoneType"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "uses_pep604_syntax": false}], "bound_args": ["git.cmd.Git"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "git.cmd.Git", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__del__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.__del__", "name": "__del__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__del__ of Repo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of Repo", "ret_type": "git.repo.base.Repo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["git.repo.base.Repo", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Repo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": [null, null], "arg_types": ["git.repo.base.Repo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of Repo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of Repo", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "path", "odbt", "search_parent_directories", "expand_vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "path", "odbt", "search_parent_directories", "expand_vars"], "arg_types": ["git.repo.base.Repo", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": "git.repo.base.LooseObjectDB", "source_any": null, "type_of_any": 3}}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Repo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.__ne__", "name": "__ne__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["git.repo.base.Repo", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ne__ of Repo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Repo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bare": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.repo.base.Repo._bare", "name": "_bare", "type": "builtins.bool"}}, "_clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["cls", "git", "url", "path", "odb_default_type", "progress", "multi_options", "allow_unsafe_protocols", "allow_unsafe_options", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.repo.base.Repo._clone", "name": "_clone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["cls", "git", "url", "path", "odb_default_type", "progress", "multi_options", "allow_unsafe_protocols", "allow_unsafe_options", "kwargs"], "arg_types": [{".class": "TypeType", "item": "git.repo.base.Repo"}, "git.cmd.Git", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "TypeType", "item": "git.db.GitCmdObjectDB"}, {".class": "UnionType", "items": ["git.util.RemoteProgress", "git.objects.submodule.base.UpdateProgress", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "git.util.RemoteProgress", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clone of <PERSON><PERSON>", "ret_type": "git.repo.base.Repo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo._clone", "name": "_clone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["cls", "git", "url", "path", "odb_default_type", "progress", "multi_options", "allow_unsafe_protocols", "allow_unsafe_options", "kwargs"], "arg_types": [{".class": "TypeType", "item": "git.repo.base.Repo"}, "git.cmd.Git", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "TypeType", "item": "git.db.GitCmdObjectDB"}, {".class": "UnionType", "items": ["git.util.RemoteProgress", "git.objects.submodule.base.UpdateProgress", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "git.util.RemoteProgress", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clone of <PERSON><PERSON>", "ret_type": "git.repo.base.Repo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_common_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "git.repo.base.Repo._common_dir", "name": "_common_dir", "type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}}}, "_config_reader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "config_level", "git_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo._config_reader", "name": "_config_reader", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "config_level", "git_dir"], "arg_types": ["git.repo.base.Repo", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.Lit_config_levels"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_config_reader of Repo", "ret_type": "git.config.GitConfigParser", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_alternates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo._get_alternates", "name": "_get_alternates", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_alternates of Repo", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_config_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "config_level", "git_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo._get_config_path", "name": "_get_config_path", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "config_level", "git_dir"], "arg_types": ["git.repo.base.Repo", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.Lit_config_levels"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_config_path of Repo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_daemon_export": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo._get_daemon_export", "name": "_get_daemon_export", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_daemon_export of Repo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo._get_description", "name": "_get_description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_description of Repo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_untracked_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo._get_untracked_files", "name": "_get_untracked_files", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["git.repo.base.Repo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_untracked_files of Repo", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_alternates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo._set_alternates", "name": "_set_alternates", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "alts"], "arg_types": ["git.repo.base.Repo", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_alternates of Repo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_daemon_export": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo._set_daemon_export", "name": "_set_daemon_export", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["git.repo.base.Repo", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_daemon_export of Repo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "descr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo._set_description", "name": "_set_description", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "descr"], "arg_types": ["git.repo.base.Repo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_description of Repo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_to_full_tag_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "git.repo.base.Repo._to_full_tag_path", "name": "_to_full_tag_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_full_tag_path of Repo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo._to_full_tag_path", "name": "_to_full_tag_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_full_tag_path of Repo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_working_tree_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "git.repo.base.Repo._working_tree_dir", "name": "_working_tree_dir", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "active_branch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.repo.base.Repo.active_branch", "name": "active_branch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "active_branch of Repo", "ret_type": "git.refs.head.Head", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.active_branch", "name": "active_branch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "active_branch of Repo", "ret_type": "git.refs.head.Head", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "alternates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.alternates", "name": "alternates", "type": "builtins.property"}}, "archive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "ostream", "treeish", "prefix", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.archive", "name": "archive", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "ostream", "treeish", "prefix", "kwargs"], "arg_types": ["git.repo.base.Repo", {".class": "UnionType", "items": ["typing.TextIO", "typing.BinaryIO"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "archive of Repo", "ret_type": "git.repo.base.Repo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.repo.base.Repo.bare", "name": "bare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bare of Repo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.bare", "name": "bare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bare of Repo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "blame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "rev", "file", "incremental", "rev_opts", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.blame", "name": "blame", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 4], "arg_names": ["self", "rev", "file", "incremental", "rev_opts", "kwargs"], "arg_types": ["git.repo.base.Repo", {".class": "UnionType", "items": ["builtins.str", "git.refs.head.HEAD"], "uses_pep604_syntax": false}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blame of <PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["git.objects.commit.Commit", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.repo.base.BlameEntry"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "blame_incremental": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "rev", "file", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.blame_incremental", "name": "blame_incremental", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "rev", "file", "kwargs"], "arg_types": ["git.repo.base.Repo", {".class": "UnionType", "items": ["builtins.str", "git.refs.head.HEAD"], "uses_pep604_syntax": true}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blame_incremental of Repo", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.repo.base.BlameEntry"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "branches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.branches", "name": "branches", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["git.refs.head.Head"], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "path", "progress", "multi_options", "allow_unsafe_protocols", "allow_unsafe_options", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.clone", "name": "clone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "path", "progress", "multi_options", "allow_unsafe_protocols", "allow_unsafe_options", "kwargs"], "arg_types": ["git.repo.base.Repo", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.CallableProgress"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clone of <PERSON><PERSON>", "ret_type": "git.repo.base.Repo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clone_from": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "url", "to_path", "progress", "env", "multi_options", "allow_unsafe_protocols", "allow_unsafe_options", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.repo.base.Repo.clone_from", "name": "clone_from", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "url", "to_path", "progress", "env", "multi_options", "allow_unsafe_protocols", "allow_unsafe_options", "kwargs"], "arg_types": [{".class": "TypeType", "item": "git.repo.base.Repo"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.CallableProgress"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clone_from of Repo", "ret_type": "git.repo.base.Repo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.clone_from", "name": "clone_from", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["cls", "url", "to_path", "progress", "env", "multi_options", "allow_unsafe_protocols", "allow_unsafe_options", "kwargs"], "arg_types": [{".class": "TypeType", "item": "git.repo.base.Repo"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "TypeAliasType", "args": [], "type_ref": "git.types.CallableProgress"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clone_from of Repo", "ret_type": "git.repo.base.Repo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of Repo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "rev"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.commit", "name": "commit", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "rev"], "arg_types": ["git.repo.base.Repo", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.Commit_ish"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commit of Repo", "ret_type": "git.objects.commit.Commit", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "common_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.repo.base.Repo.common_dir", "name": "common_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_dir of Repo", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.common_dir", "name": "common_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "common_dir of Repo", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "config_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "git.repo.base.Repo.config_level", "name": "config_level", "type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.ConfigLevels_Tup"}}}, "config_reader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "config_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.config_reader", "name": "config_reader", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "config_level"], "arg_types": ["git.repo.base.Repo", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.Lit_config_levels"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config_reader of Repo", "ret_type": "git.config.GitConfigParser", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config_writer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "config_level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.config_writer", "name": "config_writer", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "config_level"], "arg_types": ["git.repo.base.Repo", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.Lit_config_levels"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config_writer of Repo", "ret_type": "git.config.GitConfigParser", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_head": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "path", "commit", "force", "logmsg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.create_head", "name": "create_head", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "path", "commit", "force", "logmsg"], "arg_types": ["git.repo.base.Repo", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "UnionType", "items": ["git.refs.symbolic.SymbolicReference", "builtins.str"], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_head of Repo", "ret_type": "git.refs.head.Head", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_remote": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "name", "url", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.create_remote", "name": "create_remote", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "name", "url", "kwargs"], "arg_types": ["git.repo.base.Repo", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_remote of Repo", "ret_type": "git.remote.Remote", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_submodule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.create_submodule", "name": "create_submodule", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["git.repo.base.Repo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_submodule of Repo", "ret_type": "git.objects.submodule.base.Submodule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "path", "ref", "message", "force", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.create_tag", "name": "create_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "path", "ref", "message", "force", "kwargs"], "arg_types": ["git.repo.base.Repo", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "UnionType", "items": ["builtins.str", "git.refs.symbolic.SymbolicReference"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_tag of Repo", "ret_type": "git.refs.tag.TagReference", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "currently_rebasing_on": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.currently_rebasing_on", "name": "currently_rebasing_on", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "currently_rebasing_on of Repo", "ret_type": {".class": "UnionType", "items": ["git.objects.commit.Commit", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "daemon_export": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.daemon_export", "name": "daemon_export", "type": "builtins.property"}}, "delete_head": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "heads", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.delete_head", "name": "delete_head", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "heads", "kwargs"], "arg_types": ["git.repo.base.Repo", {".class": "UnionType", "items": ["builtins.str", "git.refs.head.Head"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_head of Repo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_remote": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "remote"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.delete_remote", "name": "delete_remote", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "remote"], "arg_types": ["git.repo.base.Repo", "git.remote.Remote"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_remote of Repo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.delete_tag", "name": "delete_tag", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "tags"], "arg_types": ["git.repo.base.Repo", "git.refs.tag.TagReference"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_tag of Repo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.description", "name": "description", "type": "builtins.property"}}, "git": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.git", "name": "git", "type": "git.cmd.Git"}}, "git_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.repo.base.Repo.git_dir", "name": "git_dir", "type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}}}, "has_separate_working_tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.has_separate_working_tree", "name": "has_separate_working_tree", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_separate_working_tree of Repo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "head": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.repo.base.Repo.head", "name": "head", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "head of Repo", "ret_type": "git.refs.head.HEAD", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.head", "name": "head", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "head of Repo", "ret_type": "git.refs.head.HEAD", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "heads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.repo.base.Repo.heads", "name": "heads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "heads of Repo", "ret_type": {".class": "Instance", "args": ["git.refs.head.Head"], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.heads", "name": "heads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "heads of Repo", "ret_type": {".class": "Instance", "args": ["git.refs.head.Head"], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ignored": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "paths"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.ignored", "name": "ignored", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "paths"], "arg_types": ["git.repo.base.Repo", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ignored of Repo", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.repo.base.Repo.index", "name": "index", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index of Repo", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.index", "name": "index", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "index of Repo", "ret_type": "git.index.base.IndexFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["cls", "path", "mkdir", "odbt", "expand_vars", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "git.repo.base.Repo.init", "name": "init", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["cls", "path", "mkdir", "odbt", "expand_vars", "kwargs"], "arg_types": [{".class": "TypeType", "item": "git.repo.base.Repo"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "TypeType", "item": "git.db.GitCmdObjectDB"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init of Repo", "ret_type": "git.repo.base.Repo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.init", "name": "init", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 4], "arg_names": ["cls", "path", "mkdir", "odbt", "expand_vars", "kwargs"], "arg_types": [{".class": "TypeType", "item": "git.repo.base.Repo"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "TypeType", "item": "git.db.GitCmdObjectDB"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init of Repo", "ret_type": "git.repo.base.Repo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_ancestor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ancestor_rev", "rev"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.is_ancestor", "name": "is_ancestor", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ancestor_rev", "rev"], "arg_types": ["git.repo.base.Repo", "git.objects.commit.Commit", "git.objects.commit.Commit"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_ancestor of Repo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_dirty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "index", "working_tree", "untracked_files", "submodules", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.is_dirty", "name": "is_dirty", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "index", "working_tree", "untracked_files", "submodules", "path"], "arg_types": ["git.repo.base.Repo", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_dirty of Repo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_valid_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "sha", "object_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.is_valid_object", "name": "is_valid_object", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "sha", "object_type"], "arg_types": ["git.repo.base.Repo", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_valid_object of Repo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_commits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "rev", "paths", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.iter_commits", "name": "iter_commits", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "rev", "paths", "kwargs"], "arg_types": ["git.repo.base.Repo", {".class": "UnionType", "items": ["builtins.str", "git.objects.commit.Commit", "git.refs.symbolic.SymbolicReference", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_commits of Repo", "ret_type": {".class": "Instance", "args": ["git.objects.commit.Commit"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_submodules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.iter_submodules", "name": "iter_submodules", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["git.repo.base.Repo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_submodules of Repo", "ret_type": {".class": "Instance", "args": ["git.objects.submodule.base.Submodule"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iter_trees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.iter_trees", "name": "iter_trees", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["git.repo.base.Repo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_trees of Repo", "ret_type": {".class": "Instance", "args": ["git.objects.tree.Tree"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "merge_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "rev", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.merge_base", "name": "merge_base", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "rev", "kwargs"], "arg_types": ["git.repo.base.Repo", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.TBD"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge_base of Repo", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.Commit_ish"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "odb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "git.repo.base.Repo.odb", "name": "odb", "type": "git.db.GitCmdObjectDB"}}, "re_author_committer_start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.re_author_committer_start", "name": "re_author_committer_start", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "re_envvars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.re_envvars", "name": "re_envvars", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "re_hexsha_only": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.re_hexsha_only", "name": "re_hexsha_only", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "re_hexsha_shortened": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.re_hexsha_shortened", "name": "re_hexsha_shortened", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "re_tab_full_line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.re_tab_full_line", "name": "re_tab_full_line", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "re_whitespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.re_whitespace", "name": "re_whitespace", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "references": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.repo.base.Repo.references", "name": "references", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "references of Repo", "ret_type": {".class": "Instance", "args": ["git.refs.reference.Reference"], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.references", "name": "references", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "references of Repo", "ret_type": {".class": "Instance", "args": ["git.refs.reference.Reference"], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "refs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.refs", "name": "refs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["git.refs.reference.Reference"], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remote": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.remote", "name": "remote", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "name"], "arg_types": ["git.repo.base.Repo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remote of Repo", "ret_type": "git.remote.Remote", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remotes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.repo.base.Repo.remotes", "name": "remotes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remotes of Repo", "ret_type": {".class": "Instance", "args": ["git.remote.Remote"], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.remotes", "name": "remotes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remotes of Repo", "ret_type": {".class": "Instance", "args": ["git.remote.Remote"], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "rev_parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.rev_parse", "name": "rev_parse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["repo", "rev"], "arg_types": ["git.repo.base.Repo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["git.objects.commit.Commit", "git.refs.tag.TagReference", "git.objects.tree.Tree", "git.objects.blob.Blob"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "submodule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.submodule", "name": "submodule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["git.repo.base.Repo", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "submodule of Repo", "ret_type": "git.objects.submodule.base.Submodule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "submodule_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.submodule_update", "name": "submodule_update", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["git.repo.base.Repo", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "submodule_update of Repo", "ret_type": {".class": "Instance", "args": ["git.objects.submodule.base.Submodule"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "submodules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.repo.base.Repo.submodules", "name": "submodules", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "submodules of Repo", "ret_type": {".class": "Instance", "args": ["git.objects.submodule.base.Submodule"], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.submodules", "name": "submodules", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "submodules of Repo", "ret_type": {".class": "Instance", "args": ["git.objects.submodule.base.Submodule"], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.tag", "name": "tag", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["git.repo.base.Repo", {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tag of Repo", "ret_type": "git.refs.tag.TagReference", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.repo.base.Repo.tags", "name": "tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tags of Repo", "ret_type": {".class": "Instance", "args": ["git.refs.tag.TagReference"], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.tags", "name": "tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tags of Repo", "ret_type": {".class": "Instance", "args": ["git.refs.tag.TagReference"], "extra_attrs": null, "type_ref": "git.util.IterableList"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "rev"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.repo.base.Repo.tree", "name": "tree", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "rev"], "arg_types": ["git.repo.base.Repo", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.Tree_ish"}, "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tree of Repo", "ret_type": "git.objects.tree.Tree", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unsafe_git_clone_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.repo.base.Repo.unsafe_git_clone_options", "name": "unsafe_git_clone_options", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "untracked_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.repo.base.Repo.untracked_files", "name": "untracked_files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "untracked_files of Repo", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.untracked_files", "name": "untracked_files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "untracked_files of Repo", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "working_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "git.repo.base.Repo.working_dir", "name": "working_dir", "type": {".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}}}, "working_tree_dir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "git.repo.base.Repo.working_tree_dir", "name": "working_tree_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "working_tree_dir of Repo", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "git.repo.base.Repo.working_tree_dir", "name": "working_tree_dir", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "working_tree_dir of Repo", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.repo.base.Repo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.repo.base.Repo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RootModule": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.root.RootModule", "kind": "Gdef", "module_public": false}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "Submodule": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.base.Submodule", "kind": "Gdef", "module_public": false}, "SymbolicReference": {".class": "SymbolTableNode", "cross_ref": "git.refs.symbolic.SymbolicReference", "kind": "Gdef", "module_public": false}, "TBD": {".class": "SymbolTableNode", "cross_ref": "git.types.TBD", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TagReference": {".class": "SymbolTableNode", "cross_ref": "git.refs.tag.TagReference", "kind": "Gdef", "module_public": false}, "TextIO": {".class": "SymbolTableNode", "cross_ref": "typing.TextIO", "kind": "Gdef", "module_public": false}, "Tree": {".class": "SymbolTableNode", "cross_ref": "git.objects.tree.Tree", "kind": "Gdef", "module_public": false}, "Tree_ish": {".class": "SymbolTableNode", "cross_ref": "git.types.Tree_ish", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UpdateProgress": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.base.UpdateProgress", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.repo.base.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.repo.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.repo.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.repo.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.repo.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.repo.base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.repo.base.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_progress": {".class": "SymbolTableNode", "cross_ref": "git.remote.add_progress", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "assert_never": {".class": "SymbolTableNode", "cross_ref": "git.types.assert_never", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "cygpath": {".class": "SymbolTableNode", "cross_ref": "git.util.cygpath", "kind": "Gdef", "module_public": false}, "defenc": {".class": "SymbolTableNode", "cross_ref": "git.compat.defenc", "kind": "Gdef", "module_public": false}, "expand_path": {".class": "SymbolTableNode", "cross_ref": "git.util.expand_path", "kind": "Gdef", "module_public": false}, "finalize_process": {".class": "SymbolTableNode", "cross_ref": "git.util.finalize_process", "kind": "Gdef", "module_public": false}, "find_submodule_git_dir": {".class": "SymbolTableNode", "cross_ref": "git.repo.fun.find_submodule_git_dir", "kind": "Gdef", "module_public": false}, "find_worktree_git_dir": {".class": "SymbolTableNode", "cross_ref": "git.repo.fun.find_worktree_git_dir", "kind": "Gdef", "module_public": false}, "gc": {".class": "SymbolTableNode", "cross_ref": "gc", "kind": "Gdef", "module_public": false}, "gitdb": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "git.repo.base.gitdb", "name": "gitdb", "type": {".class": "AnyType", "missing_import_name": "git.repo.base.gitdb", "source_any": null, "type_of_any": 3}}}, "handle_process_output": {".class": "SymbolTableNode", "cross_ref": "git.cmd.handle_process_output", "kind": "Gdef", "module_public": false}, "hex_to_bin": {".class": "SymbolTableNode", "cross_ref": "git.util.hex_to_bin", "kind": "Gdef", "module_public": false}, "is_git_dir": {".class": "SymbolTableNode", "cross_ref": "git.repo.fun.is_git_dir", "kind": "Gdef", "module_public": false}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.repo.base.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "osp": {".class": "SymbolTableNode", "cross_ref": "os.path", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "remove_password_if_present": {".class": "SymbolTableNode", "cross_ref": "git.util.remove_password_if_present", "kind": "Gdef", "module_public": false}, "rev_parse": {".class": "SymbolTableNode", "cross_ref": "git.repo.fun.rev_parse", "kind": "Gdef", "module_public": false}, "safe_decode": {".class": "SymbolTableNode", "cross_ref": "git.compat.safe_decode", "kind": "Gdef", "module_public": false}, "shlex": {".class": "SymbolTableNode", "cross_ref": "shlex", "kind": "Gdef", "module_public": false}, "to_progress_instance": {".class": "SymbolTableNode", "cross_ref": "git.remote.to_progress_instance", "kind": "Gdef", "module_public": false}, "touch": {".class": "SymbolTableNode", "cross_ref": "git.repo.fun.touch", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/repo/base.py"}