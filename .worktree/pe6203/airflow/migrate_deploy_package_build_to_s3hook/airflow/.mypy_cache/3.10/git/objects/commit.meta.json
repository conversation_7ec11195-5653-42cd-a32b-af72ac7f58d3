{"data_mtime": 1756971275, "dep_lines": [14, 15, 16, 10, 11, 12, 15, 47, 50, 51, 6, 7, 8, 25, 26, 27, 28, 29, 34, 626, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9], "dep_prios": [5, 10, 5, 5, 5, 5, 20, 5, 25, 20, 10, 10, 5, 5, 10, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["git.objects.tree", "git.objects.base", "git.objects.util", "git.util", "git.diff", "git.cmd", "git.objects", "git.types", "git.repo", "git.refs", "datetime", "re", "subprocess", "time", "os", "io", "logging", "collections", "typing", "git", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "configparser", "git.config", "git.db", "git.objects.blob", "git.objects.tag", "git.refs.head", "git.refs.reference", "git.refs.symbolic", "git.repo.base", "types", "typing_extensions"], "hash": "f451daaaaf5e3e1f44faf6610d411ad958ca5efa", "id": "git.objects.commit", "ignore_all": true, "interface_hash": "e66ffaa29ee042e65f0ab7cceb1bdadd8aa46c7b", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/objects/commit.py", "plugin_data": null, "size": 28991, "suppressed": ["gitdb"], "version_id": "1.15.0"}