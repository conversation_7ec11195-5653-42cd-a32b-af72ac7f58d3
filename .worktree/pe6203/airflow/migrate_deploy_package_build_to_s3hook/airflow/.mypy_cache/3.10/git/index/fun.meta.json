{"data_mtime": 1756971275, "dep_lines": [24, 33, 34, 43, 45, 8, 21, 22, 23, 29, 40, 44, 6, 7, 9, 10, 19, 38, 1, 1, 1, 1, 1, 1, 1, 30, 31], "dep_prios": [5, 5, 5, 25, 25, 10, 5, 5, 5, 5, 5, 25, 5, 10, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["git.objects.fun", "git.index.typ", "git.index.util", "git.index.base", "git.objects.tree", "os.path", "git.cmd", "git.compat", "git.exc", "git.util", "git.types", "git.db", "io", "os", "pathlib", "stat", "subprocess", "typing", "builtins", "_frozen_importlib", "_stat", "abc", "git.diff", "git.objects", "git.objects.util"], "hash": "4ea6863901147551b66e7136c75f57f5a8a69e70", "id": "git.index.fun", "ignore_all": true, "interface_hash": "977937f76ad448debfca9503526c5d98bef9bc87", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/index/fun.py", "plugin_data": null, "size": 16480, "suppressed": ["gitdb.base", "gitdb.typ"], "version_id": "1.15.0"}