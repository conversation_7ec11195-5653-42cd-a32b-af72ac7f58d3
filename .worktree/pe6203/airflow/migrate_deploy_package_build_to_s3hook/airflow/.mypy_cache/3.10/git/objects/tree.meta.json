{"data_mtime": 1756971275, "dep_lines": [13, 10, 11, 12, 15, 6, 7, 10, 34, 37, 7, 20, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 10, 20, 5, 25, 20, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.objects.submodule.base", "git.objects.util", "git.objects.base", "git.objects.blob", "git.objects.fun", "git.util", "git.diff", "git.objects", "git.types", "git.repo", "git", "typing", "io", "builtins", "_frozen_importlib", "_io", "abc", "git.db", "git.objects.commit", "git.objects.submodule", "git.objects.tag", "git.repo.base", "os"], "hash": "17454c52e596515e2c12f64102895d8b64b5332c", "id": "git.objects.tree", "ignore_all": true, "interface_hash": "cd94dd73dabafdffae84818146126e214ab6e244", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/objects/tree.py", "plugin_data": null, "size": 14391, "suppressed": [], "version_id": "1.15.0"}