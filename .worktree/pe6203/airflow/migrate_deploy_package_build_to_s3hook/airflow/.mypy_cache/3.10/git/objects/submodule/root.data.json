{".class": "MypyFile", "_fullname": "git.objects.submodule.root", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BEGIN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.BEGIN", "name": "BEGIN", "type": "builtins.int"}}, "BRANCHCHANGE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.BRANCHCHANGE", "name": "BRANCHCHANGE", "type": "builtins.int"}}, "Commit_ish": {".class": "SymbolTableNode", "cross_ref": "git.types.Commit_ish", "kind": "Gdef", "module_public": false}, "END": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.END", "name": "END", "type": "builtins.int"}}, "InvalidGitRepositoryError": {".class": "SymbolTableNode", "cross_ref": "git.exc.InvalidGitRepositoryError", "kind": "Gdef", "module_public": false}, "IterableList": {".class": "SymbolTableNode", "cross_ref": "git.util.IterableList", "kind": "Gdef", "module_public": false}, "PATHCHANGE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.PATHCHANGE", "name": "PATHCHANGE", "type": "builtins.int"}}, "REMOVE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.REMOVE", "name": "REMOVE", "type": "builtins.int"}}, "Repo": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.Repo", "kind": "Gdef", "module_public": false}, "RootModule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.objects.submodule.base.Submodule"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.objects.submodule.root.RootModule", "name": "RootModule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "git.objects.submodule.root.RootModule", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "git.objects.submodule.root", "mro": ["git.objects.submodule.root.RootModule", "git.objects.submodule.base.Submodule", "git.objects.base.IndexObject", "git.objects.base.Object", "git.objects.util.TraversableIterableObj", "git.util.IterableObj", "git.objects.util.Traversable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.submodule.root.RootModule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "repo"], "arg_types": ["git.objects.submodule.root.RootModule", "git.repo.base.Repo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RootModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "git.objects.submodule.root.RootModule.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_clear_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.submodule.root.RootModule._clear_cache", "name": "_clear_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.submodule.root.RootModule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear_cache of RootModule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "k_root_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.RootModule.k_root_name", "name": "k_root_name", "type": "builtins.str"}}, "module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.submodule.root.RootModule.module", "name": "module", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["git.objects.submodule.root.RootModule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "module of RootModule", "ret_type": "git.repo.base.Repo", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "previous_commit", "recursive", "force_remove", "init", "to_latest_revision", "progress", "dry_run", "force_reset", "keep_going"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "git.objects.submodule.root.RootModule.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "previous_commit", "recursive", "force_remove", "init", "to_latest_revision", "progress", "dry_run", "force_reset", "keep_going"], "arg_types": ["git.objects.submodule.root.RootModule", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "git.types.Commit_ish"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "NoneType"}, "git.objects.submodule.root.RootUpdateProgress"], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of RootModule", "ret_type": "git.objects.submodule.root.RootModule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.submodule.root.RootModule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.objects.submodule.root.RootModule", "values": [], "variance": 0}, "slots": ["__weakref__", "_branch_path", "_name", "_parent_commit", "_url", "<PERSON><PERSON>", "mode", "path", "repo", "size"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RootUpdateProgress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["git.objects.submodule.base.UpdateProgress"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "git.objects.submodule.root.RootUpdateProgress", "name": "RootUpdateProgress", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "git.objects.submodule.root.RootUpdateProgress", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "git.objects.submodule.root", "mro": ["git.objects.submodule.root.RootUpdateProgress", "git.objects.submodule.base.UpdateProgress", "git.util.RemoteProgress", "builtins.object"], "names": {".class": "SymbolTable", "BRANCHCHANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.RootUpdateProgress.BRANCHCHANGE", "name": "BRANCHCHANGE", "type": "builtins.int"}}, "PATHCHANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.RootUpdateProgress.PATHCHANGE", "name": "PATHCHANGE", "type": "builtins.int"}}, "REMOVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.RootUpdateProgress.REMOVE", "name": "REMOVE", "type": "builtins.int"}}, "URLCHANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.RootUpdateProgress.URLCHANGE", "name": "URLCHANGE", "type": "builtins.int"}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "git.objects.submodule.root.RootUpdateProgress.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_num_op_codes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.RootUpdateProgress._num_op_codes", "name": "_num_op_codes", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "git.objects.submodule.root.RootUpdateProgress.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "git.objects.submodule.root.RootUpdateProgress", "values": [], "variance": 0}, "slots": ["_cur_line", "_seen_ops", "error_lines", "other_lines"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Submodule": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.base.Submodule", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "URLCHANGE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.URLCHANGE", "name": "URLCHANGE", "type": "builtins.int"}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UpdateProgress": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.base.UpdateProgress", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.submodule.root.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.submodule.root.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.submodule.root.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.submodule.root.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.submodule.root.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "git.objects.submodule.root.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "find_first_remote_branch": {".class": "SymbolTableNode", "cross_ref": "git.objects.submodule.util.find_first_remote_branch", "kind": "Gdef", "module_public": false}, "git": {".class": "SymbolTableNode", "cross_ref": "git", "kind": "Gdef", "module_public": false}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "git.objects.submodule.root.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/objects/submodule/root.py"}