{"data_mtime": 1756971275, "dep_lines": [8, 9, 20, 21, 22, 8, 10, 11, 15, 18, 13, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 25, 25, 25, 20, 5, 5, 5, 25, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["git.objects.base", "git.objects.util", "git.objects.commit", "git.objects.blob", "git.objects.tree", "git.objects", "git.util", "git.compat", "git.types", "git.repo", "typing", "builtins", "_frozen_importlib", "abc", "git.db", "git.diff", "git.repo.base"], "hash": "43d6378e425cc9b42e53f0adb41bb92721028ead", "id": "git.objects.tag", "ignore_all": true, "interface_hash": "88896d3d2a399cc756a12d97f47fa812cdd7adb8", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/objects/tag.py", "plugin_data": null, "size": 3823, "suppressed": [], "version_id": "1.15.0"}