{"data_mtime": 1756971275, "dep_lines": [62, 19, 20, 27, 59, 63, 6, 8, 9, 10, 11, 12, 13, 14, 16, 17, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 10, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["git.repo.base", "git.compat", "git.exc", "git.util", "git.types", "git.diff", "__future__", "re", "contextlib", "io", "logging", "os", "signal", "subprocess", "threading", "textwrap", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "enum", "git.repo", "types", "typing_extensions"], "hash": "14a76b69224df0181269785ade3853fa8e1c6df9", "id": "git.cmd", "ignore_all": true, "interface_hash": "358480e57e57e82a61bfa7b3f8e39ee3250283a4", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/cmd.py", "plugin_data": null, "size": 57713, "suppressed": [], "version_id": "1.15.0"}