{"data_mtime": 1756971275, "dep_lines": [54, 45, 46, 51, 50, 1, 1, 1, 1, 1, 1, 1, 1, 35], "dep_prios": [25, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["git.repo.base", "git.compat", "git.util", "git.types", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "git.repo", "os", "types", "typing_extensions"], "hash": "20a3f6ffb03b70d3c2d7bf6150f91ce5cb5a1369", "id": "git.exc", "ignore_all": true, "interface_hash": "a6e6b83251555dc8f0de3e80f7be0b0b224e1d26", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/exc.py", "plugin_data": null, "size": 6990, "suppressed": ["gitdb.exc"], "version_id": "1.15.0"}