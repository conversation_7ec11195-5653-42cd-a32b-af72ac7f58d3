{"data_mtime": 1756971275, "dep_lines": [13, 14, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 10, 1, 1, 1, 1, 12], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 5], "dependencies": ["git.exc", "git.types", "git.compat", "git.config", "git.objects", "git.refs", "git.diff", "git.db", "git.cmd", "git.repo", "git.remote", "git.index", "git.util", "typing", "builtins", "_frozen_importlib", "abc", "os"], "hash": "2f56999cc856a16b9b50950c2ea9b0eca1717a49", "id": "git", "ignore_all": true, "interface_hash": "40bfe0afc09e6823056e3876e3fe3fa5e339d03c", "mtime": 1749562651, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/git/__init__.py", "plugin_data": null, "size": 3565, "suppressed": ["gitdb.util"], "version_id": "1.15.0"}