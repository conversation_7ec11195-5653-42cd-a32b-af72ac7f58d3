{"data_mtime": **********, "dep_lines": [3, 4, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["aetion.adip.airflow.integrations.s3.s3_support", "airflow.providers.amazon.aws.hooks.s3", "unittest.mock", "typing", "builtins", "_frozen_importlib", "abc", "airflow", "airflow.hooks", "airflow.hooks.base", "airflow.providers", "airflow.providers.amazon", "airflow.providers.amazon.aws", "airflow.providers.amazon.aws.hooks", "airflow.providers.amazon.aws.hooks.base_aws", "airflow.utils", "airflow.utils.log", "airflow.utils.log.logging_mixin", "unittest"], "hash": "87888700f4b18f5c19992b7e69a83c8077eefb73", "id": "tests.aetion.adip.airflow.utils.test_factories", "ignore_all": true, "interface_hash": "51882a91d9a3158e19c5cdceea2aca3637103ba3", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/tests/aetion/adip/airflow/utils/test_factories.py", "plugin_data": null, "size": 2374, "suppressed": [], "version_id": "1.15.0"}