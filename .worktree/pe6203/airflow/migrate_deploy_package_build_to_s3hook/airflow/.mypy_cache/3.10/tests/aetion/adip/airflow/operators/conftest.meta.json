{"data_mtime": **********, "dep_lines": [6, 7, 8, 1, 2, 3, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["aetion.adip.airflow.integrations.s3.s3_support", "airflow.providers.amazon.aws.hooks.s3", "tests.aetion.adip.airflow.utils.test_factories", "collections.abc", "pytest", "boto3", "moto", "mypy_boto3_s3", "builtins", "_collections_abc", "_frozen_importlib", "_pytest", "_pytest.config", "_pytest.fixtures", "abc", "aetion", "aetion.adip", "aetion.adip.airflow", "aetion.adip.airflow.integrations", "aetion.adip.airflow.integrations.s3", "airflow", "airflow.hooks", "airflow.hooks.base", "airflow.providers", "airflow.providers.amazon", "airflow.providers.amazon.aws", "airflow.providers.amazon.aws.hooks", "airflow.providers.amazon.aws.hooks.base_aws", "airflow.utils", "airflow.utils.log", "airflow.utils.log.logging_mixin", "airflow.utils.types", "botocore", "botocore.client", "botocore.config", "moto.core", "moto.core.models", "mypy_boto3_ec2", "mypy_boto3_ec2.client", "mypy_boto3_s3.client", "tests.aetion.adip.airflow.utils", "typing", "unittest", "unittest.mock"], "hash": "a8615648f4bb3bfac0b4400fafa6c11a7d9ddde5", "id": "tests.aetion.adip.airflow.operators.conftest", "ignore_all": false, "interface_hash": "c032c56d3986617fd4237ecf9239d788f57f5317", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/tests/aetion/adip/airflow/operators/conftest.py", "plugin_data": null, "size": 2975, "suppressed": [], "version_id": "1.15.0"}