{".class": "MypyFile", "_fullname": "tests.aetion.adip.airflow.operators.conftest", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AetionS3FileSystem": {".class": "SymbolTableNode", "cross_ref": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "S3Client": {".class": "SymbolTableNode", "cross_ref": "mypy_boto3_s3.client.S3Client", "kind": "Gdef"}, "S3Hook": {".class": "SymbolTableNode", "cross_ref": "airflow.providers.amazon.aws.hooks.s3.S3Hook", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.aetion.adip.airflow.operators.conftest.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.aetion.adip.airflow.operators.conftest.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.aetion.adip.airflow.operators.conftest.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.aetion.adip.airflow.operators.conftest.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.aetion.adip.airflow.operators.conftest.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.aetion.adip.airflow.operators.conftest.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "airflow_env": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["monkeypatch"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.aetion.adip.airflow.operators.conftest.airflow_env", "name": "airflow_env", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.aetion.adip.airflow.operators.conftest.airflow_env", "name": "airflow_env", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["monkeypatch"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "airflow_env", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "basic_bucket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s3_hook"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.aetion.adip.airflow.operators.conftest.basic_bucket", "name": "basic_bucket", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s3_hook"], "arg_types": ["airflow.providers.amazon.aws.hooks.s3.S3Hook"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "basic_bucket", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.aetion.adip.airflow.operators.conftest.basic_bucket", "name": "basic_bucket", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s3_hook"], "arg_types": ["airflow.providers.amazon.aws.hooks.s3.S3Hook"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "basic_bucket", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "boto3": {".class": "SymbolTableNode", "cross_ref": "boto3", "kind": "Gdef"}, "bucket_with_dict_files": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s3_hook"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.aetion.adip.airflow.operators.conftest.bucket_with_dict_files", "name": "bucket_with_dict_files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s3_hook"], "arg_types": ["airflow.providers.amazon.aws.hooks.s3.S3Hook"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bucket_with_dict_files", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.aetion.adip.airflow.operators.conftest.bucket_with_dict_files", "name": "bucket_with_dict_files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s3_hook"], "arg_types": ["airflow.providers.amazon.aws.hooks.s3.S3Hook"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bucket_with_dict_files", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "bucket_with_enum_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s3_hook"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.aetion.adip.airflow.operators.conftest.bucket_with_enum_data", "name": "bucket_with_enum_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s3_hook"], "arg_types": ["airflow.providers.amazon.aws.hooks.s3.S3Hook"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bucket_with_enum_data", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.aetion.adip.airflow.operators.conftest.bucket_with_enum_data", "name": "bucket_with_enum_data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s3_hook"], "arg_types": ["airflow.providers.amazon.aws.hooks.s3.S3Hook"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bucket_with_enum_data", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_bucket_with_files": {".class": "SymbolTableNode", "cross_ref": "tests.aetion.adip.airflow.utils.test_factories.create_bucket_with_files", "kind": "Gdef"}, "create_mock_filesystem": {".class": "SymbolTableNode", "cross_ref": "tests.aetion.adip.airflow.utils.test_factories.create_mock_filesystem", "kind": "Gdef"}, "get_sample_dict_files": {".class": "SymbolTableNode", "cross_ref": "tests.aetion.adip.airflow.utils.test_factories.get_sample_dict_files", "kind": "Gdef"}, "get_sample_enum_data": {".class": "SymbolTableNode", "cross_ref": "tests.aetion.adip.airflow.utils.test_factories.get_sample_enum_data", "kind": "Gdef"}, "mock_filesystem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.aetion.adip.airflow.operators.conftest.mock_filesystem", "name": "mock_filesystem", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.aetion.adip.airflow.operators.conftest.mock_filesystem", "name": "mock_filesystem", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mock_filesystem", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mock_filesystem_with_files": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.aetion.adip.airflow.operators.conftest.mock_filesystem_with_files", "name": "mock_filesystem_with_files", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.aetion.adip.airflow.operators.conftest.mock_filesystem_with_files", "name": "mock_filesystem_with_files", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mock_filesystem_with_files", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mock_s3": {".class": "SymbolTableNode", "cross_ref": "moto.mock_s3", "kind": "Gdef"}, "mockito_s3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "tests.aetion.adip.airflow.operators.conftest.mockito_s3", "name": "mockito_s3", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.aetion.adip.airflow.operators.conftest.mockito_s3", "name": "mockito_s3", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mockito_s3", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}, "s3_client": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mockito_s3", "airflow_env"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "tests.aetion.adip.airflow.operators.conftest.s3_client", "name": "s3_client", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mockito_s3", "airflow_env"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "s3_client", "ret_type": {".class": "Instance", "args": ["mypy_boto3_s3.client.S3Client", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.aetion.adip.airflow.operators.conftest.s3_client", "name": "s3_client", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mockito_s3", "airflow_env"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "s3_client", "ret_type": {".class": "Instance", "args": ["mypy_boto3_s3.client.S3Client", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "s3_filesystem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["mockito_s3", "s3_hook"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "tests.aetion.adip.airflow.operators.conftest.s3_filesystem", "name": "s3_filesystem", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mockito_s3", "s3_hook"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "s3_filesystem", "ret_type": {".class": "Instance", "args": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.aetion.adip.airflow.operators.conftest.s3_filesystem", "name": "s3_filesystem", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["mockito_s3", "s3_hook"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "s3_filesystem", "ret_type": {".class": "Instance", "args": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "s3_hook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["mockito_s3"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "tests.aetion.adip.airflow.operators.conftest.s3_hook", "name": "s3_hook", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mockito_s3"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "s3_hook", "ret_type": {".class": "Instance", "args": ["airflow.providers.amazon.aws.hooks.s3.S3Hook", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.aetion.adip.airflow.operators.conftest.s3_hook", "name": "s3_hook", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["mockito_s3"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "s3_hook", "ret_type": {".class": "Instance", "args": ["airflow.providers.amazon.aws.hooks.s3.S3Hook", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/tests/aetion/adip/airflow/operators/conftest.py"}