{".class": "MypyFile", "_fullname": "tests.aetion.adip.airflow.utils.test_factories", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AetionS3FileSystem": {".class": "SymbolTableNode", "cross_ref": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Mock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.Mock", "kind": "Gdef"}, "S3Hook": {".class": "SymbolTableNode", "cross_ref": "airflow.providers.amazon.aws.hooks.s3.S3Hook", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.aetion.adip.airflow.utils.test_factories.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.aetion.adip.airflow.utils.test_factories.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.aetion.adip.airflow.utils.test_factories.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.aetion.adip.airflow.utils.test_factories.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.aetion.adip.airflow.utils.test_factories.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.aetion.adip.airflow.utils.test_factories.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "create_bucket_with_files": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["s3_hook", "bucket_name", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.aetion.adip.airflow.utils.test_factories.create_bucket_with_files", "name": "create_bucket_with_files", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["s3_hook", "bucket_name", "files"], "arg_types": ["airflow.providers.amazon.aws.hooks.s3.S3Hook", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_bucket_with_files", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_enum_csv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["enum_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.aetion.adip.airflow.utils.test_factories.create_enum_csv", "name": "create_enum_csv", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["enum_data"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_enum_csv", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_mock_filesystem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["existing_files", "file_contents"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.aetion.adip.airflow.utils.test_factories.create_mock_filesystem", "name": "create_mock_filesystem", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": ["existing_files", "file_contents"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_mock_filesystem", "ret_type": "unittest.mock.Mock", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_sample_dict_files": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["count"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.aetion.adip.airflow.utils.test_factories.get_sample_dict_files", "name": "get_sample_dict_files", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["count"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sample_dict_files", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_sample_enum_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.aetion.adip.airflow.utils.test_factories.get_sample_enum_data", "name": "get_sample_enum_data", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sample_enum_data", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/tests/aetion/adip/airflow/utils/test_factories.py"}