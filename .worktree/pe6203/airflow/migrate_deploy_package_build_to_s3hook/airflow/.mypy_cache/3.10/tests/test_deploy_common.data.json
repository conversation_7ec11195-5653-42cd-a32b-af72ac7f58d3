{".class": "MypyFile", "_fullname": "tests.test_deploy_common", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DeployCommonHook": {".class": "SymbolTableNode", "cross_ref": "tools.deploy_common.DeployCommonHook", "kind": "Gdef"}, "EVENTS_DISCARDED_OUTSIDE_GDR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "tests.test_deploy_common.EVENTS_DISCARDED_OUTSIDE_GDR", "name": "EVENTS_DISCARDED_OUTSIDE_GDR", "type": "builtins.str"}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "SHARD_METADATA_JSON": {".class": "SymbolTableNode", "cross_ref": "tools.deploy_common.SHARD_METADATA_JSON", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_deploy_common.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_deploy_common.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_deploy_common.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_deploy_common.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_deploy_common.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_deploy_common.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}, "test_shard_metadata_json_for_eventsDiscardedOutsideGDR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["events_discarded_outside_gdr", "tmp_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_deploy_common.test_shard_metadata_json_for_eventsDiscardedOutsideGDR", "name": "test_shard_metadata_json_for_eventsDiscardedOutsideGDR", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["events_discarded_outside_gdr", "tmp_path"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test_shard_metadata_json_for_eventsDiscardedOutsideGDR", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "tests.test_deploy_common.test_shard_metadata_json_for_eventsDiscardedOutsideGDR", "name": "test_shard_metadata_json_for_eventsDiscardedOutsideGDR", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/tests/test_deploy_common.py"}