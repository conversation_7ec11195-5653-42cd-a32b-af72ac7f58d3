{".class": "MypyFile", "_fullname": "hooks.git_hook", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "BaseHook": {".class": "SymbolTableNode", "cross_ref": "airflow.hooks.base.BaseHook", "kind": "Gdef"}, "DEFAULT_SSH_PORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "hooks.git_hook.DEFAULT_SSH_PORT", "name": "DEFAULT_SSH_PORT", "type": "builtins.int"}}, "GitHook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.hooks.base.BaseHook", "airflow.utils.log.logging_mixin.LoggingMixin", "abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "hooks.git_hook.GitHook", "name": "GitHook", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "hooks.git_hook", "mro": ["hooks.git_hook.GitHook", "airflow.hooks.base.BaseHook", "airflow.utils.log.logging_mixin.LoggingMixin", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "repo_url", "private_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.__init__", "name": "__init__", "type": null}}, "any_changes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.any_changes", "name": "any_changes", "type": null}}, "changed_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.changed_files", "name": "changed_files", "type": null}}, "checkout_new": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "branch_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.checkout_new", "name": "checkout_new", "type": null}}, "clone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "target_dir", "branch", "sparse_folders"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.clone", "name": "clone", "type": null}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.commit", "name": "commit", "type": null}}, "encode_and_embed_http_auth_password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["url_with_user", "password"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "hooks.git_hook.GitHook.encode_and_embed_http_auth_password", "name": "encode_and_embed_http_auth_password", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["url_with_user", "password"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode_and_embed_http_auth_password of GitHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "hooks.git_hook.GitHook.encode_and_embed_http_auth_password", "name": "encode_and_embed_http_auth_password", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["url_with_user", "password"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "encode_and_embed_http_auth_password of GitHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "files_to_clean": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "hooks.git_hook.GitHook.files_to_clean", "name": "files_to_clean", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_branch_commit_sha": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "branch"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.get_branch_commit_sha", "name": "get_branch_commit_sha", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "branch"], "arg_types": ["hooks.git_hook.GitHook", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_branch_commit_sha of GitHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_branch_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.get_branch_names", "name": "get_branch_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["hooks.git_hook.GitHook"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_branch_names of GitHook", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_repo_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "branch", "latest_commit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.get_repo_url", "name": "get_repo_url", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "branch", "latest_commit"], "arg_types": ["hooks.git_hook.GitHook", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_repo_url of GitHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_repo_local": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "target_dir", "sparse_folders"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.init_repo_local", "name": "init_repo_local", "type": null}}, "init_repo_with_https": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "target_dir", "sparse_folders"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.init_repo_with_https", "name": "init_repo_with_https", "type": null}}, "init_repo_with_ssh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "target_dir", "sparse_folders"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.init_repo_with_ssh", "name": "init_repo_with_ssh", "type": null}}, "private_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "hooks.git_hook.GitHook.private_key", "name": "private_key", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "push": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "lbranch", "rbranch", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.push", "name": "push", "type": null}}, "repo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "hooks.git_hook.GitHook.repo", "name": "repo", "type": {".class": "UnionType", "items": ["git.repo.base.Repo", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "repo_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "hooks.git_hook.GitHook.repo_url", "name": "repo_url", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "sync_branch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "branch_name", "remote_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "hooks.git_hook.GitHook.sync_branch", "name": "sync_branch", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "branch_name", "remote_name"], "arg_types": ["hooks.git_hook.GitHook", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sync_branch of GitHook", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "hooks.git_hook.GitHook.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "hooks.git_hook.GitHook", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LoggingMixin": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.log.logging_mixin.LoggingMixin", "kind": "Gdef"}, "NamedTemporaryFile": {".class": "SymbolTableNode", "cross_ref": "tempfile.NamedTemporaryFile", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Repo": {".class": "SymbolTableNode", "cross_ref": "git.repo.base.Repo", "kind": "Gdef"}, "URLError": {".class": "SymbolTableNode", "cross_ref": "urllib.error.URLError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hooks.git_hook.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hooks.git_hook.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hooks.git_hook.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hooks.git_hook.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hooks.git_hook.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "hooks.git_hook.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cmd": {".class": "SymbolTableNode", "cross_ref": "git.cmd", "kind": "Gdef"}, "git": {".class": "SymbolTableNode", "cross_ref": "git", "kind": "Gdef"}, "git_version": {".class": "SymbolTableNode", "cross_ref": "git.__version__", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "hooks.git_hook.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "quote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.quote", "kind": "Gdef"}, "quote_plus": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.quote_plus", "kind": "Gdef"}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}, "urlunparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlunparse", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/dags/hooks/git_hook.py"}