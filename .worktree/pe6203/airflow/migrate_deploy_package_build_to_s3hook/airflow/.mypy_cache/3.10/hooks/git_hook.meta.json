{"data_mtime": 1756971277, "dep_lines": [11, 10, 6, 7, 12, 1, 2, 3, 4, 5, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["airflow.utils.log.logging_mixin", "airflow.hooks.base", "urllib.error", "urllib.parse", "git.cmd", "logging", "os", "abc", "tempfile", "typing", "git", "builtins", "_frozen_importlib", "airflow", "airflow.hooks", "airflow.utils", "airflow.utils.log", "git.repo", "git.repo.base", "urllib"], "hash": "55f0c28148303c03fcda4e5afa4553c9ec670fba", "id": "hooks.git_hook", "ignore_all": true, "interface_hash": "a36d111c0edab8a70e5a9e5d10a3aaf358574e46", "mtime": 1756912943, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/dags/hooks/git_hook.py", "plugin_data": null, "size": 12019, "suppressed": [], "version_id": "1.15.0"}