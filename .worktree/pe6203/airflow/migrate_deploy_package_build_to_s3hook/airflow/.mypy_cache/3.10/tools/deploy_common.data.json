{".class": "MypyFile", "_fullname": "tools.deploy_common", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AetionS3FileSystem": {".class": "SymbolTableNode", "cross_ref": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "kind": "Gdef"}, "AirflowException": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.AirflowException", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "DeployCommonHook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tools.deploy_common.DeployCommonHook", "name": "DeployCommonHook", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tools.deploy_common.DeployCommonHook", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tools.deploy_common", "mro": ["tools.deploy_common.DeployCommonHook", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "git_creds", "aws_conn_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.DeployCommonHook.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "git_creds", "aws_conn_id"], "arg_types": ["tools.deploy_common.DeployCommonHook", "tools.git_credentials.GitCredentials", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DeployCommonHook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aws_conn_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tools.deploy_common.DeployCommonHook.aws_conn_id", "name": "aws_conn_id", "type": "builtins.str"}}, "create_shard_metadata_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["metadatastore_path", "shard_package_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "tools.deploy_common.DeployCommonHook.create_shard_metadata_json", "name": "create_shard_metadata_json", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["metadatastore_path", "shard_package_data"], "arg_types": ["pathlib.Path", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_shard_metadata_json of DeployCommonHook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "tools.deploy_common.DeployCommonHook.create_shard_metadata_json", "name": "create_shard_metadata_json", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["metadatastore_path", "shard_package_data"], "arg_types": ["pathlib.Path", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_shard_metadata_json of DeployCommonHook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "find_report_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "metadatastore_repo_dir", "name", "tag", "revision"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.DeployCommonHook.find_report_template", "name": "find_report_template", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "metadatastore_repo_dir", "name", "tag", "revision"], "arg_types": ["tools.deploy_common.DeployCommonHook", "pathlib.Path", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_report_template of DeployCommonHook", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "tools.deploy_common.DeployCommonHook.fs", "name": "fs", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "tools.deploy_common.DeployCommonHook.fs", "name": "fs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tools.deploy_common.DeployCommonHook"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fs of DeployCommonHook", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["metadata", "name", "tag", "revision"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "tools.deploy_common.DeployCommonHook.get_report", "name": "get_report", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["metadata", "name", "tag", "revision"], "arg_types": ["pathlib.Path", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_report of DeployCommonHook", "ret_type": {".class": "UnionType", "items": ["tools.deploy_common.ReportTemplate", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "tools.deploy_common.DeployCommonHook.get_report", "name": "get_report", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["metadata", "name", "tag", "revision"], "arg_types": ["pathlib.Path", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_report of DeployCommonHook", "ret_type": {".class": "UnionType", "items": ["tools.deploy_common.ReportTemplate", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "git_creds": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tools.deploy_common.DeployCommonHook.git_creds", "name": "git_creds", "type": "tools.git_credentials.GitCredentials"}}, "update_aetion_pkg_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "file_s3_path", "file_local_path", "package_file", "dataset_name", "tag", "revision", "etl_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.DeployCommonHook.update_aetion_pkg_file", "name": "update_aetion_pkg_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "file_s3_path", "file_local_path", "package_file", "dataset_name", "tag", "revision", "etl_dir"], "arg_types": ["tools.deploy_common.DeployCommonHook", "builtins.str", "builtins.str", "tools.deploy_common.PackageFileMetadata", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_aetion_pkg_file of DeployCommonHook", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate_report_template_exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tmp_git", "dataset_name", "tag", "revision"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.DeployCommonHook.validate_report_template_exists", "name": "validate_report_template_exists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tmp_git", "dataset_name", "tag", "revision"], "arg_types": ["tools.deploy_common.DeployCommonHook", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_report_template_exists of DeployCommonHook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeployCommonHook.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tools.deploy_common.DeployCommonHook", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DeploymentBranchMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tools.deploy_common.DeploymentBranchMetadata", "name": "DeploymentBranchMetadata", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tools.deploy_common.DeploymentBranchMetadata", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 49, "name": "branch_suffix", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "shard_files", "type": {".class": "Instance", "args": ["tools.deploy_common.ShardDeploymentMetadata"], "extra_attrs": null, "type_ref": "builtins.list"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "tools.deploy_common", "mro": ["tools.deploy_common.DeploymentBranchMetadata", "builtins.object"], "names": {".class": "SymbolTable", "_DT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata._DT", "name": "_DT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "tools.deploy_common.DeploymentBranchMetadata.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.DeploymentBranchMetadata.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.DeploymentBranchMetadata.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.DeploymentBranchMetadata.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of DeploymentBranchMetadata", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.DeploymentBranchMetadata.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.DeploymentBranchMetadata.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.DeploymentBranchMetadata.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.DeploymentBranchMetadata.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of DeploymentBranchMetadata", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.DeploymentBranchMetadata.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "branch_suffix", "shard_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.DeploymentBranchMetadata.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "branch_suffix", "shard_files"], "arg_types": ["tools.deploy_common.DeploymentBranchMetadata", "builtins.str", {".class": "Instance", "args": ["tools.deploy_common.ShardDeploymentMetadata"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DeploymentBranchMetadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.DeploymentBranchMetadata.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.DeploymentBranchMetadata.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.DeploymentBranchMetadata.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of DeploymentBranchMetadata", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.DeploymentBranchMetadata.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.DeploymentBranchMetadata.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.DeploymentBranchMetadata.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.DeploymentBranchMetadata.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of DeploymentBranchMetadata", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.DeploymentBranchMetadata.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "tools.deploy_common.DeploymentBranchMetadata.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "branch_suffix"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shard_files"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["branch_suffix", "shard_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "tools.deploy_common.DeploymentBranchMetadata.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["branch_suffix", "shard_files"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["tools.deploy_common.ShardDeploymentMetadata"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of DeploymentBranchMetadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "tools.deploy_common.DeploymentBranchMetadata.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["branch_suffix", "shard_files"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["tools.deploy_common.ShardDeploymentMetadata"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of DeploymentBranchMetadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "branch_suffix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tools.deploy_common.DeploymentBranchMetadata.branch_suffix", "name": "branch_suffix", "type": "builtins.str"}}, "shard_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tools.deploy_common.DeploymentBranchMetadata.shard_files", "name": "shard_files", "type": {".class": "Instance", "args": ["tools.deploy_common.ShardDeploymentMetadata"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.DeploymentBranchMetadata.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tools.deploy_common.DeploymentBranchMetadata", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GitCredentials": {".class": "SymbolTableNode", "cross_ref": "tools.git_credentials.GitCredentials", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PackageFileMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tools.deploy_common.PackageFileMetadata", "name": "PackageFileMetadata", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tools.deploy_common.PackageFileMetadata", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 29, "name": "filename", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 30, "name": "metadatastore_path", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "bucket_override", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "shard_uuid_override", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "tools.deploy_common", "mro": ["tools.deploy_common.PackageFileMetadata", "builtins.object"], "names": {".class": "SymbolTable", "_DT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata._DT", "name": "_DT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "tools.deploy_common.PackageFileMetadata.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.PackageFileMetadata.__eq__", "name": "__eq__", "type": null}}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.PackageFileMetadata.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.PackageFileMetadata.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.PackageFileMetadata.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of PackageFileMetadata", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.PackageFileMetadata.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.PackageFileMetadata.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.PackageFileMetadata.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.PackageFileMetadata.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of PackageFileMetadata", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.PackageFileMetadata.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "filename", "metadatastore_path", "bucket_override", "shard_uuid_override"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.PackageFileMetadata.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "filename", "metadatastore_path", "bucket_override", "shard_uuid_override"], "arg_types": ["tools.deploy_common.PackageFileMetadata", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PackageFileMetadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.PackageFileMetadata.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.PackageFileMetadata.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.PackageFileMetadata.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of PackageFileMetadata", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.PackageFileMetadata.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.PackageFileMetadata.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.PackageFileMetadata.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.PackageFileMetadata.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of PackageFileMetadata", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.PackageFileMetadata.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "tools.deploy_common.PackageFileMetadata.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "filename"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "metadatastore_path"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bucket_override"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shard_uuid_override"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["filename", "metadatastore_path", "bucket_override", "shard_uuid_override"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "tools.deploy_common.PackageFileMetadata.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["filename", "metadatastore_path", "bucket_override", "shard_uuid_override"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of PackageFileMetadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "tools.deploy_common.PackageFileMetadata.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["filename", "metadatastore_path", "bucket_override", "shard_uuid_override"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of PackageFileMetadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "bucket_override": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "tools.deploy_common.PackageFileMetadata.bucket_override", "name": "bucket_override", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "filename": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tools.deploy_common.PackageFileMetadata.filename", "name": "filename", "type": "builtins.str"}}, "metadatastore_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tools.deploy_common.PackageFileMetadata.metadatastore_path", "name": "metadatastore_path", "type": "builtins.str"}}, "shard_uuid_override": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "tools.deploy_common.PackageFileMetadata.shard_uuid_override", "name": "shard_uuid_override", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.PackageFileMetadata.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tools.deploy_common.PackageFileMetadata", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "REPORT_DIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "tools.deploy_common.REPORT_DIR", "name": "REPORT_DIR", "type": "builtins.str"}}, "ReportTemplate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tools.deploy_common.ReportTemplate", "name": "ReportTemplate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tools.deploy_common.ReportTemplate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 23, "name": "file", "type": "pathlib.Path"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 24, "name": "uuid", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "tools.deploy_common", "mro": ["tools.deploy_common.ReportTemplate", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "tools.deploy_common.ReportTemplate.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file", "uuid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.ReportTemplate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file", "uuid"], "arg_types": ["tools.deploy_common.ReportTemplate", "pathlib.Path", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ReportTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "tools.deploy_common.ReportTemplate.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "file"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uuid"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["file", "uuid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "tools.deploy_common.ReportTemplate.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["file", "uuid"], "arg_types": ["pathlib.Path", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ReportTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "tools.deploy_common.ReportTemplate.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["file", "uuid"], "arg_types": ["pathlib.Path", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ReportTemplate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tools.deploy_common.ReportTemplate.file", "name": "file", "type": "pathlib.Path"}}, "uuid": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tools.deploy_common.ReportTemplate.uuid", "name": "uuid", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ReportTemplate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tools.deploy_common.ReportTemplate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SHARD_METADATA_JSON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "tools.deploy_common.SHARD_METADATA_JSON", "name": "SHARD_METADATA_JSON", "type": "builtins.str"}}, "ShardDeploymentMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tools.deploy_common.ShardDeploymentMetadata", "name": "ShardDeploymentMetadata", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tools.deploy_common.ShardDeploymentMetadata", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 42, "name": "client_environment_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 43, "name": "deployment_bucket", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 44, "name": "shard_uuid", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "tools.deploy_common", "mro": ["tools.deploy_common.ShardDeploymentMetadata", "builtins.object"], "names": {".class": "SymbolTable", "_DT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata._DT", "name": "_DT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "tools.deploy_common.ShardDeploymentMetadata.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.ShardDeploymentMetadata.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.ShardDeploymentMetadata.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.ShardDeploymentMetadata.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of ShardDeploymentMetadata", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.ShardDeploymentMetadata.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.ShardDeploymentMetadata.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.ShardDeploymentMetadata.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.ShardDeploymentMetadata.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of ShardDeploymentMetadata", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.ShardDeploymentMetadata.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "client_environment_name", "deployment_bucket", "shard_uuid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.ShardDeploymentMetadata.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "client_environment_name", "deployment_bucket", "shard_uuid"], "arg_types": ["tools.deploy_common.ShardDeploymentMetadata", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ShardDeploymentMetadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.ShardDeploymentMetadata.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.ShardDeploymentMetadata.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.ShardDeploymentMetadata.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of ShardDeploymentMetadata", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.ShardDeploymentMetadata.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tools.deploy_common.ShardDeploymentMetadata.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.ShardDeploymentMetadata.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.ShardDeploymentMetadata.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of ShardDeploymentMetadata", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata._DT", "id": -1, "name": "_DT", "namespace": "tools.deploy_common.ShardDeploymentMetadata.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "tools.deploy_common.ShardDeploymentMetadata.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "client_environment_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "deployment_bucket"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "shard_uuid"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["client_environment_name", "deployment_bucket", "shard_uuid"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "tools.deploy_common.ShardDeploymentMetadata.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["client_environment_name", "deployment_bucket", "shard_uuid"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ShardDeploymentMetadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "tools.deploy_common.ShardDeploymentMetadata.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["client_environment_name", "deployment_bucket", "shard_uuid"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ShardDeploymentMetadata", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "client_environment_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tools.deploy_common.ShardDeploymentMetadata.client_environment_name", "name": "client_environment_name", "type": "builtins.str"}}, "deployment_bucket": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tools.deploy_common.ShardDeploymentMetadata.deployment_bucket", "name": "deployment_bucket", "type": "builtins.str"}}, "shard_uuid": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tools.deploy_common.ShardDeploymentMetadata.shard_uuid", "name": "shard_uuid", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tools.deploy_common.ShardDeploymentMetadata.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tools.deploy_common.ShardDeploymentMetadata", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UUID_MAP_CSV": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "tools.deploy_common.UUID_MAP_CSV", "name": "UUID_MAP_CSV", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tools.deploy_common.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tools.deploy_common.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tools.deploy_common.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tools.deploy_common.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tools.deploy_common.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tools.deploy_common.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "csv": {".class": "SymbolTableNode", "cross_ref": "csv", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "tools.deploy_common.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/dags/tools/deploy_common.py"}