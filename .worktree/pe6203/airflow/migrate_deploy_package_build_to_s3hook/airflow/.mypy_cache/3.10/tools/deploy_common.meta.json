{"data_mtime": 1756971545, "dep_lines": [10, 11, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["aetion.adip.airflow.integrations.s3.s3_support", "airflow.exceptions", "tools.git_credentials", "collections", "csv", "functools", "json", "logging", "os", "dataclasses", "pathlib", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "f940050438accc90512c573cb5b7889bf9b282d6", "id": "tools.deploy_common", "ignore_all": true, "interface_hash": "89cf8360fd105e40ffa73fbff801b5bd77c54c1e", "mtime": 1756971502, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/dags/tools/deploy_common.py", "plugin_data": null, "size": 7672, "suppressed": [], "version_id": "1.15.0"}