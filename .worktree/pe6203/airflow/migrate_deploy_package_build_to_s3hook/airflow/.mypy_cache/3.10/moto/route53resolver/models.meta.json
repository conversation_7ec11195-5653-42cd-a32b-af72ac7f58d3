{"data_mtime": 1756972668, "dep_lines": [10, 11, 12, 21, 22, 23, 24, 8, 9, 2, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["moto.ec2.exceptions", "moto.moto_api._internal", "moto.route53resolver.exceptions", "moto.route53resolver.utils", "moto.route53resolver.validations", "moto.utilities.paginator", "moto.utilities.tagging_service", "moto.core", "moto.ec2", "re", "collections", "datetime", "ipaddress", "typing", "builtins", "os", "sys", "string", "inspect", "traceback", "pprint", "types", "itertools", "warnings", "copy", "html", "_collections_abc", "_frozen_importlib", "_random", "abc", "enum", "moto.core.base_backend", "moto.core.common_models", "moto.core.exceptions", "moto.ec2.models", "moto.ec2.models.amis", "moto.ec2.models.availability_zones_and_regions", "moto.ec2.models.carrier_gateways", "moto.ec2.models.core", "moto.ec2.models.customer_gateways", "moto.ec2.models.dhcp_options", "moto.ec2.models.elastic_block_store", "moto.ec2.models.elastic_ip_addresses", "moto.ec2.models.elastic_network_interfaces", "moto.ec2.models.fleets", "moto.ec2.models.flow_logs", "moto.ec2.models.hosts", "moto.ec2.models.iam_instance_profile", "moto.ec2.models.instance_types", "moto.ec2.models.instances", "moto.ec2.models.internet_gateways", "moto.ec2.models.key_pairs", "moto.ec2.models.launch_templates", "moto.ec2.models.managed_prefixes", "moto.ec2.models.nat_gateways", "moto.ec2.models.network_acls", "moto.ec2.models.route_tables", "moto.ec2.models.security_groups", "moto.ec2.models.spot_requests", "moto.ec2.models.subnets", "moto.ec2.models.tags", "moto.ec2.models.transit_gateway", "moto.ec2.models.transit_gateway_attachments", "moto.ec2.models.transit_gateway_route_tables", "moto.ec2.models.vpc_peering_connections", "moto.ec2.models.vpc_service_configuration", "moto.ec2.models.vpcs", "moto.ec2.models.vpn_connections", "moto.ec2.models.vpn_gateway", "moto.ec2.models.windows", "moto.moto_api", "moto.moto_api._internal.moto_random", "moto.utilities", "random", "werkzeug", "werkzeug.exceptions"], "hash": "db2e0c0d2c1e7199ee6ca2f79cc47b814c86dc70", "id": "moto.route53resolver.models", "ignore_all": true, "interface_hash": "54fbdd3f24675b9b9c1f1b370a92b0bb19fb52f7", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/route53resolver/models.py", "plugin_data": null, "size": 37366, "suppressed": [], "version_id": "1.15.0"}