{"data_mtime": 1756972668, "dep_lines": [11, 14, 15, 21, 22, 24, 25, 40, 43, 636, 10, 13, 589, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 25, 20, 5, 5, 20, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["xml.sax.saxutils", "moto.core.exceptions", "moto.core.utils", "moto.moto_api._internal", "moto.utilities.utils", "moto.sqs.constants", "moto.sqs.exceptions", "moto.sqs.utils", "moto.awslambda.models", "moto.cloudformation.exceptions", "urllib.parse", "moto.core", "moto.awslambda", "base64", "<PERSON><PERSON><PERSON>", "json", "re", "string", "struct", "copy", "threading", "typing", "builtins", "os", "sys", "inspect", "traceback", "pprint", "collections", "types", "itertools", "warnings", "html", "_collections_abc", "_frozen_importlib", "_hashlib", "_random", "_thread", "_typeshed", "abc", "datetime", "enum", "json.decoder", "moto.core.base_backend", "moto.core.common_models", "moto.moto_api", "moto.moto_api._internal.moto_random", "random", "typing_extensions", "urllib", "uuid", "werkzeug", "werkzeug.exceptions"], "hash": "ab820595f42cf33024146d22bfbbc09552cfbcb6", "id": "moto.sqs.models", "ignore_all": true, "interface_hash": "557ee22b447589cc154934bfff9cbb8ef77321f9", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/sqs/models.py", "plugin_data": null, "size": 44990, "suppressed": [], "version_id": "1.15.0"}