{"data_mtime": 1756972668, "dep_lines": [8, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 5, 4, 4], "dep_prios": [5, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 5, 5, 10, 20], "dependencies": ["moto.xray.models", "os", "typing", "builtins", "re", "sys", "string", "inspect", "traceback", "pprint", "collections", "types", "itertools", "warnings", "copy", "html", "_frozen_importlib", "_typeshed", "abc", "moto.core", "moto.core.base_backend"], "hash": "ac4ce2e54f7705336d6d0aff2bbf837db876b461", "id": "moto.xray.mock_client", "ignore_all": true, "interface_hash": "69ef17e3a0e1a7d70a913ee325a55eef74dea42d", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/xray/mock_client.py", "plugin_data": null, "size": 3811, "suppressed": ["aws_xray_sdk.core.emitters.udp_emitter", "aws_xray_sdk.core.context", "aws_xray_sdk.core", "aws_xray_sdk"], "version_id": "1.15.0"}