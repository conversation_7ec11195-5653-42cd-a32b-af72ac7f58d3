{"data_mtime": 1756972668, "dep_lines": [13, 14, 15, 16, 18, 34, 553, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["moto.core.utils", "moto.moto_api._internal", "moto.utilities.paginator", "moto.utilities.utils", "moto.kinesis.exceptions", "moto.kinesis.utils", "moto.cloudformation.exceptions", "moto.core", "datetime", "io", "itertools", "json", "re", "base64", "collections", "gzip", "operator", "typing", "builtins", "os", "sys", "string", "inspect", "traceback", "pprint", "types", "warnings", "copy", "html", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "moto.core.base_backend", "moto.core.common_models", "moto.core.exceptions", "moto.utilities", "typing_extensions", "werkzeug", "werkzeug.exceptions"], "hash": "bbbd4d25f0e06aea585da24b8d3f94cc726fd42b", "id": "moto.kinesis.models", "ignore_all": true, "interface_hash": "2b0436cf3de2d80f6e547dbe4f8e0ac095aa8f5e", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/kinesis/models.py", "plugin_data": null, "size": 37339, "suppressed": [], "version_id": "1.15.0"}