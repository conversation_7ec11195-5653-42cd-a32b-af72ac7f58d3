{".class": "MypyFile", "_fullname": "moto.s3control.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AccessPoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3control.models.AccessPoint", "name": "AccessPoint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3control.models.AccessPoint", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.s3control.models", "mro": ["moto.s3control.models.AccessPoint", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "name", "bucket", "vpc_configuration", "public_access_block_configuration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.AccessPoint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "name", "bucket", "vpc_configuration", "public_access_block_configuration"], "arg_types": ["moto.s3control.models.AccessPoint", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AccessPoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "alias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.s3control.models.AccessPoint.alias", "name": "alias", "type": "builtins.str"}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.s3control.models.AccessPoint.arn", "name": "arn", "type": "builtins.str"}}, "bucket": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.s3control.models.AccessPoint.bucket", "name": "bucket", "type": "builtins.str"}}, "created": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.s3control.models.AccessPoint.created", "name": "created", "type": "builtins.str"}}, "delete_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.AccessPoint.delete_policy", "name": "delete_policy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3control.models.AccessPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_policy of AccessPoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.AccessPoint.has_policy", "name": "has_policy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3control.models.AccessPoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_policy of AccessPoint", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.s3control.models.AccessPoint.name", "name": "name", "type": "builtins.str"}}, "network_origin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.s3control.models.AccessPoint.network_origin", "name": "network_origin", "type": "builtins.str"}}, "policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.s3control.models.AccessPoint.policy", "name": "policy", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pubc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.s3control.models.AccessPoint.pubc", "name": "pubc", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "set_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.AccessPoint.set_policy", "name": "set_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "policy"], "arg_types": ["moto.s3control.models.AccessPoint", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_policy of AccessPoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vpc_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.s3control.models.AccessPoint.vpc_id", "name": "vpc_id", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3control.models.AccessPoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3control.models.AccessPoint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AccessPointNotFound": {".class": "SymbolTableNode", "cross_ref": "moto.s3control.exceptions.AccessPointNotFound", "kind": "Gdef"}, "AccessPointPolicyNotFound": {".class": "SymbolTableNode", "cross_ref": "moto.s3control.exceptions.AccessPointPolicyNotFound", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "InvalidPublicAccessBlockConfiguration": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.InvalidPublicAccessBlockConfiguration", "kind": "Gdef"}, "NoSuchPublicAccessBlockConfiguration": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.NoSuchPublicAccessBlockConfiguration", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PublicAccessBlock": {".class": "SymbolTableNode", "cross_ref": "moto.s3.models.PublicAccessBlock", "kind": "Gdef"}, "S3ControlBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3control.models.S3ControlBackend", "name": "S3ControlBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3control.models.S3ControlBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3control.models", "mro": ["moto.s3control.models.S3ControlBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.S3ControlBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.s3control.models.S3ControlBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3ControlBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "access_points": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.s3control.models.S3ControlBackend.access_points", "name": "access_points", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "moto.s3control.models.AccessPoint"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "create_access_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "name", "bucket", "vpc_configuration", "public_access_block_configuration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.S3ControlBackend.create_access_point", "name": "create_access_point", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "name", "bucket", "vpc_configuration", "public_access_block_configuration"], "arg_types": ["moto.s3control.models.S3ControlBackend", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_access_point of S3ControlBackend", "ret_type": "moto.s3control.models.AccessPoint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_access_point_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "account_id", "name", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.S3ControlBackend.create_access_point_policy", "name": "create_access_point_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "account_id", "name", "policy"], "arg_types": ["moto.s3control.models.S3ControlBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_access_point_policy of S3ControlBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_access_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.S3ControlBackend.delete_access_point", "name": "delete_access_point", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "name"], "arg_types": ["moto.s3control.models.S3ControlBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_access_point of S3ControlBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_access_point_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.S3ControlBackend.delete_access_point_policy", "name": "delete_access_point_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "name"], "arg_types": ["moto.s3control.models.S3ControlBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_access_point_policy of S3ControlBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_public_access_block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.S3ControlBackend.delete_public_access_block", "name": "delete_public_access_block", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "account_id"], "arg_types": ["moto.s3control.models.S3ControlBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_public_access_block of S3ControlBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_access_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.S3ControlBackend.get_access_point", "name": "get_access_point", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "name"], "arg_types": ["moto.s3control.models.S3ControlBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_access_point of S3ControlBackend", "ret_type": "moto.s3control.models.AccessPoint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_access_point_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.S3ControlBackend.get_access_point_policy", "name": "get_access_point_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "name"], "arg_types": ["moto.s3control.models.S3ControlBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_access_point_policy of S3ControlBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_access_point_policy_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.S3ControlBackend.get_access_point_policy_status", "name": "get_access_point_policy_status", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "name"], "arg_types": ["moto.s3control.models.S3ControlBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_access_point_policy_status of S3ControlBackend", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_public_access_block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.S3ControlBackend.get_public_access_block", "name": "get_public_access_block", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "account_id"], "arg_types": ["moto.s3control.models.S3ControlBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_public_access_block of S3ControlBackend", "ret_type": "moto.s3.models.PublicAccessBlock", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "public_access_block": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.s3control.models.S3ControlBackend.public_access_block", "name": "public_access_block", "type": {".class": "UnionType", "items": ["moto.s3.models.PublicAccessBlock", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "put_public_access_block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "pub_block_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3control.models.S3ControlBackend.put_public_access_block", "name": "put_public_access_block", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "pub_block_config"], "arg_types": ["moto.s3control.models.S3ControlBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put_public_access_block of S3ControlBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3control.models.S3ControlBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3control.models.S3ControlBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WrongPublicAccessBlockAccountIdError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.WrongPublicAccessBlockAccountIdError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3control.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3control.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3control.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3control.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3control.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3control.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "mock_random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}, "s3control_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.s3control.models.s3control_backends", "name": "s3control_backends", "type": {".class": "Instance", "args": ["moto.s3control.models.S3ControlBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/s3control/models.py"}