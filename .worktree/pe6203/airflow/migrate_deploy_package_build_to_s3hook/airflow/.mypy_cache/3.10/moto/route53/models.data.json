{".class": "MypyFile", "_fullname": "moto.route53.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "ChangeList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.route53.models.ChangeList", "name": "ChangeList", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.route53.models.ChangeList", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "moto.route53.models", "mro": ["moto.route53.models.ChangeList", "builtins.list", "typing.MutableSequence", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.ChangeList.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["moto.route53.models.ChangeList", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of ChangeList", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.ChangeList.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "item"], "arg_types": ["moto.route53.models.ChangeList", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of ChangeList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_insert_or_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_rr_set"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.ChangeList.has_insert_or_update", "name": "has_insert_or_update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_rr_set"], "arg_types": ["moto.route53.models.ChangeList", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_insert_or_update of ChangeList", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.route53.models.ChangeList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.route53.models.ChangeList", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CloudFormationModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.CloudFormationModel", "kind": "Gdef"}, "DelegationSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.route53.models.DelegationSet", "name": "DelegationSet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.route53.models.DelegationSet", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.route53.models", "mro": ["moto.route53.models.DelegationSet", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "caller_reference", "name_servers", "delegation_set_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.DelegationSet.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "caller_reference", "name_servers", "delegation_set_id"], "arg_types": ["moto.route53.models.DelegationSet", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DelegationSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "caller_reference": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.DelegationSet.caller_reference", "name": "caller_reference", "type": "builtins.str"}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.DelegationSet.id", "name": "id", "type": "builtins.str"}}, "location": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.DelegationSet.location", "name": "location", "type": "builtins.str"}}, "name_servers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.DelegationSet.name_servers", "name": "name_servers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.route53.models.DelegationSet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.route53.models.DelegationSet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DnsNameInvalidForZone": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.DnsNameInvalidForZone", "kind": "Gdef"}, "FakeZone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["delete_from_cloudformation_json", 1], ["has_cfn_attr", 1], ["is_created", 1], ["update_from_cloudformation_json", 1]], "alt_promote": null, "bases": ["moto.core.common_models.CloudFormationModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.route53.models.FakeZone", "name": "FakeZone", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "moto.route53.models.FakeZone", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.route53.models", "mro": ["moto.route53.models.FakeZone", "moto.core.common_models.CloudFormationModel", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "name", "id_", "private_zone", "caller_reference", "comment", "delegation_set"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.FakeZone.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1], "arg_names": ["self", "name", "id_", "private_zone", "caller_reference", "comment", "delegation_set"], "arg_types": ["moto.route53.models.FakeZone", "builtins.str", "builtins.str", "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["moto.route53.models.DelegationSet", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeZone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_rrset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record_set"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.FakeZone.add_rrset", "name": "add_rrset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record_set"], "arg_types": ["moto.route53.models.FakeZone", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_rrset of FakeZone", "ret_type": "moto.route53.models.RecordSet", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_vpc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "vpc_id", "vpc_region"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.FakeZone.add_vpc", "name": "add_vpc", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "vpc_id", "vpc_region"], "arg_types": ["moto.route53.models.FakeZone", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_vpc of FakeZone", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "caller_reference": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.FakeZone.caller_reference", "name": "caller_reference", "type": "builtins.str"}}, "cloudformation_name_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.route53.models.FakeZone.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of FakeZone", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.FakeZone.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of FakeZone", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cloudformation_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.route53.models.FakeZone.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of FakeZone", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.FakeZone.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of FakeZone", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.FakeZone.comment", "name": "comment", "type": "builtins.str"}}, "create_from_cloudformation_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "moto.route53.models.FakeZone.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.route53.models.FakeZone"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of FakeZone", "ret_type": "moto.route53.models.FakeZone", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.FakeZone.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.route53.models.FakeZone"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of FakeZone", "ret_type": "moto.route53.models.FakeZone", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delegation_set": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.FakeZone.delegation_set", "name": "delegation_set", "type": {".class": "UnionType", "items": ["moto.route53.models.DelegationSet", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "delete_rrset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rrset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.FakeZone.delete_rrset", "name": "delete_rrset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rrset"], "arg_types": ["moto.route53.models.FakeZone", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_rrset of FakeZone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_rrset_by_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "set_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.FakeZone.delete_rrset_by_id", "name": "delete_rrset_by_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "set_identifier"], "arg_types": ["moto.route53.models.FakeZone", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_rrset_by_id of FakeZone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_vpc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "vpc_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.FakeZone.delete_vpc", "name": "delete_vpc", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "vpc_id"], "arg_types": ["moto.route53.models.FakeZone", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_vpc of FakeZone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_record_sets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start_type", "start_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.FakeZone.get_record_sets", "name": "get_record_sets", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "start_type", "start_name"], "arg_types": ["moto.route53.models.FakeZone", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_record_sets of FakeZone", "ret_type": {".class": "Instance", "args": ["moto.route53.models.RecordSet"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.FakeZone.id", "name": "id", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.FakeZone.name", "name": "name", "type": "builtins.str"}}, "physical_resource_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.route53.models.FakeZone.physical_resource_id", "name": "physical_resource_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.FakeZone"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "physical_resource_id of FakeZone", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.route53.models.FakeZone.physical_resource_id", "name": "physical_resource_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.FakeZone"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "physical_resource_id of FakeZone", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "private_zone": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.FakeZone.private_zone", "name": "private_zone", "type": "builtins.bool"}}, "rr_changes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.FakeZone.rr_changes", "name": "rr_changes", "type": "moto.route53.models.ChangeList"}}, "rrsets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.route53.models.FakeZone.rrsets", "name": "rrsets", "type": {".class": "Instance", "args": ["moto.route53.models.RecordSet"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "upsert_rrset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record_set"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.FakeZone.upsert_rrset", "name": "upsert_rrset", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "record_set"], "arg_types": ["moto.route53.models.FakeZone", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upsert_rrset of FakeZone", "ret_type": "moto.route53.models.RecordSet", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vpcs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.route53.models.FakeZone.vpcs", "name": "vpcs", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.route53.models.FakeZone.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.route53.models.FakeZone", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HealthCheck": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["delete_from_cloudformation_json", 1], ["has_cfn_attr", 1], ["is_created", 1], ["update_from_cloudformation_json", 1]], "alt_promote": null, "bases": ["moto.core.common_models.CloudFormationModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.route53.models.HealthCheck", "name": "HealthCheck", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "moto.route53.models.HealthCheck", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.route53.models", "mro": ["moto.route53.models.HealthCheck", "moto.core.common_models.CloudFormationModel", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "health_check_id", "caller_reference", "health_check_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.HealthCheck.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "health_check_id", "caller_reference", "health_check_args"], "arg_types": ["moto.route53.models.HealthCheck", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HealthCheck", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "caller_reference": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.caller_reference", "name": "caller_reference", "type": "builtins.str"}}, "children": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.children", "name": "children", "type": {".class": "NoneType"}}}, "cloudformation_name_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.route53.models.HealthCheck.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of HealthCheck", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.HealthCheck.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of HealthCheck", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cloudformation_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.route53.models.HealthCheck.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of HealthCheck", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.HealthCheck.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of HealthCheck", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_from_cloudformation_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "moto.route53.models.HealthCheck.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.route53.models.HealthCheck"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of HealthCheck", "ret_type": "moto.route53.models.HealthCheck", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.HealthCheck.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.route53.models.HealthCheck"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of HealthCheck", "ret_type": "moto.route53.models.HealthCheck", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "disabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.disabled", "name": "disabled", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "uses_pep604_syntax": false}}}, "enable_sni": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.enable_sni", "name": "enable_sni", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "uses_pep604_syntax": false}}}, "failure_threshold": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.failure_threshold", "name": "failure_threshold", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "uses_pep604_syntax": false}}}, "fqdn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.fqdn", "name": "fqdn", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "health_threshold": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.health_threshold", "name": "health_threshold", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.id", "name": "id", "type": "builtins.str"}}, "inverted": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.inverted", "name": "inverted", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "uses_pep604_syntax": false}}}, "ip_address": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.ip_address", "name": "ip_address", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "measure_latency": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.measure_latency", "name": "measure_latency", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "uses_pep604_syntax": false}}}, "physical_resource_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.route53.models.HealthCheck.physical_resource_id", "name": "physical_resource_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.HealthCheck"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "physical_resource_id of HealthCheck", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.route53.models.HealthCheck.physical_resource_id", "name": "physical_resource_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.HealthCheck"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "physical_resource_id of HealthCheck", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "port": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.port", "name": "port", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "uses_pep604_syntax": false}}}, "regions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.regions", "name": "regions", "type": {".class": "NoneType"}}}, "request_interval": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.request_interval", "name": "request_interval", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "uses_pep604_syntax": false}}}, "resource_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.resource_path", "name": "resource_path", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "search_string": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.search_string", "name": "search_string", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "set_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "children"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.HealthCheck.set_children", "name": "set_children", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "children"], "arg_types": ["moto.route53.models.HealthCheck", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_children of HealthCheck", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_regions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "regions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.HealthCheck.set_regions", "name": "set_regions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "regions"], "arg_types": ["moto.route53.models.HealthCheck", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_regions of HealthCheck", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.HealthCheck.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.HealthCheck"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of HealthCheck", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.HealthCheck.type_", "name": "type_", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.route53.models.HealthCheck.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.route53.models.HealthCheck", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HostedZoneNotEmpty": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.HostedZoneNotEmpty", "kind": "Gdef"}, "InvalidActionValue": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.InvalidActionValue", "kind": "Gdef"}, "InvalidCloudWatchArn": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.InvalidCloudWatchArn", "kind": "Gdef"}, "InvalidInput": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.InvalidInput", "kind": "Gdef"}, "LastVPCAssociation": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.LastVPCAssociation", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NoSuchCloudWatchLogsLogGroup": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.NoSuchCloudWatchLogsLogGroup", "kind": "Gdef"}, "NoSuchDelegationSet": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.NoSuchDelegationSet", "kind": "Gdef"}, "NoSuchHealthCheck": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.NoSuchHealthCheck", "kind": "Gdef"}, "NoSuchHostedZone": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.NoSuchHostedZone", "kind": "Gdef"}, "NoSuchQueryLoggingConfig": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.NoSuchQueryLoggingConfig", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PAGINATION_MODEL": {".class": "SymbolTableNode", "cross_ref": "moto.route53.utils.PAGINATION_MODEL", "kind": "Gdef"}, "PublicZoneVPCAssociation": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.PublicZoneVPCAssociation", "kind": "Gdef"}, "QueryLoggingConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.route53.models.QueryLoggingConfig", "name": "QueryLoggingConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.route53.models.QueryLoggingConfig", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.route53.models", "mro": ["moto.route53.models.QueryLoggingConfig", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "query_logging_config_id", "hosted_zone_id", "cloudwatch_logs_log_group_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.QueryLoggingConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "query_logging_config_id", "hosted_zone_id", "cloudwatch_logs_log_group_arn"], "arg_types": ["moto.route53.models.QueryLoggingConfig", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QueryLoggingConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cloudwatch_logs_log_group_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.QueryLoggingConfig.cloudwatch_logs_log_group_arn", "name": "cloudwatch_logs_log_group_arn", "type": "builtins.str"}}, "hosted_zone_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.QueryLoggingConfig.hosted_zone_id", "name": "hosted_zone_id", "type": "builtins.str"}}, "location": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.QueryLoggingConfig.location", "name": "location", "type": "builtins.str"}}, "query_logging_config_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.QueryLoggingConfig.query_logging_config_id", "name": "query_logging_config_id", "type": "builtins.str"}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.QueryLoggingConfig.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.QueryLoggingConfig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of QueryLoggingConfig", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.route53.models.QueryLoggingConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.route53.models.QueryLoggingConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QueryLoggingConfigAlreadyExists": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.QueryLoggingConfigAlreadyExists", "kind": "Gdef"}, "ROUTE53_ID_CHOICE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.route53.models.ROUTE53_ID_CHOICE", "name": "ROUTE53_ID_CHOICE", "type": "builtins.str"}}, "RecordSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["has_cfn_attr", 1], ["is_created", 1]], "alt_promote": null, "bases": ["moto.core.common_models.CloudFormationModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.route53.models.RecordSet", "name": "RecordSet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "moto.route53.models.RecordSet", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.route53.models", "mro": ["moto.route53.models.RecordSet", "moto.core.common_models.CloudFormationModel", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.RecordSet.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "arg_types": ["moto.route53.models.RecordSet", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RecordSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "alias_target": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSet.alias_target", "name": "alias_target", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "cloudformation_name_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.route53.models.RecordSet.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of RecordSet", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.RecordSet.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of RecordSet", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cloudformation_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.route53.models.RecordSet.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of RecordSet", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.RecordSet.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of RecordSet", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_from_cloudformation_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "moto.route53.models.RecordSet.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.route53.models.RecordSet"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of RecordSet", "ret_type": "moto.route53.models.RecordSet", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.RecordSet.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.route53.models.RecordSet"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of RecordSet", "ret_type": "moto.route53.models.RecordSet", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "region"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.RecordSet.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "region"], "arg_types": ["moto.route53.models.RecordSet", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete of RecordSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_from_cloudformation_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "moto.route53.models.RecordSet.delete_from_cloudformation_json", "name": "delete_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name"], "arg_types": [{".class": "TypeType", "item": "moto.route53.models.RecordSet"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_from_cloudformation_json of RecordSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.RecordSet.delete_from_cloudformation_json", "name": "delete_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name"], "arg_types": [{".class": "TypeType", "item": "moto.route53.models.RecordSet"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_from_cloudformation_json of RecordSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "failover": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSet.failover", "name": "failover", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "geo_location": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSet.geo_location", "name": "geo_location", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "health_check": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSet.health_check", "name": "health_check", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "hosted_zone_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSet.hosted_zone_id", "name": "hosted_zone_id", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "hosted_zone_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSet.hosted_zone_name", "name": "hosted_zone_name", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSet.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "physical_resource_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.route53.models.RecordSet.physical_resource_id", "name": "physical_resource_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.RecordSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "physical_resource_id of RecordSet", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.route53.models.RecordSet.physical_resource_id", "name": "physical_resource_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.RecordSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "physical_resource_id of RecordSet", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "records": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSet.records", "name": "records", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "region": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSet.region", "name": "region", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "set_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSet.set_identifier", "name": "set_identifier", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSet.ttl", "name": "ttl", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "type_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSet.type_", "name": "type_", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "update_from_cloudformation_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["cls", "original_resource", "new_resource_name", "cloudformation_json", "account_id", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "moto.route53.models.RecordSet.update_from_cloudformation_json", "name": "update_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["cls", "original_resource", "new_resource_name", "cloudformation_json", "account_id", "region_name"], "arg_types": [{".class": "TypeType", "item": "moto.route53.models.RecordSet"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_from_cloudformation_json of RecordSet", "ret_type": "moto.route53.models.RecordSet", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.RecordSet.update_from_cloudformation_json", "name": "update_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["cls", "original_resource", "new_resource_name", "cloudformation_json", "account_id", "region_name"], "arg_types": [{".class": "TypeType", "item": "moto.route53.models.RecordSet"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_from_cloudformation_json of RecordSet", "ret_type": "moto.route53.models.RecordSet", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "weight": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSet.weight", "name": "weight", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.route53.models.RecordSet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.route53.models.RecordSet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RecordSetGroup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["delete_from_cloudformation_json", 1], ["has_cfn_attr", 1], ["is_created", 1], ["update_from_cloudformation_json", 1]], "alt_promote": null, "bases": ["moto.core.common_models.CloudFormationModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.route53.models.RecordSetGroup", "name": "RecordSetGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "moto.route53.models.RecordSetGroup", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.route53.models", "mro": ["moto.route53.models.RecordSetGroup", "moto.core.common_models.CloudFormationModel", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hosted_zone_id", "record_sets"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.RecordSetGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "hosted_zone_id", "record_sets"], "arg_types": ["moto.route53.models.RecordSetGroup", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RecordSetGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cloudformation_name_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.route53.models.RecordSetGroup.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of RecordSetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.RecordSetGroup.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of RecordSetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cloudformation_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.route53.models.RecordSetGroup.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of RecordSetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.RecordSetGroup.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of RecordSetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_from_cloudformation_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "moto.route53.models.RecordSetGroup.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.route53.models.RecordSetGroup"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of RecordSetGroup", "ret_type": "moto.route53.models.RecordSetGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.RecordSetGroup.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.route53.models.RecordSetGroup"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of RecordSetGroup", "ret_type": "moto.route53.models.RecordSetGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hosted_zone_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSetGroup.hosted_zone_id", "name": "hosted_zone_id", "type": "builtins.str"}}, "physical_resource_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.route53.models.RecordSetGroup.physical_resource_id", "name": "physical_resource_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.RecordSetGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "physical_resource_id of RecordSetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.route53.models.RecordSetGroup.physical_resource_id", "name": "physical_resource_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.RecordSetGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "physical_resource_id of RecordSetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "record_sets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53.models.RecordSetGroup.record_sets", "name": "record_sets", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.route53.models.RecordSetGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.route53.models.RecordSetGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceRecordAlreadyExists": {".class": "SymbolTableNode", "cross_ref": "moto.route53.exceptions.ResourceRecordAlreadyExists", "kind": "Gdef"}, "Route53Backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.route53.models.Route53Backend", "name": "Route53Backend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.route53.models", "mro": ["moto.route53.models.Route53Backend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Route53Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["region", "arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.route53.models.Route53Backend._validate_arn", "name": "_validate_arn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["region", "arn"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_arn of Route53Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.route53.models.Route53Backend._validate_arn", "name": "_validate_arn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["region", "arn"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_arn of Route53Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "associate_vpc_with_hosted_zone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "zone_id", "vpcid", "vpcregion"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.associate_vpc_with_hosted_zone", "name": "associate_vpc_with_hosted_zone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "zone_id", "vpcid", "vpcregion"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "associate_vpc_with_hosted_zone of Route53Backend", "ret_type": "moto.route53.models.FakeZone", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "change_resource_record_sets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "zoneid", "change_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.change_resource_record_sets", "name": "change_resource_record_sets", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "zoneid", "change_list"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "change_resource_record_sets of Route53Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "change_tags_for_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_id", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.change_tags_for_resource", "name": "change_tags_for_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_id", "tags"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "change_tags_for_resource of Route53Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_health_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "caller_reference", "health_check_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.create_health_check", "name": "create_health_check", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "caller_reference", "health_check_args"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_health_check of Route53Backend", "ret_type": "moto.route53.models.HealthCheck", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_hosted_zone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "private_zone", "caller_reference", "vpcid", "vpcregion", "comment", "delegation_set_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.create_hosted_zone", "name": "create_hosted_zone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "private_zone", "caller_reference", "vpcid", "vpcregion", "comment", "delegation_set_id"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_hosted_zone of Route53Backend", "ret_type": "moto.route53.models.FakeZone", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_query_logging_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "region", "hosted_zone_id", "log_group_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.create_query_logging_config", "name": "create_query_logging_config", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "region", "hosted_zone_id", "log_group_arn"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_query_logging_config of Route53Backend", "ret_type": "moto.route53.models.QueryLoggingConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_reusable_delegation_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "caller_reference", "delegation_set_id", "hosted_zone_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.create_reusable_delegation_set", "name": "create_reusable_delegation_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "caller_reference", "delegation_set_id", "hosted_zone_id"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_reusable_delegation_set of Route53Backend", "ret_type": "moto.route53.models.DelegationSet", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delegation_sets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.route53.models.Route53Backend.delegation_sets", "name": "delegation_sets", "type": {".class": "Instance", "args": ["builtins.str", "moto.route53.models.DelegationSet"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "delete_health_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "health_check_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.delete_health_check", "name": "delete_health_check", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "health_check_id"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_health_check of Route53Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_hosted_zone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "id_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.delete_hosted_zone", "name": "delete_hosted_zone", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "id_"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_hosted_zone of Route53Backend", "ret_type": {".class": "UnionType", "items": ["moto.route53.models.FakeZone", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_query_logging_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query_logging_config_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.delete_query_logging_config", "name": "delete_query_logging_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query_logging_config_id"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_query_logging_config of Route53Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_reusable_delegation_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delegation_set_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.delete_reusable_delegation_set", "name": "delete_reusable_delegation_set", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "delegation_set_id"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_reusable_delegation_set of Route53Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "disassociate_vpc_from_hosted_zone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "zone_id", "vpcid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.disassociate_vpc_from_hosted_zone", "name": "disassociate_vpc_from_hosted_zone", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "zone_id", "vpcid"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disassociate_vpc_from_hosted_zone of Route53Backend", "ret_type": "moto.route53.models.FakeZone", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_dnssec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "zone_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.get_dnssec", "name": "get_dnssec", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "zone_id"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_dnssec of Route53Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_health_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "health_check_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.get_health_check", "name": "get_health_check", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "health_check_id"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_health_check of Route53Backend", "ret_type": "moto.route53.models.HealthCheck", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_health_check_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.get_health_check_status", "name": "get_health_check_status", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.Route53Backend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_health_check_status of Route53Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_hosted_zone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "id_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.get_hosted_zone", "name": "get_hosted_zone", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "id_"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hosted_zone of Route53Backend", "ret_type": "moto.route53.models.FakeZone", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_hosted_zone_by_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.get_hosted_zone_by_name", "name": "get_hosted_zone_by_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hosted_zone_by_name of Route53Backend", "ret_type": {".class": "UnionType", "items": ["moto.route53.models.FakeZone", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_hosted_zone_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.get_hosted_zone_count", "name": "get_hosted_zone_count", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.Route53Backend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hosted_zone_count of Route53Backend", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_query_logging_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query_logging_config_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.get_query_logging_config", "name": "get_query_logging_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query_logging_config_id"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_query_logging_config of Route53Backend", "ret_type": "moto.route53.models.QueryLoggingConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_reusable_delegation_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delegation_set_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.get_reusable_delegation_set", "name": "get_reusable_delegation_set", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "delegation_set_id"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_reusable_delegation_set of Route53Backend", "ret_type": "moto.route53.models.DelegationSet", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "health_checks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.route53.models.Route53Backend.health_checks", "name": "health_checks", "type": {".class": "Instance", "args": ["builtins.str", "moto.route53.models.HealthCheck"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_health_checks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.list_health_checks", "name": "list_health_checks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.Route53Backend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_health_checks of Route53Backend", "ret_type": {".class": "Instance", "args": ["moto.route53.models.HealthCheck"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_hosted_zones": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.list_hosted_zones", "name": "list_hosted_zones", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.Route53Backend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_hosted_zones of Route53Backend", "ret_type": {".class": "Instance", "args": ["moto.route53.models.FakeZone"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_hosted_zones_by_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dnsnames"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.list_hosted_zones_by_name", "name": "list_hosted_zones_by_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dnsnames"], "arg_types": ["moto.route53.models.Route53Backend", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_hosted_zones_by_name of Route53Backend", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["moto.route53.models.FakeZone"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_hosted_zones_by_vpc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "vpc_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.list_hosted_zones_by_vpc", "name": "list_hosted_zones_by_vpc", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "vpc_id"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_hosted_zones_by_vpc of Route53Backend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_query_logging_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "hosted_zone_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.route53.models.Route53Backend.list_query_logging_configs", "name": "list_query_logging_configs", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "hosted_zone_id"], "arg_types": ["moto.route53.models.Route53Backend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_query_logging_configs of Route53Backend", "ret_type": {".class": "Instance", "args": ["moto.route53.models.QueryLoggingConfig"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.route53.models.Route53Backend.list_query_logging_configs", "name": "list_query_logging_configs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "list_resource_record_sets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "zone_id", "start_type", "start_name", "max_items"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.list_resource_record_sets", "name": "list_resource_record_sets", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "zone_id", "start_type", "start_name", "max_items"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str", "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_resource_record_sets of Route53Backend", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["moto.route53.models.RecordSet"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_reusable_delegation_sets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.list_reusable_delegation_sets", "name": "list_reusable_delegation_sets", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53.models.Route53Backend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_reusable_delegation_sets of Route53Backend", "ret_type": {".class": "Instance", "args": ["moto.route53.models.DelegationSet"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_tags_for_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.list_tags_for_resource", "name": "list_tags_for_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_id"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_tags_for_resource of Route53Backend", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "query_logging_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.route53.models.Route53Backend.query_logging_configs", "name": "query_logging_configs", "type": {".class": "Instance", "args": ["builtins.str", "moto.route53.models.QueryLoggingConfig"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "resource_tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.route53.models.Route53Backend.resource_tags", "name": "resource_tags", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "update_health_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "health_check_id", "health_check_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.update_health_check", "name": "update_health_check", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "health_check_id", "health_check_args"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_health_check of Route53Backend", "ret_type": "moto.route53.models.HealthCheck", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_hosted_zone_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "id_", "comment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.Route53Backend.update_hosted_zone_comment", "name": "update_hosted_zone_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "id_", "comment"], "arg_types": ["moto.route53.models.Route53Backend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_hosted_zone_comment of Route53Backend", "ret_type": "moto.route53.models.FakeZone", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "zones": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.route53.models.Route53Backend.zones", "name": "zones", "type": {".class": "Instance", "args": ["builtins.str", "moto.route53.models.FakeZone"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.route53.models.Route53Backend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.route53.models.Route53Backend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Template": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Template", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.route53.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.route53.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.route53.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.route53.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.route53.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.route53.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "create_route53_caller_reference": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.create_route53_caller_reference", "name": "create_route53_caller_reference", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_route53_caller_reference", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_route53_zone_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.create_route53_zone_id", "name": "create_route53_zone_id", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_route53_zone_id", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "paginate": {".class": "SymbolTableNode", "cross_ref": "moto.utilities.paginator.paginate", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "reverse_domain_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["domain_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53.models.reverse_domain_name", "name": "reverse_domain_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["domain_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse_domain_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "route53_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.route53.models.route53_backends", "name": "route53_backends", "type": {".class": "Instance", "args": ["moto.route53.models.Route53Backend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "string": {".class": "SymbolTableNode", "cross_ref": "string", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/route53/models.py"}