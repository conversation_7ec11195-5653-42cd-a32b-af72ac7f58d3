{".class": "MypyFile", "_fullname": "moto.iam.access_control", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef"}, "AWSRequest": {".class": "SymbolTableNode", "cross_ref": "botocore.awsrequest.AWSRequest", "kind": "Gdef"}, "AccessDeniedError": {".class": "SymbolTableNode", "cross_ref": "moto.core.exceptions.AccessDeniedError", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AssumedRoleAccessKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iam.access_control.AssumedRoleAccessKey", "name": "AssumedRoleAccessKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.AssumedRoleAccessKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.iam.access_control", "mro": ["moto.iam.access_control.AssumedRoleAccessKey", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "account_id", "access_key_id", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.AssumedRoleAccessKey.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "account_id", "access_key_id", "headers"], "arg_types": ["moto.iam.access_control.AssumedRoleAccessKey", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AssumedRoleAccessKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_access_key_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.AssumedRoleAccessKey._access_key_id", "name": "_access_key_id", "type": "builtins.str"}}, "_owner_role_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.AssumedRoleAccessKey._owner_role_name", "name": "_owner_role_name", "type": "builtins.str"}}, "_secret_access_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.AssumedRoleAccessKey._secret_access_key", "name": "_secret_access_key", "type": "builtins.str"}}, "_session_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.AssumedRoleAccessKey._session_name", "name": "_session_name", "type": "builtins.str"}}, "_session_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.AssumedRoleAccessKey._session_token", "name": "_session_token", "type": "builtins.str"}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.AssumedRoleAccessKey.account_id", "name": "account_id", "type": "builtins.str"}}, "arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.iam.access_control.AssumedRoleAccessKey.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.AssumedRoleAccessKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of AssumedRoleAccessKey", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.iam.access_control.AssumedRoleAccessKey.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.AssumedRoleAccessKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of AssumedRoleAccessKey", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.iam.access_control.AssumedRoleAccessKey.backend", "name": "backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.AssumedRoleAccessKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backend of AssumedRoleAccessKey", "ret_type": "moto.iam.models.IAMBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.iam.access_control.AssumedRoleAccessKey.backend", "name": "backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.AssumedRoleAccessKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backend of AssumedRoleAccessKey", "ret_type": "moto.iam.models.IAMBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "collect_policies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.AssumedRoleAccessKey.collect_policies", "name": "collect_policies", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.AssumedRoleAccessKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collect_policies of AssumedRoleAccessKey", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.AssumedRoleAccessKey.create_credentials", "name": "create_credentials", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.AssumedRoleAccessKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_credentials of AssumedRoleAccessKey", "ret_type": "botocore.credentials.Credentials", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iam.access_control.AssumedRoleAccessKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iam.access_control.AssumedRoleAccessKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AuthFailureError": {".class": "SymbolTableNode", "cross_ref": "moto.core.exceptions.AuthFailureError", "kind": "Gdef"}, "BucketAccessDeniedError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.BucketAccessDeniedError", "kind": "Gdef"}, "BucketInvalidAccessKeyIdError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.BucketInvalidAccessKeyIdError", "kind": "Gdef"}, "BucketInvalidTokenError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.BucketInvalidTokenError", "kind": "Gdef"}, "BucketSignatureDoesNotMatchError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.BucketSignatureDoesNotMatchError", "kind": "Gdef"}, "CreateAccessKeyFailure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iam.access_control.CreateAccessKeyFailure", "name": "CreateAccessKeyFailure", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.CreateAccessKeyFailure", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.iam.access_control", "mro": ["moto.iam.access_control.CreateAccessKeyFailure", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.CreateAccessKeyFailure.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "reason"], "arg_types": ["moto.iam.access_control.CreateAccessKeyFailure", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CreateAccessKeyFailure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reason": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.CreateAccessKeyFailure.reason", "name": "reason", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iam.access_control.CreateAccessKeyFailure.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iam.access_control.CreateAccessKeyFailure", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Credentials": {".class": "SymbolTableNode", "cross_ref": "botocore.credentials.Credentials", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "IAMBackend": {".class": "SymbolTableNode", "cross_ref": "moto.iam.models.IAMBackend", "kind": "Gdef"}, "IAMPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iam.access_control.IAMPolicy", "name": "IAMPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.iam.access_control", "mro": ["moto.iam.access_control.IAMPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "policy"], "arg_types": ["moto.iam.access_control.IAMPolicy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IAMPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_policy_json": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMPolicy._policy_json", "name": "_policy_json", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "is_action_permitted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "action", "resource"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMPolicy.is_action_permitted", "name": "is_action_permitted", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "action", "resource"], "arg_types": ["moto.iam.access_control.IAMPolicy", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_action_permitted of IAMPolicy", "ret_type": "moto.iam.access_control.PermissionResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iam.access_control.IAMPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iam.access_control.IAMPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IAMPolicyStatement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iam.access_control.IAMPolicyStatement", "name": "IAMPolicyStatement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMPolicyStatement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.iam.access_control", "mro": ["moto.iam.access_control.IAMPolicyStatement", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "statement"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMPolicyStatement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "statement"], "arg_types": ["moto.iam.access_control.IAMPolicyStatement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IAMPolicyStatement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_element_matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "statement_element", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMPolicyStatement._check_element_matches", "name": "_check_element_matches", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "statement_element", "value"], "arg_types": ["moto.iam.access_control.IAMPolicyStatement", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_element_matches of IAMPolicyStatement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["pattern", "string"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.iam.access_control.IAMPolicyStatement._match", "name": "_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["pattern", "string"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_match of IAMPolicyStatement", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.iam.access_control.IAMPolicyStatement._match", "name": "_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["pattern", "string"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_match of IAMPolicyStatement", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "re.Match"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_statement": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMPolicyStatement._statement", "name": "_statement", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "is_action_permitted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "action", "resource"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMPolicyStatement.is_action_permitted", "name": "is_action_permitted", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "action", "resource"], "arg_types": ["moto.iam.access_control.IAMPolicyStatement", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_action_permitted of IAMPolicyStatement", "ret_type": "moto.iam.access_control.PermissionResult", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_unknown_principal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "principal"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMPolicyStatement.is_unknown_principal", "name": "is_unknown_principal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "principal"], "arg_types": ["moto.iam.access_control.IAMPolicyStatement", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_unknown_principal of IAMPolicyStatement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iam.access_control.IAMPolicyStatement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iam.access_control.IAMPolicyStatement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IAMRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.iam.access_control.IAMRequestBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iam.access_control.IAMRequest", "name": "IAMRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMRequest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "moto.iam.access_control", "mro": ["moto.iam.access_control.IAMRequest", "moto.iam.access_control.IAMRequestBase", "builtins.object"], "names": {".class": "SymbolTable", "_create_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMRequest._create_auth", "name": "_create_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["moto.iam.access_control.IAMRequest", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_auth of IAMRequest", "ret_type": "botocore.auth.SigV4Auth", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_raise_access_denied": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMRequest._raise_access_denied", "name": "_raise_access_denied", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_access_denied of IAMRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_raise_invalid_access_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMRequest._raise_invalid_access_key", "name": "_raise_invalid_access_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_"], "arg_types": ["moto.iam.access_control.IAMRequest", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_invalid_access_key of IAMRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_raise_signature_does_not_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMRequest._raise_signature_does_not_match", "name": "_raise_signature_does_not_match", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_signature_does_not_match of IAMRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iam.access_control.IAMRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iam.access_control.IAMRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IAMRequestBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["_create_auth", 1], ["_raise_access_denied", 1], ["_raise_invalid_access_key", 1], ["_raise_signature_does_not_match", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "moto.iam.access_control.IAMRequestBase", "name": "IAMRequestBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "moto.iam.access_control.IAMRequestBase", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "moto.iam.access_control", "mro": ["moto.iam.access_control.IAMRequestBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "method", "path", "data", "body", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMRequestBase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "method", "path", "data", "body", "headers"], "arg_types": ["moto.iam.access_control.IAMRequestBase", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bytes", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IAMRequestBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_access_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._access_key", "name": "_access_key", "type": {".class": "UnionType", "items": ["moto.iam.access_control.IAMUserAccessKey", "moto.iam.access_control.AssumedRoleAccessKey"], "uses_pep604_syntax": false}}}, "_action": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._action", "name": "_action", "type": "builtins.str"}}, "_action_from_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMRequestBase._action_from_request", "name": "_action_from_request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMRequestBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_action_from_request of IAMRequestBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_body": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._body", "name": "_body", "type": "builtins.bytes"}}, "_calculate_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMRequestBase._calculate_signature", "name": "_calculate_signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMRequestBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_signature of IAMRequestBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "moto.iam.access_control.IAMRequestBase._create_auth", "name": "_create_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["moto.iam.access_control.IAMRequestBase", "botocore.credentials.Credentials"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_auth of IAMRequestBase", "ret_type": "botocore.auth.SigV4Auth", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._create_auth", "name": "_create_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["moto.iam.access_control.IAMRequestBase", "botocore.credentials.Credentials"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_auth of IAMRequestBase", "ret_type": "botocore.auth.SigV4Auth", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_create_aws_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMRequestBase._create_aws_request", "name": "_create_aws_request", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMRequestBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_aws_request of IAMRequestBase", "ret_type": "botocore.awsrequest.AWSRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_headers_for_aws_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["signed_headers", "original_headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.iam.access_control.IAMRequestBase._create_headers_for_aws_request", "name": "_create_headers_for_aws_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["signed_headers", "original_headers"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_headers_for_aws_request of IAMRequestBase", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._create_headers_for_aws_request", "name": "_create_headers_for_aws_request", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["signed_headers", "original_headers"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_headers_for_aws_request of IAMRequestBase", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._data", "name": "_data", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_get_string_between": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["first_separator", "second_separator", "string"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.iam.access_control.IAMRequestBase._get_string_between", "name": "_get_string_between", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["first_separator", "second_separator", "string"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_string_between of IAMRequestBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._get_string_between", "name": "_get_string_between", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["first_separator", "second_separator", "string"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_string_between of IAMRequestBase", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_headers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._headers", "name": "_headers", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._method", "name": "_method", "type": "builtins.str"}}, "_path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._path", "name": "_path", "type": "builtins.str"}}, "_raise_access_denied": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "moto.iam.access_control.IAMRequestBase._raise_access_denied", "name": "_raise_access_denied", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMRequestBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_access_denied of IAMRequestBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._raise_access_denied", "name": "_raise_access_denied", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMRequestBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_access_denied of IAMRequestBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_raise_invalid_access_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "moto.iam.access_control.IAMRequestBase._raise_invalid_access_key", "name": "_raise_invalid_access_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "reason"], "arg_types": ["moto.iam.access_control.IAMRequestBase", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_invalid_access_key of IAMRequestBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._raise_invalid_access_key", "name": "_raise_invalid_access_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "reason"], "arg_types": ["moto.iam.access_control.IAMRequestBase", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_invalid_access_key of IAMRequestBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_raise_signature_does_not_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "moto.iam.access_control.IAMRequestBase._raise_signature_does_not_match", "name": "_raise_signature_does_not_match", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMRequestBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_signature_does_not_match of IAMRequestBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._raise_signature_does_not_match", "name": "_raise_signature_does_not_match", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMRequestBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_signature_does_not_match of IAMRequestBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_region": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._region", "name": "_region", "type": "builtins.str"}}, "_service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase._service", "name": "_service", "type": "builtins.str"}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMRequestBase.account_id", "name": "account_id", "type": "builtins.str"}}, "check_action_permitted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMRequestBase.check_action_permitted", "name": "check_action_permitted", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource"], "arg_types": ["moto.iam.access_control.IAMRequestBase", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_action_permitted of IAMRequestBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMRequestBase.check_signature", "name": "check_signature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMRequestBase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_signature of IAMRequestBase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iam.access_control.IAMRequestBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iam.access_control.IAMRequestBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IAMUserAccessKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iam.access_control.IAMUserAccessKey", "name": "IAMUserAccessKey", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMUserAccessKey", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.iam.access_control", "mro": ["moto.iam.access_control.IAMUserAccessKey", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "account_id", "access_key_id", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMUserAccessKey.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "account_id", "access_key_id", "headers"], "arg_types": ["moto.iam.access_control.IAMUserAccessKey", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IAMUserAccessKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_access_key_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMUserAccessKey._access_key_id", "name": "_access_key_id", "type": "builtins.str"}}, "_owner_user_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMUserAccessKey._owner_user_name", "name": "_owner_user_name", "type": "builtins.str"}}, "_secret_access_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMUserAccessKey._secret_access_key", "name": "_secret_access_key", "type": "builtins.str"}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iam.access_control.IAMUserAccessKey.account_id", "name": "account_id", "type": "builtins.str"}}, "arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.iam.access_control.IAMUserAccessKey.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMUserAccessKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of IAMUserAccessKey", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.iam.access_control.IAMUserAccessKey.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMUserAccessKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of IAMUserAccessKey", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.iam.access_control.IAMUserAccessKey.backend", "name": "backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMUserAccessKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backend of IAMUserAccessKey", "ret_type": "moto.iam.models.IAMBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.iam.access_control.IAMUserAccessKey.backend", "name": "backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMUserAccessKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backend of IAMUserAccessKey", "ret_type": "moto.iam.models.IAMBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "collect_policies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMUserAccessKey.collect_policies", "name": "collect_policies", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMUserAccessKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collect_policies of IAMUserAccessKey", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.IAMUserAccessKey.create_credentials", "name": "create_credentials", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.IAMUserAccessKey"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_credentials of IAMUserAccessKey", "ret_type": "botocore.credentials.Credentials", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iam.access_control.IAMUserAccessKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iam.access_control.IAMUserAccessKey", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidClientTokenIdError": {".class": "SymbolTableNode", "cross_ref": "moto.core.exceptions.InvalidClientTokenIdError", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Match": {".class": "SymbolTableNode", "cross_ref": "re.Match", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PermissionResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iam.access_control.PermissionResult", "name": "PermissionResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "moto.iam.access_control.PermissionResult", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "moto.iam.access_control", "mro": ["moto.iam.access_control.PermissionResult", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "DENIED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.iam.access_control.PermissionResult.DENIED", "name": "DENIED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "NEUTRAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.iam.access_control.PermissionResult.NEUTRAL", "name": "NEUTRAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "PERMITTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.iam.access_control.PermissionResult.PERMITTED", "name": "PERMITTED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iam.access_control.PermissionResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iam.access_control.PermissionResult", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Policy": {".class": "SymbolTableNode", "cross_ref": "moto.iam.models.Policy", "kind": "Gdef"}, "S3AccessDeniedError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.S3AccessDeniedError", "kind": "Gdef"}, "S3IAMRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.iam.access_control.IAMRequestBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iam.access_control.S3IAMRequest", "name": "S3IAMRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.S3IAMRequest", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "moto.iam.access_control", "mro": ["moto.iam.access_control.S3IAMRequest", "moto.iam.access_control.IAMRequestBase", "builtins.object"], "names": {".class": "SymbolTable", "_create_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.S3IAMRequest._create_auth", "name": "_create_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "credentials"], "arg_types": ["moto.iam.access_control.S3IAMRequest", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_auth of S3IAMRequest", "ret_type": "botocore.auth.S3SigV4Auth", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_raise_access_denied": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.S3IAMRequest._raise_access_denied", "name": "_raise_access_denied", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.S3IAMRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_access_denied of S3IAMRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_raise_invalid_access_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.S3IAMRequest._raise_invalid_access_key", "name": "_raise_invalid_access_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "reason"], "arg_types": ["moto.iam.access_control.S3IAMRequest", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_invalid_access_key of S3IAMRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_raise_signature_does_not_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.S3IAMRequest._raise_signature_does_not_match", "name": "_raise_signature_does_not_match", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iam.access_control.S3IAMRequest"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_raise_signature_does_not_match of S3IAMRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iam.access_control.S3IAMRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iam.access_control.S3IAMRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3InvalidAccessKeyIdError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.S3InvalidAccessKeyIdError", "kind": "Gdef"}, "S3InvalidTokenError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.S3InvalidTokenError", "kind": "Gdef"}, "S3SigV4Auth": {".class": "SymbolTableNode", "cross_ref": "botocore.auth.S3SigV4Auth", "kind": "Gdef"}, "S3SignatureDoesNotMatchError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.S3SignatureDoesNotMatchError", "kind": "Gdef"}, "SigV4Auth": {".class": "SymbolTableNode", "cross_ref": "botocore.auth.SigV4Auth", "kind": "Gdef"}, "SignatureDoesNotMatchError": {".class": "SymbolTableNode", "cross_ref": "moto.core.exceptions.SignatureDoesNotMatchError", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iam.access_control.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iam.access_control.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iam.access_control.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iam.access_control.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iam.access_control.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iam.access_control.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "create_access_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["account_id", "access_key_id", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.access_control.create_access_key", "name": "create_access_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["account_id", "access_key_id", "headers"], "arg_types": ["builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_access_key", "ret_type": {".class": "UnionType", "items": ["moto.iam.access_control.IAMUserAccessKey", "moto.iam.access_control.AssumedRoleAccessKey"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iam_backends": {".class": "SymbolTableNode", "cross_ref": "moto.iam.models.iam_backends", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.iam.access_control.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sts_backends": {".class": "SymbolTableNode", "cross_ref": "moto.sts.models.sts_backends", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/iam/access_control.py"}