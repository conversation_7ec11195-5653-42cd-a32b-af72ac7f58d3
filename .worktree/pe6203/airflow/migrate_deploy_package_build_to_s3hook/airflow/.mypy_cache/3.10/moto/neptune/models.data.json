{".class": "MypyFile", "_fullname": "moto.neptune.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "DBCluster": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.neptune.models.DBCluster", "name": "DBCluster", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.neptune.models.DBCluster", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.neptune.models", "mro": ["moto.neptune.models.DBCluster", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "db_cluster_identifier", "database_name", "tags", "storage_encrypted", "parameter_group_name", "engine", "engine_version", "kms_key_id", "preferred_maintenance_window", "preferred_backup_window", "backup_retention_period", "port", "serverless_v2_scaling_configuration"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.DBCluster.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "db_cluster_identifier", "database_name", "tags", "storage_encrypted", "parameter_group_name", "engine", "engine_version", "kms_key_id", "preferred_maintenance_window", "preferred_backup_window", "backup_retention_period", "port", "serverless_v2_scaling_configuration"], "arg_types": ["moto.neptune.models.DBCluster", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DBCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.account_id", "name": "account_id", "type": "builtins.str"}}, "add_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.DBCluster.add_tags", "name": "add_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "arg_types": ["moto.neptune.models.DBCluster", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tags of DBCluster", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "availability_zones": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.availability_zones", "name": "availability_zones", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "backup_retention_period": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.backup_retention_period", "name": "backup_retention_period", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cluster_create_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.cluster_create_time", "name": "cluster_create_time", "type": "builtins.str"}}, "database_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.database_name", "name": "database_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "db_cluster_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.neptune.models.DBCluster.db_cluster_arn", "name": "db_cluster_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.neptune.models.DBCluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "db_cluster_arn of DBCluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.neptune.models.DBCluster.db_cluster_arn", "name": "db_cluster_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.neptune.models.DBCluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "db_cluster_arn of DBCluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "db_cluster_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.db_cluster_identifier", "name": "db_cluster_identifier", "type": "builtins.str"}}, "db_cluster_parameter_group_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.db_cluster_parameter_group_name", "name": "db_cluster_parameter_group_name", "type": "builtins.str"}}, "db_subnet_group": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.db_subnet_group", "name": "db_subnet_group", "type": "builtins.str"}}, "endpoint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.endpoint", "name": "endpoint", "type": "builtins.str"}}, "engine": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.engine", "name": "engine", "type": "builtins.str"}}, "engine_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.engine_version", "name": "engine_version", "type": "builtins.str"}}, "get_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.DBCluster.get_tags", "name": "get_tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.neptune.models.DBCluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tags of DBCluster", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hosted_zone_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.hosted_zone_id", "name": "hosted_zone_id", "type": "builtins.str"}}, "kms_key_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.kms_key_id", "name": "kms_key_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "port": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.port", "name": "port", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "preferred_backup_window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.preferred_backup_window", "name": "preferred_backup_window", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "preferred_maintenance_window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.preferred_maintenance_window", "name": "preferred_maintenance_window", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "reader_endpoint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.reader_endpoint", "name": "reader_endpoint", "type": "builtins.str"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.region_name", "name": "region_name", "type": "builtins.str"}}, "remove_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.DBCluster.remove_tags", "name": "remove_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "arg_types": ["moto.neptune.models.DBCluster", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_tags of DBCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resource_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.resource_id", "name": "resource_id", "type": "builtins.str"}}, "serverless_v2_scaling_configuration": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.serverless_v2_scaling_configuration", "name": "serverless_v2_scaling_configuration", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.status", "name": "status", "type": "builtins.str"}}, "storage_encrypted": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.storage_encrypted", "name": "storage_encrypted", "type": "builtins.bool"}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.tags", "name": "tags", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.DBCluster.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.neptune.models.DBCluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of DBCluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "url_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.DBCluster.url_identifier", "name": "url_identifier", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.neptune.models.DBCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.neptune.models.DBCluster", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBClusterNotFoundError": {".class": "SymbolTableNode", "cross_ref": "moto.neptune.exceptions.DBClusterNotFoundError", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GlobalCluster": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.neptune.models.GlobalCluster", "name": "GlobalCluster", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.neptune.models.GlobalCluster", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.neptune.models", "mro": ["moto.neptune.models.GlobalCluster", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "global_cluster_identifier", "engine", "engine_version", "storage_encrypted", "deletion_protection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.GlobalCluster.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "global_cluster_identifier", "engine", "engine_version", "storage_encrypted", "deletion_protection"], "arg_types": ["moto.neptune.models.GlobalCluster", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GlobalCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deletion_protection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.GlobalCluster.deletion_protection", "name": "deletion_protection", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": ""}, {".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": false}}}, "engine": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.GlobalCluster.engine", "name": "engine", "type": "builtins.str"}}, "engine_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.GlobalCluster.engine_version", "name": "engine_version", "type": "builtins.str"}}, "global_cluster_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.GlobalCluster.global_cluster_arn", "name": "global_cluster_arn", "type": "builtins.str"}}, "global_cluster_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.GlobalCluster.global_cluster_identifier", "name": "global_cluster_identifier", "type": "builtins.str"}}, "global_cluster_resource_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.GlobalCluster.global_cluster_resource_id", "name": "global_cluster_resource_id", "type": "builtins.str"}}, "storage_encrypted": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.neptune.models.GlobalCluster.storage_encrypted", "name": "storage_encrypted", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": ""}, {".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": false}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.GlobalCluster.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.neptune.models.GlobalCluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of GlobalCluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.neptune.models.GlobalCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.neptune.models.GlobalCluster", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NeptuneBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.neptune.models.NeptuneBackend", "name": "NeptuneBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.neptune.models.NeptuneBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.neptune.models", "mro": ["moto.neptune.models.NeptuneBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.NeptuneBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.neptune.models.NeptuneBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NeptuneBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_db_cluster_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.neptune.models.NeptuneBackend._db_cluster_options", "name": "_db_cluster_options", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "clusters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.neptune.models.NeptuneBackend.clusters", "name": "clusters", "type": {".class": "Instance", "args": ["builtins.str", "moto.neptune.models.DBCluster"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "create_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.NeptuneBackend.create_db_cluster", "name": "create_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["moto.neptune.models.NeptuneBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_cluster of NeptuneBackend", "ret_type": "moto.neptune.models.DBCluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_global_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "global_cluster_identifier", "engine", "engine_version", "storage_encrypted", "deletion_protection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.NeptuneBackend.create_global_cluster", "name": "create_global_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "global_cluster_identifier", "engine", "engine_version", "storage_encrypted", "deletion_protection"], "arg_types": ["moto.neptune.models.NeptuneBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_global_cluster of NeptuneBackend", "ret_type": "moto.neptune.models.GlobalCluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "db_cluster_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.neptune.models.NeptuneBackend.db_cluster_options", "name": "db_cluster_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.neptune.models.NeptuneBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "db_cluster_options of NeptuneBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.neptune.models.NeptuneBackend.db_cluster_options", "name": "db_cluster_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.neptune.models.NeptuneBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "db_cluster_options of NeptuneBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cluster_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.NeptuneBackend.delete_db_cluster", "name": "delete_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cluster_identifier"], "arg_types": ["moto.neptune.models.NeptuneBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_cluster of NeptuneBackend", "ret_type": "moto.neptune.models.DBCluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_global_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "global_cluster_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.NeptuneBackend.delete_global_cluster", "name": "delete_global_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "global_cluster_identifier"], "arg_types": ["moto.neptune.models.NeptuneBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_global_cluster of NeptuneBackend", "ret_type": "moto.neptune.models.GlobalCluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_db_clusters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_cluster_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.NeptuneBackend.describe_db_clusters", "name": "describe_db_clusters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_cluster_identifier"], "arg_types": ["moto.neptune.models.NeptuneBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_clusters of NeptuneBackend", "ret_type": {".class": "Instance", "args": ["moto.neptune.models.DBCluster"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_global_clusters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.NeptuneBackend.describe_global_clusters", "name": "describe_global_clusters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.neptune.models.NeptuneBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_global_clusters of NeptuneBackend", "ret_type": {".class": "Instance", "args": ["moto.neptune.models.GlobalCluster"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_orderable_db_instance_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "engine_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.NeptuneBackend.describe_orderable_db_instance_options", "name": "describe_orderable_db_instance_options", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "engine_version"], "arg_types": ["moto.neptune.models.NeptuneBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_orderable_db_instance_options of NeptuneBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "global_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.neptune.models.NeptuneBackend.global_backend", "name": "global_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.neptune.models.NeptuneBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "global_backend of NeptuneBackend", "ret_type": "moto.neptune.models.NeptuneBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.neptune.models.NeptuneBackend.global_backend", "name": "global_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.neptune.models.NeptuneBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "global_backend of NeptuneBackend", "ret_type": "moto.neptune.models.NeptuneBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "global_clusters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.neptune.models.NeptuneBackend.global_clusters", "name": "global_clusters", "type": {".class": "Instance", "args": ["builtins.str", "moto.neptune.models.GlobalCluster"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "modify_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.NeptuneBackend.modify_db_cluster", "name": "modify_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "arg_types": ["moto.neptune.models.NeptuneBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_cluster of NeptuneBackend", "ret_type": "moto.neptune.models.DBCluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cluster_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.neptune.models.NeptuneBackend.start_db_cluster", "name": "start_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cluster_identifier"], "arg_types": ["moto.neptune.models.NeptuneBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_db_cluster of NeptuneBackend", "ret_type": "moto.neptune.models.DBCluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.neptune.models.NeptuneBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.neptune.models.NeptuneBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Template": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Template", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.neptune.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.neptune.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.neptune.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.neptune.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.neptune.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.neptune.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "iso_8601_datetime_with_milliseconds": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.iso_8601_datetime_with_milliseconds", "kind": "Gdef"}, "load_resource": {".class": "SymbolTableNode", "cross_ref": "moto.utilities.utils.load_resource", "kind": "Gdef"}, "neptune_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.neptune.models.neptune_backends", "name": "neptune_backends", "type": {".class": "Instance", "args": ["moto.neptune.models.NeptuneBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}, "string": {".class": "SymbolTableNode", "cross_ref": "string", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/neptune/models.py"}