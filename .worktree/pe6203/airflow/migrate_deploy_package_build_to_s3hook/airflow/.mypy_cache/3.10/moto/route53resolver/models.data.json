{".class": "MypyFile", "_fullname": "moto.route53resolver.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "CAMEL_TO_SNAKE_PATTERN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.route53resolver.models.CAMEL_TO_SNAKE_PATTERN", "name": "CAMEL_TO_SNAKE_PATTERN", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "IPv4Address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.IPv4Address", "kind": "Gdef"}, "InvalidParameterException": {".class": "SymbolTableNode", "cross_ref": "moto.route53resolver.exceptions.InvalidParameterException", "kind": "Gdef"}, "InvalidRequestException": {".class": "SymbolTableNode", "cross_ref": "moto.route53resolver.exceptions.InvalidRequestException", "kind": "Gdef"}, "InvalidSecurityGroupNotFoundError": {".class": "SymbolTableNode", "cross_ref": "moto.ec2.exceptions.InvalidSecurityGroupNotFoundError", "kind": "Gdef"}, "InvalidSubnetIdError": {".class": "SymbolTableNode", "cross_ref": "moto.ec2.exceptions.InvalidSubnetIdError", "kind": "Gdef"}, "LimitExceededException": {".class": "SymbolTableNode", "cross_ref": "moto.route53resolver.exceptions.LimitExceededException", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PAGINATION_MODEL": {".class": "SymbolTableNode", "cross_ref": "moto.route53resolver.utils.PAGINATION_MODEL", "kind": "Gdef"}, "ResolverEndpoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.route53resolver.models.ResolverEndpoint", "name": "ResolverEndpoint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverEndpoint", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.route53resolver.models", "mro": ["moto.route53resolver.models.ResolverEndpoint", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "FILTER_NAMES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.route53resolver.models.ResolverEndpoint.FILTER_NAMES", "name": "FILTER_NAMES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "MAX_ENDPOINTS_PER_REGION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.route53resolver.models.ResolverEndpoint.MAX_ENDPOINTS_PER_REGION", "name": "MAX_ENDPOINTS_PER_REGION", "type": "builtins.int"}}, "MAX_TAGS_PER_RESOLVER_ENDPOINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.route53resolver.models.ResolverEndpoint.MAX_TAGS_PER_RESOLVER_ENDPOINT", "name": "MAX_TAGS_PER_RESOLVER_ENDPOINT", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "endpoint_id", "creator_request_id", "security_group_ids", "direction", "ip_addresses", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverEndpoint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "endpoint_id", "creator_request_id", "security_group_ids", "direction", "ip_addresses", "name"], "arg_types": ["moto.route53resolver.models.ResolverEndpoint", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResolverEndpoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_subnet_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverEndpoint._build_subnet_info", "name": "_build_subnet_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53resolver.models.ResolverEndpoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_subnet_info of ResolverEndpoint", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_vpc_id_from_subnet": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverEndpoint._vpc_id_from_subnet", "name": "_vpc_id_from_subnet", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53resolver.models.ResolverEndpoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_vpc_id_from_subnet of ResolverEndpoint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.account_id", "name": "account_id", "type": "builtins.str"}}, "arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.route53resolver.models.ResolverEndpoint.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53resolver.models.ResolverEndpoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of ResolverEndpoint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53resolver.models.ResolverEndpoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of ResolverEndpoint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "associate_ip_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverEndpoint.associate_ip_address", "name": "associate_ip_address", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["moto.route53resolver.models.ResolverEndpoint", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "associate_ip_address of ResolverEndpoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_eni": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverEndpoint.create_eni", "name": "create_eni", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53resolver.models.ResolverEndpoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_eni of ResolverEndpoint", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "creation_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.creation_time", "name": "creation_time", "type": "builtins.str"}}, "creator_request_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.creator_request_id", "name": "creator_request_id", "type": "builtins.str"}}, "delete_eni": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverEndpoint.delete_eni", "name": "delete_eni", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53resolver.models.ResolverEndpoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_eni of ResolverEndpoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverEndpoint.description", "name": "description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53resolver.models.ResolverEndpoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "description of ResolverEndpoint", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "direction": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.direction", "name": "direction", "type": "builtins.str"}}, "disassociate_ip_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverEndpoint.disassociate_ip_address", "name": "disassociate_ip_address", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["moto.route53resolver.models.ResolverEndpoint", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disassociate_ip_address of ResolverEndpoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ec2_backend": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.ec2_backend", "name": "ec2_backend", "type": "moto.ec2.models.EC2Backend"}}, "eni_ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.eni_ids", "name": "eni_ids", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "host_vpc_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.host_vpc_id", "name": "host_vpc_id", "type": "builtins.str"}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.id", "name": "id", "type": "builtins.str"}}, "ip_address_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.ip_address_count", "name": "ip_address_count", "type": "builtins.int"}}, "ip_addresses": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.ip_addresses", "name": "ip_addresses", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "ip_descriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverEndpoint.ip_descriptions", "name": "ip_descriptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53resolver.models.ResolverEndpoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ip_descriptions of ResolverEndpoint", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "modification_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.modification_time", "name": "modification_time", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.name", "name": "name", "type": "builtins.str"}}, "region": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.region", "name": "region", "type": "builtins.str"}}, "security_group_ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.security_group_ids", "name": "security_group_ids", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.status", "name": "status", "type": "builtins.str"}}, "status_message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.status_message", "name": "status_message", "type": "builtins.str"}}, "subnets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverEndpoint.subnets", "name": "subnets", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "update_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverEndpoint.update_name", "name": "update_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["moto.route53resolver.models.ResolverEndpoint", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_name of ResolverEndpoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.route53resolver.models.ResolverEndpoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.route53resolver.models.ResolverEndpoint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResolverRule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.route53resolver.models.ResolverRule", "name": "ResolverRule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverRule", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.route53resolver.models", "mro": ["moto.route53resolver.models.ResolverRule", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "FILTER_NAMES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.route53resolver.models.ResolverRule.FILTER_NAMES", "name": "FILTER_NAMES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "MAX_RULES_PER_REGION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.route53resolver.models.ResolverRule.MAX_RULES_PER_REGION", "name": "MAX_RULES_PER_REGION", "type": "builtins.int"}}, "MAX_TAGS_PER_RESOLVER_RULE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.route53resolver.models.ResolverRule.MAX_TAGS_PER_RESOLVER_RULE", "name": "MAX_TAGS_PER_RESOLVER_RULE", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "rule_id", "creator_request_id", "rule_type", "domain_name", "target_ips", "resolver_endpoint_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverRule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "rule_id", "creator_request_id", "rule_type", "domain_name", "target_ips", "resolver_endpoint_id", "name"], "arg_types": ["moto.route53resolver.models.ResolverRule", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResolverRule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.account_id", "name": "account_id", "type": "builtins.str"}}, "arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.route53resolver.models.ResolverRule.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53resolver.models.ResolverRule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of ResolverRule", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53resolver.models.ResolverRule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of ResolverRule", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "creation_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.creation_time", "name": "creation_time", "type": "builtins.str"}}, "creator_request_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.creator_request_id", "name": "creator_request_id", "type": "builtins.str"}}, "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverRule.description", "name": "description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53resolver.models.ResolverRule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "description of ResolverRule", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "domain_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.domain_name", "name": "domain_name", "type": "builtins.str"}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.id", "name": "id", "type": "builtins.str"}}, "modification_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.modification_time", "name": "modification_time", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.name", "name": "name", "type": "builtins.str"}}, "region": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.region", "name": "region", "type": "builtins.str"}}, "resolver_endpoint_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.resolver_endpoint_id", "name": "resolver_endpoint_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "rule_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.rule_id", "name": "rule_id", "type": "builtins.str"}}, "rule_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.rule_type", "name": "rule_type", "type": "builtins.str"}}, "share_status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.share_status", "name": "share_status", "type": "builtins.str"}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.status", "name": "status", "type": "builtins.str"}}, "status_message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.status_message", "name": "status_message", "type": "builtins.str"}}, "target_ips": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRule.target_ips", "name": "target_ips", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.route53resolver.models.ResolverRule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.route53resolver.models.ResolverRule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResolverRuleAssociation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.route53resolver.models.ResolverRuleAssociation", "name": "ResolverRuleAssociation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverRuleAssociation", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.route53resolver.models", "mro": ["moto.route53resolver.models.ResolverRuleAssociation", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "FILTER_NAMES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.route53resolver.models.ResolverRuleAssociation.FILTER_NAMES", "name": "FILTER_NAMES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "MAX_RULE_ASSOCIATIONS_PER_REGION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.route53resolver.models.ResolverRuleAssociation.MAX_RULE_ASSOCIATIONS_PER_REGION", "name": "MAX_RULE_ASSOCIATIONS_PER_REGION", "type": "builtins.int"}}, "MAX_TAGS_PER_RESOLVER_ENDPOINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.route53resolver.models.ResolverRuleAssociation.MAX_TAGS_PER_RESOLVER_ENDPOINT", "name": "MAX_TAGS_PER_RESOLVER_ENDPOINT", "type": "builtins.int"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "region", "resolver_rule_association_id", "resolver_rule_id", "vpc_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverRuleAssociation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "region", "resolver_rule_association_id", "resolver_rule_id", "vpc_id", "name"], "arg_types": ["moto.route53resolver.models.ResolverRuleAssociation", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResolverRuleAssociation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.ResolverRuleAssociation.description", "name": "description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.route53resolver.models.ResolverRuleAssociation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "description of ResolverRuleAssociation", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRuleAssociation.id", "name": "id", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRuleAssociation.name", "name": "name", "type": "builtins.str"}}, "region": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRuleAssociation.region", "name": "region", "type": "builtins.str"}}, "resolver_rule_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRuleAssociation.resolver_rule_id", "name": "resolver_rule_id", "type": "builtins.str"}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRuleAssociation.status", "name": "status", "type": "builtins.str"}}, "status_message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRuleAssociation.status_message", "name": "status_message", "type": "builtins.str"}}, "vpc_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.ResolverRuleAssociation.vpc_id", "name": "vpc_id", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.route53resolver.models.ResolverRuleAssociation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.route53resolver.models.ResolverRuleAssociation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceExistsException": {".class": "SymbolTableNode", "cross_ref": "moto.route53resolver.exceptions.ResourceExistsException", "kind": "Gdef"}, "ResourceInUseException": {".class": "SymbolTableNode", "cross_ref": "moto.route53resolver.exceptions.ResourceInUseException", "kind": "Gdef"}, "ResourceNotFoundException": {".class": "SymbolTableNode", "cross_ref": "moto.route53resolver.exceptions.ResourceNotFoundException", "kind": "Gdef"}, "Route53ResolverBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.route53resolver.models.Route53ResolverBackend", "name": "Route53ResolverBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.route53resolver.models", "mro": ["moto.route53resolver.models.Route53ResolverBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Route53ResolverBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_field_name_to_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.route53resolver.models.Route53ResolverBackend._add_field_name_to_filter", "name": "_add_field_name_to_filter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filters"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_field_name_to_filter of Route53ResolverBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend._add_field_name_to_filter", "name": "_add_field_name_to_filter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filters"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_field_name_to_filter of Route53ResolverBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_matched_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend._matched_arn", "name": "_matched_arn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_matched_arn of Route53ResolverBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_matches_all_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["entity", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.route53resolver.models.Route53ResolverBackend._matches_all_filters", "name": "_matches_all_filters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["entity", "filters"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_matches_all_filters of Route53ResolverBackend", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend._matches_all_filters", "name": "_matches_all_filters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["entity", "filters"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_matches_all_filters of Route53ResolverBackend", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_validate_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["filters", "allowed_filter_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.route53resolver.models.Route53ResolverBackend._validate_filters", "name": "_validate_filters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["filters", "allowed_filter_names"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_filters of Route53ResolverBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend._validate_filters", "name": "_validate_filters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["filters", "allowed_filter_names"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_filters of Route53ResolverBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_validate_resolver_endpoint_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resolver_endpoint_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend._validate_resolver_endpoint_id", "name": "_validate_resolver_endpoint_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resolver_endpoint_id"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_resolver_endpoint_id of Route53ResolverBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_resolver_rule_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resolver_rule_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend._validate_resolver_rule_id", "name": "_validate_resolver_rule_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resolver_rule_id"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_resolver_rule_id of Route53ResolverBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_verify_security_group_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "security_group_ids"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend._verify_security_group_ids", "name": "_verify_security_group_ids", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "security_group_ids"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_verify_security_group_ids of Route53ResolverBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_verify_subnet_ips": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "ip_addresses", "initial"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend._verify_subnet_ips", "name": "_verify_subnet_ips", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "ip_addresses", "initial"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_verify_subnet_ips of Route53ResolverBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "associate_resolver_endpoint_ip_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resolver_endpoint_id", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.associate_resolver_endpoint_ip_address", "name": "associate_resolver_endpoint_ip_address", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resolver_endpoint_id", "value"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "associate_resolver_endpoint_ip_address of Route53ResolverBackend", "ret_type": "moto.route53resolver.models.ResolverEndpoint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "associate_resolver_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "resolver_rule_id", "name", "vpc_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.associate_resolver_rule", "name": "associate_resolver_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "resolver_rule_id", "name", "vpc_id"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "associate_resolver_rule of Route53ResolverBackend", "ret_type": "moto.route53resolver.models.ResolverRuleAssociation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_resolver_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "region", "creator_request_id", "name", "security_group_ids", "direction", "ip_addresses", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.create_resolver_endpoint", "name": "create_resolver_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "region", "creator_request_id", "name", "security_group_ids", "direction", "ip_addresses", "tags"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_resolver_endpoint of Route53ResolverBackend", "ret_type": "moto.route53resolver.models.ResolverEndpoint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_resolver_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "region", "creator_request_id", "name", "rule_type", "domain_name", "target_ips", "resolver_endpoint_id", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.create_resolver_rule", "name": "create_resolver_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "region", "creator_request_id", "name", "rule_type", "domain_name", "target_ips", "resolver_endpoint_id", "tags"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_resolver_rule of Route53ResolverBackend", "ret_type": "moto.route53resolver.models.ResolverRule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_vpc_endpoint_service": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["service_region", "zones"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.default_vpc_endpoint_service", "name": "default_vpc_endpoint_service", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["service_region", "zones"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_vpc_endpoint_service of Route53ResolverBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.default_vpc_endpoint_service", "name": "default_vpc_endpoint_service", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["service_region", "zones"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_vpc_endpoint_service of Route53ResolverBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_resolver_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resolver_endpoint_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.delete_resolver_endpoint", "name": "delete_resolver_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resolver_endpoint_id"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_resolver_endpoint of Route53ResolverBackend", "ret_type": "moto.route53resolver.models.ResolverEndpoint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_resolver_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resolver_rule_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.delete_resolver_rule", "name": "delete_resolver_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resolver_rule_id"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_resolver_rule of Route53ResolverBackend", "ret_type": "moto.route53resolver.models.ResolverRule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "disassociate_resolver_endpoint_ip_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resolver_endpoint_id", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.disassociate_resolver_endpoint_ip_address", "name": "disassociate_resolver_endpoint_ip_address", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resolver_endpoint_id", "value"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disassociate_resolver_endpoint_ip_address of Route53ResolverBackend", "ret_type": "moto.route53resolver.models.ResolverEndpoint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "disassociate_resolver_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resolver_rule_id", "vpc_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.disassociate_resolver_rule", "name": "disassociate_resolver_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resolver_rule_id", "vpc_id"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disassociate_resolver_rule of Route53ResolverBackend", "ret_type": "moto.route53resolver.models.ResolverRuleAssociation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ec2_backend": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.ec2_backend", "name": "ec2_backend", "type": "moto.ec2.models.EC2Backend"}}, "get_resolver_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resolver_endpoint_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.get_resolver_endpoint", "name": "get_resolver_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resolver_endpoint_id"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resolver_endpoint of Route53ResolverBackend", "ret_type": "moto.route53resolver.models.ResolverEndpoint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resolver_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resolver_rule_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.get_resolver_rule", "name": "get_resolver_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resolver_rule_id"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resolver_rule of Route53ResolverBackend", "ret_type": "moto.route53resolver.models.ResolverRule", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resolver_rule_association": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resolver_rule_association_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.get_resolver_rule_association", "name": "get_resolver_rule_association", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resolver_rule_association_id"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resolver_rule_association of Route53ResolverBackend", "ret_type": "moto.route53resolver.models.ResolverRuleAssociation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_resolver_endpoint_ip_addresses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resolver_endpoint_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.list_resolver_endpoint_ip_addresses", "name": "list_resolver_endpoint_ip_addresses", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resolver_endpoint_id"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_resolver_endpoint_ip_addresses of Route53ResolverBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.list_resolver_endpoint_ip_addresses", "name": "list_resolver_endpoint_ip_addresses", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "list_resolver_endpoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.list_resolver_endpoints", "name": "list_resolver_endpoints", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "filters"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_resolver_endpoints of Route53ResolverBackend", "ret_type": {".class": "Instance", "args": ["moto.route53resolver.models.ResolverEndpoint"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.list_resolver_endpoints", "name": "list_resolver_endpoints", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "list_resolver_rule_associations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.list_resolver_rule_associations", "name": "list_resolver_rule_associations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "filters"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_resolver_rule_associations of Route53ResolverBackend", "ret_type": {".class": "Instance", "args": ["moto.route53resolver.models.ResolverRuleAssociation"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.list_resolver_rule_associations", "name": "list_resolver_rule_associations", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "list_resolver_rules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.list_resolver_rules", "name": "list_resolver_rules", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "filters"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_resolver_rules of Route53ResolverBackend", "ret_type": {".class": "Instance", "args": ["moto.route53resolver.models.ResolverRule"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.list_resolver_rules", "name": "list_resolver_rules", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "list_tags_for_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.list_tags_for_resource", "name": "list_tags_for_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_tags_for_resource of Route53ResolverBackend", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.list_tags_for_resource", "name": "list_tags_for_resource", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "resolver_endpoints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.resolver_endpoints", "name": "resolver_endpoints", "type": {".class": "Instance", "args": ["builtins.str", "moto.route53resolver.models.ResolverEndpoint"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "resolver_rule_associations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.resolver_rule_associations", "name": "resolver_rule_associations", "type": {".class": "Instance", "args": ["builtins.str", "moto.route53resolver.models.ResolverRuleAssociation"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "resolver_rules": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.resolver_rules", "name": "resolver_rules", "type": {".class": "Instance", "args": ["builtins.str", "moto.route53resolver.models.ResolverRule"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "tag_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arn", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.tag_resource", "name": "tag_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arn", "tags"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tag_resource of Route53ResolverBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tagger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.route53resolver.models.Route53ResolverBackend.tagger", "name": "tagger", "type": "moto.utilities.tagging_service.TaggingService"}}, "untag_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arn", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.untag_resource", "name": "untag_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arn", "tag_keys"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "untag_resource of Route53ResolverBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_resolver_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resolver_endpoint_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.route53resolver.models.Route53ResolverBackend.update_resolver_endpoint", "name": "update_resolver_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resolver_endpoint_id", "name"], "arg_types": ["moto.route53resolver.models.Route53ResolverBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_resolver_endpoint of Route53ResolverBackend", "ret_type": "moto.route53resolver.models.ResolverEndpoint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.route53resolver.models.Route53ResolverBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.route53resolver.models.Route53ResolverBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TagValidationException": {".class": "SymbolTableNode", "cross_ref": "moto.route53resolver.exceptions.TagValidationException", "kind": "Gdef"}, "TaggingService": {".class": "SymbolTableNode", "cross_ref": "moto.utilities.tagging_service.TaggingService", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.route53resolver.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.route53resolver.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.route53resolver.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.route53resolver.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.route53resolver.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.route53resolver.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "ec2_backends": {".class": "SymbolTableNode", "cross_ref": "moto.ec2.models.ec2_backends", "kind": "Gdef"}, "ip_address": {".class": "SymbolTableNode", "cross_ref": "ipaddress.ip_address", "kind": "Gdef"}, "ip_network": {".class": "SymbolTableNode", "cross_ref": "ipaddress.ip_network", "kind": "Gdef"}, "mock_random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}, "paginate": {".class": "SymbolTableNode", "cross_ref": "moto.utilities.paginator.paginate", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "route53resolver_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.route53resolver.models.route53resolver_backends", "name": "route53resolver_backends", "type": {".class": "Instance", "args": ["moto.route53resolver.models.Route53ResolverBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "timezone": {".class": "SymbolTableNode", "cross_ref": "datetime.timezone", "kind": "Gdef"}, "validate_args": {".class": "SymbolTableNode", "cross_ref": "moto.route53resolver.validations.validate_args", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/route53resolver/models.py"}