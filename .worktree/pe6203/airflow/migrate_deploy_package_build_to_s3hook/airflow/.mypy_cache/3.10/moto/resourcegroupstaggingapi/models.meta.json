{"data_mtime": 1756972668, "dep_lines": [3, 4, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 5, 7, 182, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["moto.acm.models", "moto.awslambda.models", "moto.core.exceptions", "moto.ecs.models", "moto.elb.models", "moto.elbv2.models", "moto.emr.models", "moto.glacier.models", "moto.glue.models", "moto.kinesis.models", "moto.kms.models", "moto.logs.models", "moto.moto_api._internal", "moto.rds.models", "moto.redshift.models", "moto.s3.models", "moto.sqs.models", "moto.utilities.tagging_service", "moto.core", "moto.ec2", "moto.cloudformation", "typing", "builtins", "os", "re", "sys", "string", "inspect", "traceback", "pprint", "collections", "types", "itertools", "warnings", "copy", "html", "_frozen_importlib", "abc", "moto.acm", "moto.awslambda", "moto.core.base_backend", "moto.core.common_models", "moto.ecs", "moto.elb", "moto.elbv2", "moto.emr", "moto.glacier", "moto.glue", "moto.kinesis", "moto.kms", "moto.logs", "moto.rds", "moto.redshift", "moto.s3", "moto.sqs"], "hash": "acd544b616709ed1ee7bde41a89b8f93b270a6a3", "id": "moto.resourcegroupstaggingapi.models", "ignore_all": true, "interface_hash": "d82a6149e467bd8af564fe4abeb0705853004a17", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/resourcegroupstaggingapi/models.py", "plugin_data": null, "size": 31180, "suppressed": [], "version_id": "1.15.0"}