{"data_mtime": 1756972668, "dep_lines": [14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 6, 4, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["moto.swf.models.activity_task", "moto.swf.models.activity_type", "moto.swf.models.decision_task", "moto.swf.models.domain", "moto.swf.models.generic_type", "moto.swf.models.history_event", "moto.swf.models.timeout", "moto.swf.models.timer", "moto.swf.models.workflow_execution", "moto.swf.models.workflow_type", "moto.swf.exceptions", "moto.core", "time", "typing", "builtins", "os", "re", "sys", "string", "inspect", "traceback", "pprint", "collections", "types", "itertools", "warnings", "copy", "html", "_frozen_importlib", "abc", "moto.core.base_backend", "moto.core.common_models", "moto.core.exceptions", "werkzeug", "werkzeug.exceptions"], "hash": "add14b372e715a232553d35700033e92af580a01", "id": "moto.swf.models", "ignore_all": true, "interface_hash": "b2b91e8f9b7ae98f63ac7d98b95f1a0f9e94cbdd", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/swf/models/__init__.py", "plugin_data": null, "size": 19879, "suppressed": [], "version_id": "1.15.0"}