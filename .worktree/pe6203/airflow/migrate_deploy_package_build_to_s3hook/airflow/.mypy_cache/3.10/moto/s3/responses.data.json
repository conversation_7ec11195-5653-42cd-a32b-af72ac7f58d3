{".class": "MypyFile", "_fullname": "moto.s3.responses", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACTION_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.ACTION_MAP", "name": "ACTION_MAP", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ARCHIVE_STORAGE_CLASSES": {".class": "SymbolTableNode", "cross_ref": "moto.s3.utils.ARCHIVE_STORAGE_CLASSES", "kind": "Gdef"}, "AccessForbidden": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.AccessForbidden", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseResponse": {".class": "SymbolTableNode", "cross_ref": "moto.core.responses.BaseResponse", "kind": "Gdef"}, "BucketAccessDeniedError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.BucketAccessDeniedError", "kind": "Gdef"}, "BucketAlreadyExists": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.BucketAlreadyExists", "kind": "Gdef"}, "BucketMustHaveLockeEnabled": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.BucketMustHaveLockeEnabled", "kind": "Gdef"}, "DEFAULT_REGION_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.DEFAULT_REGION_NAME", "name": "DEFAULT_REGION_NAME", "type": "builtins.str"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DuplicateTagKeys": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.DuplicateTagKeys", "kind": "Gdef"}, "FakeAcl": {".class": "SymbolTableNode", "cross_ref": "moto.s3.models.FakeAcl", "kind": "Gdef"}, "FakeBucket": {".class": "SymbolTableNode", "cross_ref": "moto.s3.models.FakeBucket", "kind": "Gdef"}, "FakeGrant": {".class": "SymbolTableNode", "cross_ref": "moto.s3.models.FakeGrant", "kind": "Gdef"}, "FakeGrantee": {".class": "SymbolTableNode", "cross_ref": "moto.s3.models.FakeGrantee", "kind": "Gdef"}, "FakeKey": {".class": "SymbolTableNode", "cross_ref": "moto.s3.models.FakeKey", "kind": "Gdef"}, "HeadOnDeleteMarker": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.HeadOnDeleteMarker", "kind": "Gdef"}, "IllegalLocationConstraintException": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.IllegalLocationConstraintException", "kind": "Gdef"}, "InvalidContentMD5": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.InvalidContentMD5", "kind": "Gdef"}, "InvalidContinuationToken": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.InvalidContinuationToken", "kind": "Gdef"}, "InvalidMaxPartArgument": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.InvalidMaxPartArgument", "kind": "Gdef"}, "InvalidMaxPartNumberArgument": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.InvalidMaxPartNumberArgument", "kind": "Gdef"}, "InvalidNotificationARN": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.InvalidNotificationARN", "kind": "Gdef"}, "InvalidNotificationEvent": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.InvalidNotificationEvent", "kind": "Gdef"}, "InvalidObjectState": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.InvalidObjectState", "kind": "Gdef"}, "InvalidPartOrder": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.InvalidPartOrder", "kind": "Gdef"}, "InvalidRange": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.InvalidRange", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LockNotEnabled": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.LockNotEnabled", "kind": "Gdef"}, "MalformedACLError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.MalformedACLError", "kind": "Gdef"}, "MalformedXML": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.MalformedXML", "kind": "Gdef"}, "MissingBucket": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.MissingBucket", "kind": "Gdef"}, "MissingKey": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "MissingVersion": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.MissingVersion", "kind": "Gdef"}, "NoSystemTags": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.NoSystemTags", "kind": "Gdef"}, "NotAnIntegerException": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.NotAnIntegerException", "kind": "Gdef"}, "ObjectNotInActiveTierError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.ObjectNotInActiveTierError", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PreconditionFailed": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.PreconditionFailed", "kind": "Gdef"}, "S3AclAndGrantError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.S3AclAndGrantError", "kind": "Gdef"}, "S3Backend": {".class": "SymbolTableNode", "cross_ref": "moto.s3.models.S3Backend", "kind": "Gdef"}, "S3ClientError": {".class": "SymbolTableNode", "cross_ref": "moto.s3.exceptions.S3ClientError", "kind": "Gdef"}, "S3Response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.responses.BaseResponse"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.responses.S3Response", "name": "S3Response", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.responses", "mro": ["moto.s3.responses.S3Response", "moto.core.responses.BaseResponse", "moto.core.responses._TemplateEnvironmentMixin", "moto.core.responses.ActionAuthenticatorMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3Response", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_accelerate_config_from_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._accelerate_config_from_body", "name": "_accelerate_config_from_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_accelerate_config_from_body of S3Response", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_acl_from_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._acl_from_body", "name": "_acl_from_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_acl_from_body of S3Response", "ret_type": {".class": "UnionType", "items": ["moto.s3.models.FakeAcl", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_acl_from_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._acl_from_headers", "name": "_acl_from_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["moto.s3.responses.S3Response", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_acl_from_headers of S3Response", "ret_type": {".class": "UnionType", "items": ["moto.s3.models.FakeAcl", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_body_contains_location_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._body_contains_location_constraint", "name": "_body_contains_location_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "body"], "arg_types": ["moto.s3.responses.S3Response", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_body_contains_location_constraint of S3Response", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bucket_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "full_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._bucket_response", "name": "_bucket_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "full_url"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bucket_response of S3Response", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bucket_response_delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "bucket_name", "querystring"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._bucket_response_delete", "name": "_bucket_response_delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "bucket_name", "querystring"], "arg_types": ["moto.s3.responses.S3Response", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bucket_response_delete of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bucket_response_delete_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "bucket_name", "authenticated"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._bucket_response_delete_keys", "name": "_bucket_response_delete_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "bucket_name", "authenticated"], "arg_types": ["moto.s3.responses.S3Response", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bucket_response_delete_keys of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bucket_response_get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "bucket_name", "querystring"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._bucket_response_get", "name": "_bucket_response_get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "bucket_name", "querystring"], "arg_types": ["moto.s3.responses.S3Response", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bucket_response_get of S3Response", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bucket_response_head": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "bucket_name", "querystring"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._bucket_response_head", "name": "_bucket_response_head", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "bucket_name", "querystring"], "arg_types": ["moto.s3.responses.S3Response", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bucket_response_head of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bucket_response_post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "bucket_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._bucket_response_post", "name": "_bucket_response_post", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "bucket_name"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bucket_response_post of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bucket_response_put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "request", "region_name", "bucket_name", "querystring"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._bucket_response_put", "name": "_bucket_response_put", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "request", "region_name", "bucket_name", "querystring"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bucket_response_put of S3Response", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bucket_tagging_from_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._bucket_tagging_from_body", "name": "_bucket_tagging_from_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bucket_tagging_from_body of S3Response", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_complete_multipart_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._complete_multipart_body", "name": "_complete_multipart_body", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "body"], "arg_types": ["moto.s3.responses.S3Response", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_complete_multipart_body of S3Response", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_cors_from_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._cors_from_body", "name": "_cors_from_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_cors_from_body of S3Response", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_bucket_configuration_is_empty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._create_bucket_configuration_is_empty", "name": "_create_bucket_configuration_is_empty", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "body"], "arg_types": ["moto.s3.responses.S3Response", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_bucket_configuration_is_empty of S3Response", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_encryption_config_from_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._encryption_config_from_body", "name": "_encryption_config_from_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_encryption_config_from_body of S3Response", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_cors_headers_other": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "headers", "bucket_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._get_cors_headers_other", "name": "_get_cors_headers_other", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "headers", "bucket_name"], "arg_types": ["moto.s3.responses.S3Response", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_cors_headers_other of S3Response", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_grants_from_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "grant_list", "exception_type", "permissions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._get_grants_from_xml", "name": "_get_grants_from_xml", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "grant_list", "exception_type", "permissions"], "arg_types": ["moto.s3.responses.S3Response", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeType", "item": "moto.s3.exceptions.S3ClientError"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_grants_from_xml of S3Response", "ret_type": {".class": "Instance", "args": ["moto.s3.models.FakeGrant"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.s3.responses.S3Response._get_path", "name": "_get_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_path of S3Response", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.s3.responses.S3Response._get_path", "name": "_get_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_path of S3Response", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_querystring": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "full_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._get_querystring", "name": "_get_querystring", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "full_url"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_querystring of S3Response", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_results_from_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result_keys", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._get_results_from_token", "name": "_get_results_from_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "result_keys", "token"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_results_from_token of S3Response", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_encoded_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "body", "content_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._handle_encoded_body", "name": "_handle_encoded_body", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "body", "content_length"], "arg_types": ["moto.s3.responses.S3Response", "builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_encoded_body of S3Response", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_list_objects_v2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "bucket_name", "querystring"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._handle_list_objects_v2", "name": "_handle_list_objects_v2", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "bucket_name", "querystring"], "arg_types": ["moto.s3.responses.S3Response", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_list_objects_v2 of S3Response", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_range_header": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "response_headers", "response_content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._handle_range_header", "name": "_handle_range_header", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "response_headers", "response_content"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_range_header of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_v4_chunk_signatures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "body", "content_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._handle_v4_chunk_signatures", "name": "_handle_v4_chunk_signatures", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "body", "content_length"], "arg_types": ["moto.s3.responses.S3Response", "builtins.bytes", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_v4_chunk_signatures of S3Response", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_invalid_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "url", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._invalid_headers", "name": "_invalid_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "url", "headers"], "arg_types": ["moto.s3.responses.S3Response", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_invalid_headers of S3Response", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_key_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "full_url", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._key_response", "name": "_key_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "full_url", "headers"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_key_response of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_key_response_delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "headers", "bucket_name", "query", "key_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._key_response_delete", "name": "_key_response_delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "headers", "bucket_name", "query", "key_name"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_key_response_delete of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_key_response_get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "bucket_name", "query", "key_name", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._key_response_get", "name": "_key_response_get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "bucket_name", "query", "key_name", "headers"], "arg_types": ["moto.s3.responses.S3Response", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_key_response_get of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_key_response_head": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "bucket_name", "query", "key_name", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._key_response_head", "name": "_key_response_head", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "bucket_name", "query", "key_name", "headers"], "arg_types": ["moto.s3.responses.S3Response", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_key_response_head of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_key_response_post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "request", "body", "bucket_name", "query", "key_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._key_response_post", "name": "_key_response_post", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "request", "body", "bucket_name", "query", "key_name"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_key_response_post of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_key_response_put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "request", "body", "bucket_name", "query", "key_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._key_response_put", "name": "_key_response_put", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "request", "body", "bucket_name", "query", "key_name"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bytes", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_key_response_put of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_legal_hold_status_from_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xml"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._legal_hold_status_from_xml", "name": "_legal_hold_status_from_xml", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xml"], "arg_types": ["moto.s3.responses.S3Response", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_legal_hold_status_from_xml of S3Response", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_lock_config_from_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._lock_config_from_body", "name": "_lock_config_from_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_lock_config_from_body of S3Response", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_logging_from_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._logging_from_body", "name": "_logging_from_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_logging_from_body of S3Response", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mode_until_from_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._mode_until_from_body", "name": "_mode_until_from_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mode_until_from_body of S3Response", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_notification_config_from_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._notification_config_from_body", "name": "_notification_config_from_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_notification_config_from_body of S3Response", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ownership_rule_from_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._ownership_rule_from_body", "name": "_ownership_rule_from_body", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ownership_rule_from_body of S3Response", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_pab_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._parse_pab_config", "name": "_parse_pab_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_pab_config of S3Response", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_replication_config_from_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xml"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._replication_config_from_xml", "name": "_replication_config_from_xml", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xml"], "arg_types": ["moto.s3.responses.S3Response", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replication_config_from_xml of S3Response", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_response_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "headers", "bucket_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._response_options", "name": "_response_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "headers", "bucket_name"], "arg_types": ["moto.s3.responses.S3Response", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_response_options of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_send_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.s3.responses.S3Response._send_response", "name": "_send_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["response"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_send_response of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.s3.responses.S3Response._send_response", "name": "_send_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["response"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_send_response of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_set_action": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "action_resource_type", "method", "querystring"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._set_action", "name": "_set_action", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "action_resource_type", "method", "querystring"], "arg_types": ["moto.s3.responses.S3Response", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_action of S3Response", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_cors_headers_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "headers", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._set_cors_headers_options", "name": "_set_cors_headers_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "headers", "bucket"], "arg_types": ["moto.s3.responses.S3Response", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "moto.s3.models.FakeBucket"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_cors_headers_options of S3Response", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_split_truncated_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["truncated_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.s3.responses.S3Response._split_truncated_keys", "name": "_split_truncated_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["truncated_keys"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_split_truncated_keys of S3Response", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.s3.responses.S3Response._split_truncated_keys", "name": "_split_truncated_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["truncated_keys"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_split_truncated_keys of S3Response", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_tagging_from_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._tagging_from_headers", "name": "_tagging_from_headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["moto.s3.responses.S3Response", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tagging_from_headers of S3Response", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tagging_from_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "xml"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._tagging_from_xml", "name": "_tagging_from_xml", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "xml"], "arg_types": ["moto.s3.responses.S3Response", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tagging_from_xml of S3Response", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_truncate_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result_keys", "max_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response._truncate_result", "name": "_truncate_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "result_keys", "max_keys"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_truncate_result of S3Response", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "all_buckets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response.all_buckets", "name": "all_buckets", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "all_buckets of S3Response", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ambiguous_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "full_url", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response.ambiguous_response", "name": "ambiguous_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "full_url", "headers"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ambiguous_response of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.s3.responses.S3Response.backend", "name": "backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backend of S3Response", "ret_type": "moto.s3.models.S3Backend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.s3.responses.S3Response.backend", "name": "backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backend of S3Response", "ret_type": "moto.s3.models.S3Backend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "bucket_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "full_url", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.s3.responses.S3Response.bucket_response", "name": "bucket_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "full_url", "headers"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bucket_response of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.s3.responses.S3Response.bucket_response", "name": "bucket_response", "type": "moto.utilities.aws_headers.GenericFunction"}}}, "get_safe_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response.get_safe_path", "name": "get_safe_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_safe_path of S3Response", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_access_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.s3.responses.S3Response.is_access_point", "name": "is_access_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_access_point of S3Response", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.s3.responses.S3Response.is_access_point", "name": "is_access_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_access_point of S3Response", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_delete_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response.is_delete_keys", "name": "is_delete_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_delete_keys of S3Response", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "full_url", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.s3.responses.S3Response.key_response", "name": "key_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "request", "full_url", "headers"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_response of S3Response", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "moto.core.common_types.TYPE_RESPONSE"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.s3.responses.S3Response.key_response", "name": "key_response", "type": "moto.utilities.aws_headers.GenericFunction"}}}, "parse_bucket_name_from_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response.parse_bucket_name_from_url", "name": "parse_bucket_name_from_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "url"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_bucket_name_from_url of S3Response", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_key_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response.parse_key_name", "name": "parse_key_name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "url"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_key_name of S3Response", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "should_autoescape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.s3.responses.S3Response.should_autoescape", "name": "should_autoescape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_autoescape of S3Response", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.s3.responses.S3Response.should_autoescape", "name": "should_autoescape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.responses.S3Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "should_autoescape of S3Response", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "subdomain_based_buckets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.S3Response.subdomain_based_buckets", "name": "subdomain_based_buckets", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["moto.s3.responses.S3Response", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subdomain_based_buckets of S3Response", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.responses.S3Response.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.responses.S3Response", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3ResponseInstance": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3ResponseInstance", "name": "S3ResponseInstance", "type": "moto.s3.responses.S3Response"}}, "S3_ALL_BUCKETS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_ALL_BUCKETS", "name": "S3_ALL_BUCKETS", "type": "builtins.str"}}, "S3_ALL_MULTIPARTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_ALL_MULTIPARTS", "name": "S3_ALL_MULTIPARTS", "type": "builtins.str"}}, "S3_BUCKET_ACCELERATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_BUCKET_ACCELERATE", "name": "S3_BUCKET_ACCELERATE", "type": "builtins.str"}}, "S3_BUCKET_ACCELERATE_NOT_SET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_BUCKET_ACCELERATE_NOT_SET", "name": "S3_BUCKET_ACCELERATE_NOT_SET", "type": "builtins.str"}}, "S3_BUCKET_CORS_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_BUCKET_CORS_RESPONSE", "name": "S3_BUCKET_CORS_RESPONSE", "type": "builtins.str"}}, "S3_BUCKET_CREATE_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_BUCKET_CREATE_RESPONSE", "name": "S3_BUCKET_CREATE_RESPONSE", "type": "builtins.str"}}, "S3_BUCKET_GET_OWNERSHIP_RULE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_BUCKET_GET_OWNERSHIP_RULE", "name": "S3_BUCKET_GET_OWNERSHIP_RULE", "type": "builtins.str"}}, "S3_BUCKET_GET_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_BUCKET_GET_RESPONSE", "name": "S3_BUCKET_GET_RESPONSE", "type": "builtins.str"}}, "S3_BUCKET_GET_RESPONSE_V2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_BUCKET_GET_RESPONSE_V2", "name": "S3_BUCKET_GET_RESPONSE_V2", "type": "builtins.str"}}, "S3_BUCKET_GET_VERSIONING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_BUCKET_GET_VERSIONING", "name": "S3_BUCKET_GET_VERSIONING", "type": "builtins.str"}}, "S3_BUCKET_GET_VERSIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_BUCKET_GET_VERSIONS", "name": "S3_BUCKET_GET_VERSIONS", "type": "builtins.str"}}, "S3_BUCKET_LIFECYCLE_CONFIGURATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_BUCKET_LIFECYCLE_CONFIGURATION", "name": "S3_BUCKET_LIFECYCLE_CONFIGURATION", "type": "builtins.str"}}, "S3_BUCKET_LOCATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_BUCKET_LOCATION", "name": "S3_BUCKET_LOCATION", "type": "builtins.str"}}, "S3_BUCKET_LOCK_CONFIGURATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_BUCKET_LOCK_CONFIGURATION", "name": "S3_BUCKET_LOCK_CONFIGURATION", "type": "builtins.str"}}, "S3_BUCKET_VERSIONING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_BUCKET_VERSIONING", "name": "S3_BUCKET_VERSIONING", "type": "builtins.str"}}, "S3_DELETE_BUCKET_SUCCESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_DELETE_BUCKET_SUCCESS", "name": "S3_DELETE_BUCKET_SUCCESS", "type": "builtins.str"}}, "S3_DELETE_BUCKET_WITH_ITEMS_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_DELETE_BUCKET_WITH_ITEMS_ERROR", "name": "S3_DELETE_BUCKET_WITH_ITEMS_ERROR", "type": "builtins.str"}}, "S3_DELETE_KEYS_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_DELETE_KEYS_RESPONSE", "name": "S3_DELETE_KEYS_RESPONSE", "type": "builtins.str"}}, "S3_DELETE_KEY_TAGGING_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_DELETE_KEY_TAGGING_RESPONSE", "name": "S3_DELETE_KEY_TAGGING_RESPONSE", "type": "builtins.str"}}, "S3_DUPLICATE_BUCKET_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_DUPLICATE_BUCKET_ERROR", "name": "S3_DUPLICATE_BUCKET_ERROR", "type": "builtins.str"}}, "S3_ENCRYPTION_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_ENCRYPTION_CONFIG", "name": "S3_ENCRYPTION_CONFIG", "type": "builtins.str"}}, "S3_ERROR_BUCKET_ONWERSHIP_NOT_FOUND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_ERROR_BUCKET_ONWERSHIP_NOT_FOUND", "name": "S3_ERROR_BUCKET_ONWERSHIP_NOT_FOUND", "type": "builtins.str"}}, "S3_GET_BUCKET_NOTIFICATION_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_GET_BUCKET_NOTIFICATION_CONFIG", "name": "S3_GET_BUCKET_NOTIFICATION_CONFIG", "type": "builtins.str"}}, "S3_INVALID_CORS_REQUEST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_INVALID_CORS_REQUEST", "name": "S3_INVALID_CORS_REQUEST", "type": "builtins.str"}}, "S3_INVALID_PRESIGNED_PARAMETERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_INVALID_PRESIGNED_PARAMETERS", "name": "S3_INVALID_PRESIGNED_PARAMETERS", "type": "builtins.str"}}, "S3_LOGGING_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_LOGGING_CONFIG", "name": "S3_LOGGING_CONFIG", "type": "builtins.str"}}, "S3_MULTIPART_COMPLETE_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_MULTIPART_COMPLETE_RESPONSE", "name": "S3_MULTIPART_COMPLETE_RESPONSE", "type": "builtins.str"}}, "S3_MULTIPART_INITIATE_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_MULTIPART_INITIATE_RESPONSE", "name": "S3_MULTIPART_INITIATE_RESPONSE", "type": "builtins.str"}}, "S3_MULTIPART_LIST_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_MULTIPART_LIST_RESPONSE", "name": "S3_MULTIPART_LIST_RESPONSE", "type": "builtins.str"}}, "S3_MULTIPART_UPLOAD_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_MULTIPART_UPLOAD_RESPONSE", "name": "S3_MULTIPART_UPLOAD_RESPONSE", "type": "builtins.str"}}, "S3_NO_BUCKET_TAGGING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_NO_BUCKET_TAGGING", "name": "S3_NO_BUCKET_TAGGING", "type": "builtins.str"}}, "S3_NO_BUCKET_WEBSITE_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_NO_BUCKET_WEBSITE_CONFIG", "name": "S3_NO_BUCKET_WEBSITE_CONFIG", "type": "builtins.str"}}, "S3_NO_CORS_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_NO_CORS_CONFIG", "name": "S3_NO_CORS_CONFIG", "type": "builtins.str"}}, "S3_NO_ENCRYPTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_NO_ENCRYPTION", "name": "S3_NO_ENCRYPTION", "type": "builtins.str"}}, "S3_NO_LIFECYCLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_NO_LIFECYCLE", "name": "S3_NO_LIFECYCLE", "type": "builtins.str"}}, "S3_NO_LOGGING_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_NO_LOGGING_CONFIG", "name": "S3_NO_LOGGING_CONFIG", "type": "builtins.str"}}, "S3_NO_POLICY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_NO_POLICY", "name": "S3_NO_POLICY", "type": "builtins.str"}}, "S3_NO_REPLICATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_NO_REPLICATION", "name": "S3_NO_REPLICATION", "type": "builtins.str"}}, "S3_NO_VERSIONING_ENABLED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_NO_VERSIONING_ENABLED", "name": "S3_NO_VERSIONING_ENABLED", "type": "builtins.str"}}, "S3_OBJECT_ACL_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_OBJECT_ACL_RESPONSE", "name": "S3_OBJECT_ACL_RESPONSE", "type": "builtins.str"}}, "S3_OBJECT_ATTRIBUTES_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_OBJECT_ATTRIBUTES_RESPONSE", "name": "S3_OBJECT_ATTRIBUTES_RESPONSE", "type": "builtins.str"}}, "S3_OBJECT_COPY_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_OBJECT_COPY_RESPONSE", "name": "S3_OBJECT_COPY_RESPONSE", "type": "builtins.str"}}, "S3_OBJECT_LEGAL_HOLD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_OBJECT_LEGAL_HOLD", "name": "S3_OBJECT_LEGAL_HOLD", "type": "builtins.str"}}, "S3_OBJECT_TAGGING_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_OBJECT_TAGGING_RESPONSE", "name": "S3_OBJECT_TAGGING_RESPONSE", "type": "builtins.str"}}, "S3_PUBLIC_ACCESS_BLOCK_CONFIGURATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_PUBLIC_ACCESS_BLOCK_CONFIGURATION", "name": "S3_PUBLIC_ACCESS_BLOCK_CONFIGURATION", "type": "builtins.str"}}, "S3_REPLICATION_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.responses.S3_REPLICATION_CONFIG", "name": "S3_REPLICATION_CONFIG", "type": "builtins.str"}}, "TYPE_RESPONSE": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_types.TYPE_RESPONSE", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3.responses.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3.responses.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3.responses.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3.responses.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3.responses.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3.responses.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "amzn_request_id": {".class": "SymbolTableNode", "cross_ref": "moto.utilities.aws_headers.amzn_request_id", "kind": "Gdef"}, "bucket_name_from_url": {".class": "SymbolTableNode", "cross_ref": "moto.s3.utils.bucket_name_from_url", "kind": "Gdef"}, "bucketpath_bucket_name_from_url": {".class": "SymbolTableNode", "cross_ref": "moto.s3bucket_path.utils.bucket_name_from_url", "kind": "Gdef"}, "bucketpath_parse_key_name": {".class": "SymbolTableNode", "cross_ref": "moto.s3bucket_path.utils.parse_key_name", "kind": "Gdef"}, "compute_checksum": {".class": "SymbolTableNode", "cross_ref": "moto.s3.utils.compute_checksum", "kind": "Gdef"}, "cors_matches_origin": {".class": "SymbolTableNode", "cross_ref": "moto.s3.utils.cors_matches_origin", "kind": "Gdef"}, "extract_region_from_aws_authorization": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.extract_region_from_aws_authorization", "kind": "Gdef"}, "get_canned_acl": {".class": "SymbolTableNode", "cross_ref": "moto.s3.models.get_canned_acl", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "metadata_from_headers": {".class": "SymbolTableNode", "cross_ref": "moto.s3.utils.metadata_from_headers", "kind": "Gdef"}, "minidom": {".class": "SymbolTableNode", "cross_ref": "xml.dom.minidom", "kind": "Gdef"}, "parse_key_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.responses.parse_key_name", "name": "parse_key_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pth"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_key_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_qs": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.parse_qs", "kind": "Gdef"}, "parse_region_from_url": {".class": "SymbolTableNode", "cross_ref": "moto.s3.utils.parse_region_from_url", "kind": "Gdef"}, "path_url": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.path_url", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "s3_backends": {".class": "SymbolTableNode", "cross_ref": "moto.s3.models.s3_backends", "kind": "Gdef"}, "serialize_select": {".class": "SymbolTableNode", "cross_ref": "moto.s3.select_object_content.serialize_select", "kind": "Gdef"}, "settings": {".class": "SymbolTableNode", "cross_ref": "moto.settings", "kind": "Gdef"}, "str_to_rfc_1123_datetime": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.str_to_rfc_1123_datetime", "kind": "Gdef"}, "unquote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote", "kind": "Gdef"}, "urlencode": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlencode", "kind": "Gdef"}, "urllib": {".class": "SymbolTableNode", "cross_ref": "urllib", "kind": "Gdef"}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}, "urlunparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlunparse", "kind": "Gdef"}, "xmltodict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "moto.s3.responses.xmltodict", "name": "xmltodict", "type": {".class": "AnyType", "missing_import_name": "moto.s3.responses.xmltodict", "source_any": null, "type_of_any": 3}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/s3/responses.py"}