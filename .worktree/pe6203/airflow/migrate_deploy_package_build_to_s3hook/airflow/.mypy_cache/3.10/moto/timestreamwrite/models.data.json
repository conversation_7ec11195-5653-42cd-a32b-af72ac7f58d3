{".class": "MypyFile", "_fullname": "moto.timestreamwrite.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ResourceNotFound": {".class": "SymbolTableNode", "cross_ref": "moto.timestreamwrite.exceptions.ResourceNotFound", "kind": "Gdef"}, "TaggingService": {".class": "SymbolTableNode", "cross_ref": "moto.utilities.tagging_service.TaggingService", "kind": "Gdef"}, "TimestreamDatabase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.timestreamwrite.models.TimestreamDatabase", "name": "TimestreamDatabase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamDatabase", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.timestreamwrite.models", "mro": ["moto.timestreamwrite.models.TimestreamDatabase", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "database_name", "kms_key_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "database_name", "kms_key_id"], "arg_types": ["moto.timestreamwrite.models.TimestreamDatabase", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimestreamDatabase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.account_id", "name": "account_id", "type": "builtins.str"}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.arn", "name": "arn", "type": "builtins.str"}}, "create_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "table_name", "retention_properties", "magnetic_store_write_properties", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.create_table", "name": "create_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "table_name", "retention_properties", "magnetic_store_write_properties", "schema"], "arg_types": ["moto.timestreamwrite.models.TimestreamDatabase", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_table of TimestreamDatabase", "ret_type": "moto.timestreamwrite.models.TimestreamTable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "created_on": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.created_on", "name": "created_on", "type": "builtins.float"}}, "delete_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.delete_table", "name": "delete_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "table_name"], "arg_types": ["moto.timestreamwrite.models.TimestreamDatabase", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_table of TimestreamDatabase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.describe_table", "name": "describe_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "table_name"], "arg_types": ["moto.timestreamwrite.models.TimestreamDatabase", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_table of TimestreamDatabase", "ret_type": "moto.timestreamwrite.models.TimestreamTable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.description", "name": "description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.timestreamwrite.models.TimestreamDatabase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "description of TimestreamDatabase", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kms_key_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.kms_key_id", "name": "kms_key_id", "type": "builtins.str"}}, "list_tables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.list_tables", "name": "list_tables", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.timestreamwrite.models.TimestreamDatabase"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_tables of TimestreamDatabase", "ret_type": {".class": "Instance", "args": ["moto.timestreamwrite.models.TimestreamTable"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.name", "name": "name", "type": "builtins.str"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.region_name", "name": "region_name", "type": "builtins.str"}}, "tables": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.tables", "name": "tables", "type": {".class": "Instance", "args": ["builtins.str", "moto.timestreamwrite.models.TimestreamTable"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kms_key_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kms_key_id"], "arg_types": ["moto.timestreamwrite.models.TimestreamDatabase", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of TimestreamDatabase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "table_name", "retention_properties", "magnetic_store_write_properties", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.update_table", "name": "update_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "table_name", "retention_properties", "magnetic_store_write_properties", "schema"], "arg_types": ["moto.timestreamwrite.models.TimestreamDatabase", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_table of TimestreamDatabase", "ret_type": "moto.timestreamwrite.models.TimestreamTable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "updated_on": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamDatabase.updated_on", "name": "updated_on", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.timestreamwrite.models.TimestreamDatabase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.timestreamwrite.models.TimestreamDatabase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimestreamTable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.timestreamwrite.models.TimestreamTable", "name": "TimestreamTable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamTable", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.timestreamwrite.models", "mro": ["moto.timestreamwrite.models.TimestreamTable", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "table_name", "db_name", "retention_properties", "magnetic_store_write_properties", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamTable.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "table_name", "db_name", "retention_properties", "magnetic_store_write_properties", "schema"], "arg_types": ["moto.timestreamwrite.models.TimestreamTable", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimestreamTable", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamTable.arn", "name": "arn", "type": "builtins.str"}}, "db_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamTable.db_name", "name": "db_name", "type": "builtins.str"}}, "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamTable.description", "name": "description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.timestreamwrite.models.TimestreamTable"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "description of TimestreamTable", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "magnetic_store_write_properties": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamTable.magnetic_store_write_properties", "name": "magnetic_store_write_properties", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamTable.name", "name": "name", "type": "builtins.str"}}, "records": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamTable.records", "name": "records", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamTable.region_name", "name": "region_name", "type": "builtins.str"}}, "retention_properties": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamTable.retention_properties", "name": "retention_properties", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamTable.schema", "name": "schema", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "retention_properties", "magnetic_store_write_properties", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamTable.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "retention_properties", "magnetic_store_write_properties", "schema"], "arg_types": ["moto.timestreamwrite.models.TimestreamTable", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of TimestreamTable", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_records": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "records"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamTable.write_records", "name": "write_records", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "records"], "arg_types": ["moto.timestreamwrite.models.TimestreamTable", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_records of TimestreamTable", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.timestreamwrite.models.TimestreamTable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.timestreamwrite.models.TimestreamTable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimestreamWriteBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend", "name": "TimestreamWriteBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.timestreamwrite.models", "mro": ["moto.timestreamwrite.models.TimestreamWriteBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TimestreamWriteBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_database": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "database_name", "kms_key_id", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.create_database", "name": "create_database", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "database_name", "kms_key_id", "tags"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_database of TimestreamWriteBackend", "ret_type": "moto.timestreamwrite.models.TimestreamDatabase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "database_name", "table_name", "retention_properties", "tags", "magnetic_store_write_properties", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.create_table", "name": "create_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "database_name", "table_name", "retention_properties", "tags", "magnetic_store_write_properties", "schema"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_table of TimestreamWriteBackend", "ret_type": "moto.timestreamwrite.models.TimestreamTable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "databases": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.databases", "name": "databases", "type": {".class": "Instance", "args": ["builtins.str", "moto.timestreamwrite.models.TimestreamDatabase"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "delete_database": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "database_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.delete_database", "name": "delete_database", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "database_name"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_database of TimestreamWriteBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "database_name", "table_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.delete_table", "name": "delete_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "database_name", "table_name"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_table of TimestreamWriteBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_database": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "database_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.describe_database", "name": "describe_database", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "database_name"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_database of TimestreamWriteBackend", "ret_type": "moto.timestreamwrite.models.TimestreamDatabase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_endpoints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.describe_endpoints", "name": "describe_endpoints", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_endpoints of TimestreamWriteBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "database_name", "table_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.describe_table", "name": "describe_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "database_name", "table_name"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_table of TimestreamWriteBackend", "ret_type": "moto.timestreamwrite.models.TimestreamTable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_databases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.list_databases", "name": "list_databases", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_databases of TimestreamWriteBackend", "ret_type": {".class": "Instance", "args": ["moto.timestreamwrite.models.TimestreamDatabase"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_tables": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "database_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.list_tables", "name": "list_tables", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "database_name"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_tables of TimestreamWriteBackend", "ret_type": {".class": "Instance", "args": ["moto.timestreamwrite.models.TimestreamTable"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_tags_for_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.list_tags_for_resource", "name": "list_tags_for_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_tags_for_resource of TimestreamWriteBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tag_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arn", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.tag_resource", "name": "tag_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arn", "tags"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tag_resource of TimestreamWriteBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tagging_service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.tagging_service", "name": "tagging_service", "type": "moto.utilities.tagging_service.TaggingService"}}, "untag_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arn", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.untag_resource", "name": "untag_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arn", "tag_keys"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "untag_resource of TimestreamWriteBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_database": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "database_name", "kms_key_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.update_database", "name": "update_database", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "database_name", "kms_key_id"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_database of TimestreamWriteBackend", "ret_type": "moto.timestreamwrite.models.TimestreamDatabase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "database_name", "table_name", "retention_properties", "magnetic_store_write_properties", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.update_table", "name": "update_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "database_name", "table_name", "retention_properties", "magnetic_store_write_properties", "schema"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_table of TimestreamWriteBackend", "ret_type": "moto.timestreamwrite.models.TimestreamTable", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_records": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "database_name", "table_name", "records"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.write_records", "name": "write_records", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "database_name", "table_name", "records"], "arg_types": ["moto.timestreamwrite.models.TimestreamWriteBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_records of TimestreamWriteBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.timestreamwrite.models.TimestreamWriteBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.timestreamwrite.models.TimestreamWriteBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.timestreamwrite.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.timestreamwrite.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.timestreamwrite.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.timestreamwrite.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.timestreamwrite.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.timestreamwrite.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "timestreamwrite_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.timestreamwrite.models.timestreamwrite_backends", "name": "timestreamwrite_backends", "type": {".class": "Instance", "args": ["moto.timestreamwrite.models.TimestreamWriteBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "unix_time": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.unix_time", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/timestreamwrite/models.py"}