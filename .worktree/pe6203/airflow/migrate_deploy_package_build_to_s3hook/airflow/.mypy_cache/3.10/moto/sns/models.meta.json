{"data_mtime": 1756972668, "dep_lines": [10, 14, 16, 17, 19, 32, 100, 9, 15, 283, 1, 2, 3, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7], "dep_prios": [5, 5, 5, 5, 5, 5, 20, 5, 5, 20, 10, 10, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["moto.core.utils", "moto.moto_api._internal", "moto.sqs.exceptions", "moto.utilities.arns", "moto.sns.exceptions", "moto.sns.utils", "moto.cloudformation.exceptions", "moto.core", "moto.sqs", "moto.awslambda", "contextlib", "json", "re", "collections", "typing", "builtins", "os", "sys", "string", "inspect", "traceback", "pprint", "types", "itertools", "warnings", "copy", "html", "_collections_abc", "_frozen_importlib", "_random", "_typeshed", "abc", "enum", "json.decoder", "json.encoder", "moto.core.base_backend", "moto.core.common_models", "moto.core.exceptions", "moto.moto_api", "moto.moto_api._internal.moto_random", "random", "typing_extensions", "uuid", "werkzeug", "werkzeug.exceptions"], "hash": "ae001a0f5a694f44041953754c11871d34ef22c1", "id": "moto.sns.models", "ignore_all": true, "interface_hash": "5740f6bf3283ded260ab669f111f58f48d382a5f", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/sns/models.py", "plugin_data": null, "size": 43968, "suppressed": ["requests"], "version_id": "1.15.0"}