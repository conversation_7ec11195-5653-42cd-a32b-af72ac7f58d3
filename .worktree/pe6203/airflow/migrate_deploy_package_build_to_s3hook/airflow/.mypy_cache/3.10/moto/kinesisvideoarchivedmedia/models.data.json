{".class": "MypyFile", "_fullname": "moto.kinesisvideoarchivedmedia.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "KinesisVideoArchivedMediaBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend", "name": "KinesisVideoArchivedMediaBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.kinesisvideoarchivedmedia.models", "mro": ["moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "_get_streaming_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "stream_name", "stream_arn", "api_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend._get_streaming_url", "name": "_get_streaming_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "stream_name", "stream_arn", "api_name"], "arg_types": ["moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_streaming_url of KinesisVideoArchivedMediaBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend.backend", "name": "backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backend of KinesisVideoArchivedMediaBackend", "ret_type": "moto.kinesisvideo.models.KinesisVideoBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend.backend", "name": "backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backend of KinesisVideoArchivedMediaBackend", "ret_type": "moto.kinesisvideo.models.KinesisVideoBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_clip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "stream_name", "stream_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend.get_clip", "name": "get_clip", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "stream_name", "stream_arn"], "arg_types": ["moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_clip of KinesisVideoArchivedMediaBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_dash_streaming_session_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "stream_name", "stream_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend.get_dash_streaming_session_url", "name": "get_dash_streaming_session_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "stream_name", "stream_arn"], "arg_types": ["moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_dash_streaming_session_url of KinesisVideoArchivedMediaBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_hls_streaming_session_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "stream_name", "stream_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend.get_hls_streaming_session_url", "name": "get_hls_streaming_session_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "stream_name", "stream_arn"], "arg_types": ["moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_hls_streaming_session_url of KinesisVideoArchivedMediaBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KinesisVideoBackend": {".class": "SymbolTableNode", "cross_ref": "moto.kinesisvideo.models.KinesisVideoBackend", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.kinesisvideoarchivedmedia.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.kinesisvideoarchivedmedia.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.kinesisvideoarchivedmedia.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.kinesisvideoarchivedmedia.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.kinesisvideoarchivedmedia.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.kinesisvideoarchivedmedia.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "kinesisvideo_backends": {".class": "SymbolTableNode", "cross_ref": "moto.kinesisvideo.models.kinesisvideo_backends", "kind": "Gdef"}, "kinesisvideoarchivedmedia_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.kinesisvideoarchivedmedia.models.kinesisvideoarchivedmedia_backends", "name": "kinesisvideoarchivedmedia_backends", "type": {".class": "Instance", "args": ["moto.kinesisvideoarchivedmedia.models.KinesisVideoArchivedMediaBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "random_session_token": {".class": "SymbolTableNode", "cross_ref": "moto.sts.utils.random_session_token", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/kinesisvideoarchivedmedia/models.py"}