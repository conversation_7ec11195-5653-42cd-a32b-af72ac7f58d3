{"data_mtime": 1756972668, "dep_lines": [1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30], "dependencies": ["moto.core.models", "moto.kinesisvideoarchivedmedia.models", "builtins", "os", "re", "sys", "string", "inspect", "traceback", "pprint", "collections", "types", "itertools", "warnings", "copy", "typing", "html", "_frozen_importlib", "abc", "moto.core", "moto.core.base_backend"], "hash": "5d10a81adb7fbfe5aeac0be526b7cf37d445bb6d", "id": "moto.kinesisvideoarchivedmedia", "ignore_all": true, "interface_hash": "5ab003887e7a412cd3ac956d8ea788976937e700", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/kinesisvideoarchivedmedia/__init__.py", "plugin_data": null, "size": 265, "suppressed": [], "version_id": "1.15.0"}