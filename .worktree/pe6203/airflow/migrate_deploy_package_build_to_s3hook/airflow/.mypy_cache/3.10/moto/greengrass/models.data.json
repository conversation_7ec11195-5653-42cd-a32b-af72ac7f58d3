{".class": "MypyFile", "_fullname": "moto.greengrass.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FakeAssociatedRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeAssociatedRole", "name": "FakeAssociatedRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeAssociatedRole", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeAssociatedRole", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "role_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeAssociatedRole.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "role_arn"], "arg_types": ["moto.greengrass.models.FakeAssociatedRole", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeAssociatedRole", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "associated_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeAssociatedRole.associated_at", "name": "associated_at", "type": "datetime.datetime"}}, "role_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeAssociatedRole.role_arn", "name": "role_arn", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "include_detail"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeAssociatedRole.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "include_detail"], "arg_types": ["moto.greengrass.models.FakeAssociatedRole", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeAssociatedRole", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeAssociatedRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeAssociatedRole", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeCoreDefinition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeCoreDefinition", "name": "FakeCoreDefinition", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeCoreDefinition", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeCoreDefinition", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeCoreDefinition.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "name"], "arg_types": ["moto.greengrass.models.FakeCoreDefinition", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeCoreDefinition", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeCoreDefinition.arn", "name": "arn", "type": "builtins.str"}}, "created_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeCoreDefinition.created_at_datetime", "name": "created_at_datetime", "type": "datetime.datetime"}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeCoreDefinition.id", "name": "id", "type": "builtins.str"}}, "latest_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeCoreDefinition.latest_version", "name": "latest_version", "type": "builtins.str"}}, "latest_version_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeCoreDefinition.latest_version_arn", "name": "latest_version_arn", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeCoreDefinition.name", "name": "name", "type": "builtins.str"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeCoreDefinition.region_name", "name": "region_name", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeCoreDefinition.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.FakeCoreDefinition"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeCoreDefinition", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeCoreDefinition.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeCoreDefinition", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeCoreDefinitionVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeCoreDefinitionVersion", "name": "FakeCoreDefinitionVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeCoreDefinitionVersion", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeCoreDefinitionVersion", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "core_definition_id", "definition"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeCoreDefinitionVersion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "core_definition_id", "definition"], "arg_types": ["moto.greengrass.models.FakeCoreDefinitionVersion", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeCoreDefinitionVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeCoreDefinitionVersion.arn", "name": "arn", "type": "builtins.str"}}, "core_definition_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeCoreDefinitionVersion.core_definition_id", "name": "core_definition_id", "type": "builtins.str"}}, "created_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeCoreDefinitionVersion.created_at_datetime", "name": "created_at_datetime", "type": "datetime.datetime"}}, "definition": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeCoreDefinitionVersion.definition", "name": "definition", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeCoreDefinitionVersion.region_name", "name": "region_name", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "include_detail"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeCoreDefinitionVersion.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "include_detail"], "arg_types": ["moto.greengrass.models.FakeCoreDefinitionVersion", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeCoreDefinitionVersion", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeCoreDefinitionVersion.version", "name": "version", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeCoreDefinitionVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeCoreDefinitionVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeDeployment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeDeployment", "name": "FakeDeployment", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeDeployment", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeDeployment", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "group_id", "group_arn", "deployment_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeDeployment.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "group_id", "group_arn", "deployment_type"], "arg_types": ["moto.greengrass.models.FakeDeployment", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeDeployment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeployment.arn", "name": "arn", "type": "builtins.str"}}, "created_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeployment.created_at_datetime", "name": "created_at_datetime", "type": "datetime.datetime"}}, "deployment_status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeployment.deployment_status", "name": "deployment_status", "type": "builtins.str"}}, "deployment_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeployment.deployment_type", "name": "deployment_type", "type": "builtins.str"}}, "group_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeployment.group_arn", "name": "group_arn", "type": "builtins.str"}}, "group_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeployment.group_id", "name": "group_id", "type": "builtins.str"}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeployment.id", "name": "id", "type": "builtins.str"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeployment.region_name", "name": "region_name", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "include_detail"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeDeployment.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "include_detail"], "arg_types": ["moto.greengrass.models.FakeDeployment", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeDeployment", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeployment.update_at_datetime", "name": "update_at_datetime", "type": "datetime.datetime"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeDeployment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeDeployment", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeDeploymentStatus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeDeploymentStatus", "name": "FakeDeploymentStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeDeploymentStatus", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeDeploymentStatus", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "deployment_type", "updated_at", "deployment_status"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeDeploymentStatus.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "deployment_type", "updated_at", "deployment_status"], "arg_types": ["moto.greengrass.models.FakeDeploymentStatus", "builtins.str", "datetime.datetime", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeDeploymentStatus", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deployment_status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeploymentStatus.deployment_status", "name": "deployment_status", "type": "builtins.str"}}, "deployment_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeploymentStatus.deployment_type", "name": "deployment_type", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeDeploymentStatus.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.FakeDeploymentStatus"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeDeploymentStatus", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeploymentStatus.update_at_datetime", "name": "update_at_datetime", "type": "datetime.datetime"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeDeploymentStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeDeploymentStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeDeviceDefinition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeDeviceDefinition", "name": "FakeDeviceDefinition", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeDeviceDefinition", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeDeviceDefinition", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "name", "initial_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeDeviceDefinition.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "name", "initial_version"], "arg_types": ["moto.greengrass.models.FakeDeviceDefinition", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeDeviceDefinition", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinition.arn", "name": "arn", "type": "builtins.str"}}, "created_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinition.created_at_datetime", "name": "created_at_datetime", "type": "datetime.datetime"}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinition.id", "name": "id", "type": "builtins.str"}}, "initial_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinition.initial_version", "name": "initial_version", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "latest_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinition.latest_version", "name": "latest_version", "type": "builtins.str"}}, "latest_version_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinition.latest_version_arn", "name": "latest_version_arn", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinition.name", "name": "name", "type": "builtins.str"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinition.region_name", "name": "region_name", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeDeviceDefinition.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.FakeDeviceDefinition"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeDeviceDefinition", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinition.update_at_datetime", "name": "update_at_datetime", "type": "datetime.datetime"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeDeviceDefinition.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeDeviceDefinition", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeDeviceDefinitionVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeDeviceDefinitionVersion", "name": "FakeDeviceDefinitionVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeDeviceDefinitionVersion", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeDeviceDefinitionVersion", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "device_definition_id", "devices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeDeviceDefinitionVersion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "device_definition_id", "devices"], "arg_types": ["moto.greengrass.models.FakeDeviceDefinitionVersion", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeDeviceDefinitionVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinitionVersion.arn", "name": "arn", "type": "builtins.str"}}, "created_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinitionVersion.created_at_datetime", "name": "created_at_datetime", "type": "datetime.datetime"}}, "device_definition_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinitionVersion.device_definition_id", "name": "device_definition_id", "type": "builtins.str"}}, "devices": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinitionVersion.devices", "name": "devices", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinitionVersion.region_name", "name": "region_name", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "include_detail"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeDeviceDefinitionVersion.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "include_detail"], "arg_types": ["moto.greengrass.models.FakeDeviceDefinitionVersion", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeDeviceDefinitionVersion", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeDeviceDefinitionVersion.version", "name": "version", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeDeviceDefinitionVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeDeviceDefinitionVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeFunctionDefinition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeFunctionDefinition", "name": "FakeFunctionDefinition", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeFunctionDefinition", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeFunctionDefinition", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "name", "initial_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeFunctionDefinition.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "name", "initial_version"], "arg_types": ["moto.greengrass.models.FakeFunctionDefinition", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeFunctionDefinition", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinition.arn", "name": "arn", "type": "builtins.str"}}, "created_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinition.created_at_datetime", "name": "created_at_datetime", "type": "datetime.datetime"}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinition.id", "name": "id", "type": "builtins.str"}}, "initial_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinition.initial_version", "name": "initial_version", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "latest_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinition.latest_version", "name": "latest_version", "type": "builtins.str"}}, "latest_version_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinition.latest_version_arn", "name": "latest_version_arn", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinition.name", "name": "name", "type": "builtins.str"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinition.region_name", "name": "region_name", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeFunctionDefinition.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.FakeFunctionDefinition"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeFunctionDefinition", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinition.update_at_datetime", "name": "update_at_datetime", "type": "datetime.datetime"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeFunctionDefinition.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeFunctionDefinition", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeFunctionDefinitionVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeFunctionDefinitionVersion", "name": "FakeFunctionDefinitionVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeFunctionDefinitionVersion", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeFunctionDefinitionVersion", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "function_definition_id", "functions", "default_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeFunctionDefinitionVersion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "function_definition_id", "functions", "default_config"], "arg_types": ["moto.greengrass.models.FakeFunctionDefinitionVersion", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeFunctionDefinitionVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinitionVersion.arn", "name": "arn", "type": "builtins.str"}}, "created_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinitionVersion.created_at_datetime", "name": "created_at_datetime", "type": "datetime.datetime"}}, "default_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinitionVersion.default_config", "name": "default_config", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "function_definition_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinitionVersion.function_definition_id", "name": "function_definition_id", "type": "builtins.str"}}, "functions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinitionVersion.functions", "name": "functions", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinitionVersion.region_name", "name": "region_name", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeFunctionDefinitionVersion.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.FakeFunctionDefinitionVersion"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeFunctionDefinitionVersion", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeFunctionDefinitionVersion.version", "name": "version", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeFunctionDefinitionVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeFunctionDefinitionVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeGroup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeGroup", "name": "FakeGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeGroup", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeGroup", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "name"], "arg_types": ["moto.greengrass.models.FakeGroup", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroup.arn", "name": "arn", "type": "builtins.str"}}, "created_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroup.created_at_datetime", "name": "created_at_datetime", "type": "datetime.datetime"}}, "group_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroup.group_id", "name": "group_id", "type": "builtins.str"}}, "last_updated_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroup.last_updated_datetime", "name": "last_updated_datetime", "type": "datetime.datetime"}}, "latest_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroup.latest_version", "name": "latest_version", "type": "builtins.str"}}, "latest_version_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroup.latest_version_arn", "name": "latest_version_arn", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroup.name", "name": "name", "type": "builtins.str"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroup.region_name", "name": "region_name", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeGroup.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.FakeGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeGroup", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeGroupVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeGroupVersion", "name": "FakeGroupVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeGroupVersion", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeGroupVersion", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "group_id", "core_definition_version_arn", "device_definition_version_arn", "function_definition_version_arn", "resource_definition_version_arn", "subscription_definition_version_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeGroupVersion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "group_id", "core_definition_version_arn", "device_definition_version_arn", "function_definition_version_arn", "resource_definition_version_arn", "subscription_definition_version_arn"], "arg_types": ["moto.greengrass.models.FakeGroupVersion", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeGroupVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroupVersion.arn", "name": "arn", "type": "builtins.str"}}, "core_definition_version_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroupVersion.core_definition_version_arn", "name": "core_definition_version_arn", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "created_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroupVersion.created_at_datetime", "name": "created_at_datetime", "type": "datetime.datetime"}}, "device_definition_version_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroupVersion.device_definition_version_arn", "name": "device_definition_version_arn", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "function_definition_version_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroupVersion.function_definition_version_arn", "name": "function_definition_version_arn", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "group_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroupVersion.group_id", "name": "group_id", "type": "builtins.str"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroupVersion.region_name", "name": "region_name", "type": "builtins.str"}}, "resource_definition_version_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroupVersion.resource_definition_version_arn", "name": "resource_definition_version_arn", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "subscription_definition_version_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroupVersion.subscription_definition_version_arn", "name": "subscription_definition_version_arn", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "include_detail"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeGroupVersion.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "include_detail"], "arg_types": ["moto.greengrass.models.FakeGroupVersion", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeGroupVersion", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeGroupVersion.version", "name": "version", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeGroupVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeGroupVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeResourceDefinition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeResourceDefinition", "name": "FakeResourceDefinition", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeResourceDefinition", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeResourceDefinition", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "name", "initial_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeResourceDefinition.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "name", "initial_version"], "arg_types": ["moto.greengrass.models.FakeResourceDefinition", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeResourceDefinition", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinition.arn", "name": "arn", "type": "builtins.str"}}, "created_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinition.created_at_datetime", "name": "created_at_datetime", "type": "datetime.datetime"}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinition.id", "name": "id", "type": "builtins.str"}}, "initial_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinition.initial_version", "name": "initial_version", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "latest_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinition.latest_version", "name": "latest_version", "type": "builtins.str"}}, "latest_version_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinition.latest_version_arn", "name": "latest_version_arn", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinition.name", "name": "name", "type": "builtins.str"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinition.region_name", "name": "region_name", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeResourceDefinition.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.FakeResourceDefinition"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeResourceDefinition", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinition.update_at_datetime", "name": "update_at_datetime", "type": "datetime.datetime"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeResourceDefinition.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeResourceDefinition", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeResourceDefinitionVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeResourceDefinitionVersion", "name": "FakeResourceDefinitionVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeResourceDefinitionVersion", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeResourceDefinitionVersion", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "resource_definition_id", "resources"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeResourceDefinitionVersion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "resource_definition_id", "resources"], "arg_types": ["moto.greengrass.models.FakeResourceDefinitionVersion", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeResourceDefinitionVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinitionVersion.arn", "name": "arn", "type": "builtins.str"}}, "created_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinitionVersion.created_at_datetime", "name": "created_at_datetime", "type": "datetime.datetime"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinitionVersion.region_name", "name": "region_name", "type": "builtins.str"}}, "resource_definition_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinitionVersion.resource_definition_id", "name": "resource_definition_id", "type": "builtins.str"}}, "resources": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinitionVersion.resources", "name": "resources", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeResourceDefinitionVersion.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.FakeResourceDefinitionVersion"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeResourceDefinitionVersion", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeResourceDefinitionVersion.version", "name": "version", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeResourceDefinitionVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeResourceDefinitionVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeSubscriptionDefinition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeSubscriptionDefinition", "name": "FakeSubscriptionDefinition", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeSubscriptionDefinition", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeSubscriptionDefinition", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "name", "initial_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeSubscriptionDefinition.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "name", "initial_version"], "arg_types": ["moto.greengrass.models.FakeSubscriptionDefinition", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeSubscriptionDefinition", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinition.arn", "name": "arn", "type": "builtins.str"}}, "created_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinition.created_at_datetime", "name": "created_at_datetime", "type": "datetime.datetime"}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinition.id", "name": "id", "type": "builtins.str"}}, "initial_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinition.initial_version", "name": "initial_version", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "latest_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinition.latest_version", "name": "latest_version", "type": "builtins.str"}}, "latest_version_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinition.latest_version_arn", "name": "latest_version_arn", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinition.name", "name": "name", "type": "builtins.str"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinition.region_name", "name": "region_name", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeSubscriptionDefinition.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.FakeSubscriptionDefinition"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeSubscriptionDefinition", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinition.update_at_datetime", "name": "update_at_datetime", "type": "datetime.datetime"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeSubscriptionDefinition.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeSubscriptionDefinition", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeSubscriptionDefinitionVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.FakeSubscriptionDefinitionVersion", "name": "FakeSubscriptionDefinitionVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeSubscriptionDefinitionVersion", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.FakeSubscriptionDefinitionVersion", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "subscription_definition_id", "subscriptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeSubscriptionDefinitionVersion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "subscription_definition_id", "subscriptions"], "arg_types": ["moto.greengrass.models.FakeSubscriptionDefinitionVersion", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeSubscriptionDefinitionVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinitionVersion.arn", "name": "arn", "type": "builtins.str"}}, "created_at_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinitionVersion.created_at_datetime", "name": "created_at_datetime", "type": "datetime.datetime"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinitionVersion.region_name", "name": "region_name", "type": "builtins.str"}}, "subscription_definition_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinitionVersion.subscription_definition_id", "name": "subscription_definition_id", "type": "builtins.str"}}, "subscriptions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinitionVersion.subscriptions", "name": "subscriptions", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.FakeSubscriptionDefinitionVersion.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.FakeSubscriptionDefinitionVersion"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeSubscriptionDefinitionVersion", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.greengrass.models.FakeSubscriptionDefinitionVersion.version", "name": "version", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.FakeSubscriptionDefinitionVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.FakeSubscriptionDefinitionVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GreengrassBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.greengrass.models.GreengrassBackend", "name": "GreengrassBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.greengrass.models", "mro": ["moto.greengrass.models.GreengrassBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_valid_subscription_target_or_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["target_or_source"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.greengrass.models.GreengrassBackend._is_valid_subscription_target_or_source", "name": "_is_valid_subscription_target_or_source", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["target_or_source"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_valid_subscription_target_or_source of GreengrassBackend", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend._is_valid_subscription_target_or_source", "name": "_is_valid_subscription_target_or_source", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["target_or_source"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_valid_subscription_target_or_source of GreengrassBackend", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_validate_group_version_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "core_definition_version_arn", "device_definition_version_arn", "function_definition_version_arn", "resource_definition_version_arn", "subscription_definition_version_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend._validate_group_version_definitions", "name": "_validate_group_version_definitions", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "core_definition_version_arn", "device_definition_version_arn", "function_definition_version_arn", "resource_definition_version_arn", "subscription_definition_version_arn"], "arg_types": ["moto.greengrass.models.GreengrassBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_group_version_definitions of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_resources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["resources"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.greengrass.models.GreengrassBackend._validate_resources", "name": "_validate_resources", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["resources"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_resources of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend._validate_resources", "name": "_validate_resources", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["resources"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_resources of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_validate_subscription_target_or_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["subscriptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.greengrass.models.GreengrassBackend._validate_subscription_target_or_source", "name": "_validate_subscription_target_or_source", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["subscriptions"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_subscription_target_or_source of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend._validate_subscription_target_or_source", "name": "_validate_subscription_target_or_source", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["subscriptions"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_subscription_target_or_source of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "associate_role_to_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "group_id", "role_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.associate_role_to_group", "name": "associate_role_to_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "group_id", "role_arn"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "associate_role_to_group of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeAssociatedRole", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "core_definition_versions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.core_definition_versions", "name": "core_definition_versions", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeCoreDefinitionVersion"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "core_definitions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.core_definitions", "name": "core_definitions", "type": {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeCoreDefinition"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "create_core_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "initial_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.create_core_definition", "name": "create_core_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "initial_version"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_core_definition of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeCoreDefinition", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_core_definition_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "core_definition_id", "cores"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.create_core_definition_version", "name": "create_core_definition_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "core_definition_id", "cores"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_core_definition_version of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeCoreDefinitionVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_deployment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "group_id", "group_version_id", "deployment_type", "deployment_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.create_deployment", "name": "create_deployment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "group_id", "group_version_id", "deployment_type", "deployment_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_deployment of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeDeployment", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_device_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "initial_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.create_device_definition", "name": "create_device_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "initial_version"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_device_definition of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeDeviceDefinition", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_device_definition_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "device_definition_id", "devices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.create_device_definition_version", "name": "create_device_definition_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "device_definition_id", "devices"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_device_definition_version of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeDeviceDefinitionVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_function_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "initial_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.create_function_definition", "name": "create_function_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "initial_version"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_function_definition of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeFunctionDefinition", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_function_definition_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "function_definition_id", "functions", "default_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.create_function_definition_version", "name": "create_function_definition_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "function_definition_id", "functions", "default_config"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_function_definition_version of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeFunctionDefinitionVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "initial_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.create_group", "name": "create_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "initial_version"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_group of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_group_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "group_id", "core_definition_version_arn", "device_definition_version_arn", "function_definition_version_arn", "resource_definition_version_arn", "subscription_definition_version_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.create_group_version", "name": "create_group_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "group_id", "core_definition_version_arn", "device_definition_version_arn", "function_definition_version_arn", "resource_definition_version_arn", "subscription_definition_version_arn"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_group_version of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeGroupVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_resource_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "initial_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.create_resource_definition", "name": "create_resource_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "initial_version"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_resource_definition of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeResourceDefinition", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_resource_definition_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_definition_id", "resources"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.create_resource_definition_version", "name": "create_resource_definition_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_definition_id", "resources"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_resource_definition_version of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeResourceDefinitionVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_subscription_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "initial_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.create_subscription_definition", "name": "create_subscription_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "initial_version"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_subscription_definition of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeSubscriptionDefinition", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_subscription_definition_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "subscription_definition_id", "subscriptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.create_subscription_definition_version", "name": "create_subscription_definition_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "subscription_definition_id", "subscriptions"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_subscription_definition_version of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeSubscriptionDefinitionVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_core_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "core_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.delete_core_definition", "name": "delete_core_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "core_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_core_definition of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_device_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "device_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.delete_device_definition", "name": "delete_device_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "device_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_device_definition of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_function_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "function_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.delete_function_definition", "name": "delete_function_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "function_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_function_definition of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "group_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.delete_group", "name": "delete_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "group_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_group of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_resource_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.delete_resource_definition", "name": "delete_resource_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_resource_definition of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_subscription_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "subscription_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.delete_subscription_definition", "name": "delete_subscription_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "subscription_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_subscription_definition of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deployments": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.deployments", "name": "deployments", "type": {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeDeployment"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "device_definition_versions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.device_definition_versions", "name": "device_definition_versions", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeDeviceDefinitionVersion"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "device_definitions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.device_definitions", "name": "device_definitions", "type": {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeDeviceDefinition"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "disassociate_role_from_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "group_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.disassociate_role_from_group", "name": "disassociate_role_from_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "group_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disassociate_role_from_group of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "function_definition_versions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.function_definition_versions", "name": "function_definition_versions", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeFunctionDefinitionVersion"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "function_definitions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.function_definitions", "name": "function_definitions", "type": {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeFunctionDefinition"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_associated_role": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "group_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_associated_role", "name": "get_associated_role", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "group_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_associated_role of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeAssociatedRole", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_core_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "core_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_core_definition", "name": "get_core_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "core_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_core_definition of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeCoreDefinition", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_core_definition_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "core_definition_id", "core_definition_version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_core_definition_version", "name": "get_core_definition_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "core_definition_id", "core_definition_version_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_core_definition_version of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeCoreDefinitionVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_deployment_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "group_id", "deployment_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_deployment_status", "name": "get_deployment_status", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "group_id", "deployment_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_deployment_status of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeDeploymentStatus", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_device_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "device_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_device_definition", "name": "get_device_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "device_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_device_definition of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeDeviceDefinition", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_device_definition_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "device_definition_id", "device_definition_version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_device_definition_version", "name": "get_device_definition_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "device_definition_id", "device_definition_version_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_device_definition_version of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeDeviceDefinitionVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_function_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "function_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_function_definition", "name": "get_function_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "function_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_function_definition of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeFunctionDefinition", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_function_definition_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "function_definition_id", "function_definition_version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_function_definition_version", "name": "get_function_definition_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "function_definition_id", "function_definition_version_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_function_definition_version of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeFunctionDefinitionVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "group_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_group", "name": "get_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "group_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_group of GreengrassBackend", "ret_type": {".class": "UnionType", "items": ["moto.greengrass.models.FakeGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_group_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "group_id", "group_version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_group_version", "name": "get_group_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "group_id", "group_version_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_group_version of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeGroupVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resource_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_resource_definition", "name": "get_resource_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_definition of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeResourceDefinition", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resource_definition_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_definition_id", "resource_definition_version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_resource_definition_version", "name": "get_resource_definition_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_definition_id", "resource_definition_version_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_definition_version of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeResourceDefinitionVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_subscription_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "subscription_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_subscription_definition", "name": "get_subscription_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "subscription_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_subscription_definition of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeSubscriptionDefinition", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_subscription_definition_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "subscription_definition_id", "subscription_definition_version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.get_subscription_definition_version", "name": "get_subscription_definition_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "subscription_definition_id", "subscription_definition_version_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_subscription_definition_version of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeSubscriptionDefinitionVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "group_role_associations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.group_role_associations", "name": "group_role_associations", "type": {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeAssociatedRole"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "group_versions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.group_versions", "name": "group_versions", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeGroupVersion"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.groups", "name": "groups", "type": {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeGroup"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_core_definition_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "core_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.list_core_definition_versions", "name": "list_core_definition_versions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "core_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_core_definition_versions of GreengrassBackend", "ret_type": {".class": "Instance", "args": ["moto.greengrass.models.FakeCoreDefinitionVersion"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_core_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.list_core_definitions", "name": "list_core_definitions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.GreengrassBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_core_definitions of GreengrassBackend", "ret_type": {".class": "Instance", "args": ["moto.greengrass.models.FakeCoreDefinition"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_deployments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "group_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.list_deployments", "name": "list_deployments", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "group_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_deployments of GreengrassBackend", "ret_type": {".class": "Instance", "args": ["moto.greengrass.models.FakeDeployment"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_device_definition_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "device_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.list_device_definition_versions", "name": "list_device_definition_versions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "device_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_device_definition_versions of GreengrassBackend", "ret_type": {".class": "Instance", "args": ["moto.greengrass.models.FakeDeviceDefinitionVersion"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_device_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.list_device_definitions", "name": "list_device_definitions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.GreengrassBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_device_definitions of GreengrassBackend", "ret_type": {".class": "Instance", "args": ["moto.greengrass.models.FakeDeviceDefinition"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_function_definition_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "function_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.list_function_definition_versions", "name": "list_function_definition_versions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "function_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_function_definition_versions of GreengrassBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeFunctionDefinitionVersion"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_function_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.list_function_definitions", "name": "list_function_definitions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.GreengrassBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_function_definitions of GreengrassBackend", "ret_type": {".class": "Instance", "args": ["moto.greengrass.models.FakeFunctionDefinition"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_group_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "group_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.list_group_versions", "name": "list_group_versions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "group_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_group_versions of GreengrassBackend", "ret_type": {".class": "Instance", "args": ["moto.greengrass.models.FakeGroupVersion"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.list_groups", "name": "list_groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.GreengrassBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_groups of GreengrassBackend", "ret_type": {".class": "Instance", "args": ["moto.greengrass.models.FakeGroup"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_resource_definition_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.list_resource_definition_versions", "name": "list_resource_definition_versions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_resource_definition_versions of GreengrassBackend", "ret_type": {".class": "Instance", "args": ["moto.greengrass.models.FakeResourceDefinitionVersion"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_resource_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.list_resource_definitions", "name": "list_resource_definitions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.GreengrassBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_resource_definitions of GreengrassBackend", "ret_type": {".class": "Instance", "args": ["moto.greengrass.models.FakeResourceDefinition"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_subscription_definition_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "subscription_definition_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.list_subscription_definition_versions", "name": "list_subscription_definition_versions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "subscription_definition_id"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_subscription_definition_versions of GreengrassBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeSubscriptionDefinitionVersion"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_subscription_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.list_subscription_definitions", "name": "list_subscription_definitions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.greengrass.models.GreengrassBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_subscription_definitions of GreengrassBackend", "ret_type": {".class": "Instance", "args": ["moto.greengrass.models.FakeSubscriptionDefinition"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset_deployments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "group_id", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.reset_deployments", "name": "reset_deployments", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "group_id", "force"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_deployments of GreengrassBackend", "ret_type": "moto.greengrass.models.FakeDeployment", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resource_definition_versions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.resource_definition_versions", "name": "resource_definition_versions", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeResourceDefinitionVersion"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "resource_definitions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.resource_definitions", "name": "resource_definitions", "type": {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeResourceDefinition"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "subscription_definition_versions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.subscription_definition_versions", "name": "subscription_definition_versions", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeSubscriptionDefinitionVersion"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "subscription_definitions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.greengrass.models.GreengrassBackend.subscription_definitions", "name": "subscription_definitions", "type": {".class": "Instance", "args": ["builtins.str", "moto.greengrass.models.FakeSubscriptionDefinition"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "update_core_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "core_definition_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.update_core_definition", "name": "update_core_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "core_definition_id", "name"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_core_definition of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_device_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "device_definition_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.update_device_definition", "name": "update_device_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "device_definition_id", "name"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_device_definition of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_function_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "function_definition_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.update_function_definition", "name": "update_function_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "function_definition_id", "name"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_function_definition of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "group_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.update_group", "name": "update_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "group_id", "name"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_group of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_resource_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_definition_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.update_resource_definition", "name": "update_resource_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_definition_id", "name"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_resource_definition of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_subscription_definition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "subscription_definition_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.greengrass.models.GreengrassBackend.update_subscription_definition", "name": "update_subscription_definition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "subscription_definition_id", "name"], "arg_types": ["moto.greengrass.models.GreengrassBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_subscription_definition of GreengrassBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.greengrass.models.GreengrassBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.greengrass.models.GreengrassBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GreengrassClientError": {".class": "SymbolTableNode", "cross_ref": "moto.greengrass.exceptions.GreengrassClientError", "kind": "Gdef"}, "IdNotFoundException": {".class": "SymbolTableNode", "cross_ref": "moto.greengrass.exceptions.IdNotFoundException", "kind": "Gdef"}, "InvalidContainerDefinitionException": {".class": "SymbolTableNode", "cross_ref": "moto.greengrass.exceptions.InvalidContainerDefinitionException", "kind": "Gdef"}, "InvalidInputException": {".class": "SymbolTableNode", "cross_ref": "moto.greengrass.exceptions.InvalidInputException", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MissingCoreException": {".class": "SymbolTableNode", "cross_ref": "moto.greengrass.exceptions.MissingCoreException", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "ResourceNotFoundException": {".class": "SymbolTableNode", "cross_ref": "moto.greengrass.exceptions.ResourceNotFoundException", "kind": "Gdef"}, "VersionNotFoundException": {".class": "SymbolTableNode", "cross_ref": "moto.greengrass.exceptions.VersionNotFoundException", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.greengrass.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.greengrass.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.greengrass.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.greengrass.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.greengrass.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.greengrass.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "greengrass_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.greengrass.models.greengrass_backends", "name": "greengrass_backends", "type": {".class": "Instance", "args": ["moto.greengrass.models.GreengrassBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "iso_8601_datetime_with_milliseconds": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.iso_8601_datetime_with_milliseconds", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "mock_random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "utcnow": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.utcnow", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/greengrass/models.py"}