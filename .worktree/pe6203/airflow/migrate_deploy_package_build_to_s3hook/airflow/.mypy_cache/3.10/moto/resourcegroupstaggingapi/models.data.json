{".class": "MypyFile", "_fullname": "moto.resourcegroupstaggingapi.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AWSCertificateManagerBackend": {".class": "SymbolTableNode", "cross_ref": "moto.acm.models.AWSCertificateManagerBackend", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EC2ContainerServiceBackend": {".class": "SymbolTableNode", "cross_ref": "moto.ecs.models.EC2ContainerServiceBackend", "kind": "Gdef"}, "ELBBackend": {".class": "SymbolTableNode", "cross_ref": "moto.elb.models.ELBBackend", "kind": "Gdef"}, "ELBv2Backend": {".class": "SymbolTableNode", "cross_ref": "moto.elbv2.models.ELBv2Backend", "kind": "Gdef"}, "ElasticMapReduceBackend": {".class": "SymbolTableNode", "cross_ref": "moto.emr.models.ElasticMapReduceBackend", "kind": "Gdef"}, "GlacierBackend": {".class": "SymbolTableNode", "cross_ref": "moto.glacier.models.GlacierBackend", "kind": "Gdef"}, "GlueBackend": {".class": "SymbolTableNode", "cross_ref": "moto.glue.models.GlueBackend", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "KinesisBackend": {".class": "SymbolTableNode", "cross_ref": "moto.kinesis.models.KinesisBackend", "kind": "Gdef"}, "KmsBackend": {".class": "SymbolTableNode", "cross_ref": "moto.kms.models.KmsBackend", "kind": "Gdef"}, "LambdaBackend": {".class": "SymbolTableNode", "cross_ref": "moto.awslambda.models.LambdaBackend", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LogsBackend": {".class": "SymbolTableNode", "cross_ref": "moto.logs.models.LogsBackend", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RDSBackend": {".class": "SymbolTableNode", "cross_ref": "moto.rds.models.RDSBackend", "kind": "Gdef"}, "RESTError": {".class": "SymbolTableNode", "cross_ref": "moto.core.exceptions.RESTError", "kind": "Gdef"}, "RedshiftBackend": {".class": "SymbolTableNode", "cross_ref": "moto.redshift.models.RedshiftBackend", "kind": "Gdef"}, "ResourceGroupsTaggingAPIBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend", "name": "ResourceGroupsTaggingAPIBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.resourcegroupstaggingapi.models", "mro": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResourceGroupsTaggingAPIBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_resources_generator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "tag_filters", "resource_type_filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend._get_resources_generator", "name": "_get_resources_generator", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "tag_filters", "resource_type_filters"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_resources_generator of ResourceGroupsTaggingAPIBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_tag_keys_generator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend._get_tag_keys_generator", "name": "_get_tag_keys_generator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_tag_keys_generator of ResourceGroupsTaggingAPIBackend", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_tag_values_generator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend._get_tag_values_generator", "name": "_get_tag_values_generator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag_key"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_tag_values_generator of ResourceGroupsTaggingAPIBackend", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_pages": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend._pages", "name": "_pages", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "acm_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.acm_backend", "name": "acm_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "acm_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.acm.models.AWSCertificateManagerBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.acm_backend", "name": "acm_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "acm_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.acm.models.AWSCertificateManagerBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ec2_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.ec2_backend", "name": "ec2_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ec2_backend of ResourceGroupsTaggingAPIBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.ec2_backend", "name": "ec2_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ec2_backend of ResourceGroupsTaggingAPIBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "ecs_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.ecs_backend", "name": "ecs_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ecs_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.ecs.models.EC2ContainerServiceBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.ecs_backend", "name": "ecs_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ecs_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.ecs.models.EC2ContainerServiceBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "elb_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.elb_backend", "name": "elb_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "elb_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.elb.models.ELBBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.elb_backend", "name": "elb_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "elb_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.elb.models.ELBBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "elbv2_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.elbv2_backend", "name": "elbv2_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "elbv2_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.elbv2.models.ELBv2Backend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.elbv2_backend", "name": "elbv2_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "elbv2_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.elbv2.models.ELBv2Backend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "emr_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.emr_backend", "name": "emr_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "emr_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.emr.models.ElasticMapReduceBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.emr_backend", "name": "emr_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "emr_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.emr.models.ElasticMapReduceBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_resources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pagination_token", "resources_per_page", "tags_per_page", "tag_filters", "resource_type_filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.get_resources", "name": "get_resources", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "pagination_token", "resources_per_page", "tags_per_page", "tag_filters", "resource_type_filters"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resources of ResourceGroupsTaggingAPIBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tag_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "pagination_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.get_tag_keys", "name": "get_tag_keys", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "pagination_token"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tag_keys of ResourceGroupsTaggingAPIBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tag_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pagination_token", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.get_tag_values", "name": "get_tag_values", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "pagination_token", "key"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tag_values of ResourceGroupsTaggingAPIBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "glacier_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.glacier_backend", "name": "glacier_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "glacier_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.glacier.models.GlacierBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.glacier_backend", "name": "glacier_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "glacier_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.glacier.models.GlacierBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "glue_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.glue_backend", "name": "glue_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "glue_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.glue.models.GlueBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.glue_backend", "name": "glue_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "glue_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.glue.models.GlueBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kinesis_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.kinesis_backend", "name": "kinesis_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kinesis_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.kinesis.models.KinesisBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.kinesis_backend", "name": "kinesis_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kinesis_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.kinesis.models.KinesisBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kms_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.kms_backend", "name": "kms_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kms_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.kms.models.KmsBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.kms_backend", "name": "kms_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kms_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.kms.models.KmsBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "lambda_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.lambda_backend", "name": "lambda_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lambda_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.awslambda.models.LambdaBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.lambda_backend", "name": "lambda_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lambda_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.awslambda.models.LambdaBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "logs_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.logs_backend", "name": "logs_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logs_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.logs.models.LogsBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.logs_backend", "name": "logs_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logs_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.logs.models.LogsBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "rds_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.rds_backend", "name": "rds_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rds_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.rds.models.RDSBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.rds_backend", "name": "rds_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rds_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.rds.models.RDSBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "redshift_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.redshift_backend", "name": "redshift_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "redshift_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.redshift.models.RedshiftBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.redshift_backend", "name": "redshift_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "redshift_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.redshift.models.RedshiftBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "s3_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.s3_backend", "name": "s3_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "s3_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.s3.models.S3Backend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.s3_backend", "name": "s3_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "s3_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.s3.models.S3Backend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "sqs_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.sqs_backend", "name": "sqs_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sqs_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.sqs.models.SQSBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.sqs_backend", "name": "sqs_backend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sqs_backend of ResourceGroupsTaggingAPIBackend", "ret_type": "moto.sqs.models.SQSBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "tag_resources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arns", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.tag_resources", "name": "tag_resources", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arns", "tags"], "arg_types": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tag_resources of ResourceGroupsTaggingAPIBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3Backend": {".class": "SymbolTableNode", "cross_ref": "moto.s3.models.S3Backend", "kind": "Gdef"}, "SQSBackend": {".class": "SymbolTableNode", "cross_ref": "moto.sqs.models.SQSBackend", "kind": "Gdef"}, "TaggingService": {".class": "SymbolTableNode", "cross_ref": "moto.utilities.tagging_service.TaggingService", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.resourcegroupstaggingapi.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.resourcegroupstaggingapi.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.resourcegroupstaggingapi.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.resourcegroupstaggingapi.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.resourcegroupstaggingapi.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.resourcegroupstaggingapi.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "acm_backends": {".class": "SymbolTableNode", "cross_ref": "moto.acm.models.acm_backends", "kind": "Gdef"}, "ec2_backends": {".class": "SymbolTableNode", "cross_ref": "moto.ec2.models.ec2_backends", "kind": "Gdef"}, "ecs_backends": {".class": "SymbolTableNode", "cross_ref": "moto.ecs.models.ecs_backends", "kind": "Gdef"}, "elb_backends": {".class": "SymbolTableNode", "cross_ref": "moto.elb.models.elb_backends", "kind": "Gdef"}, "elbv2_backends": {".class": "SymbolTableNode", "cross_ref": "moto.elbv2.models.elbv2_backends", "kind": "Gdef"}, "emr_backends": {".class": "SymbolTableNode", "cross_ref": "moto.emr.models.emr_backends", "kind": "Gdef"}, "glacier_backends": {".class": "SymbolTableNode", "cross_ref": "moto.glacier.models.glacier_backends", "kind": "Gdef"}, "glue_backends": {".class": "SymbolTableNode", "cross_ref": "moto.glue.models.glue_backends", "kind": "Gdef"}, "kinesis_backends": {".class": "SymbolTableNode", "cross_ref": "moto.kinesis.models.kinesis_backends", "kind": "Gdef"}, "kms_backends": {".class": "SymbolTableNode", "cross_ref": "moto.kms.models.kms_backends", "kind": "Gdef"}, "lambda_backends": {".class": "SymbolTableNode", "cross_ref": "moto.awslambda.models.lambda_backends", "kind": "Gdef"}, "logs_backends": {".class": "SymbolTableNode", "cross_ref": "moto.logs.models.logs_backends", "kind": "Gdef"}, "mock_random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}, "rds_backends": {".class": "SymbolTableNode", "cross_ref": "moto.rds.models.rds_backends", "kind": "Gdef"}, "redshift_backends": {".class": "SymbolTableNode", "cross_ref": "moto.redshift.models.redshift_backends", "kind": "Gdef"}, "resourcegroupstaggingapi_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.resourcegroupstaggingapi.models.resourcegroupstaggingapi_backends", "name": "resourcegroupstaggingapi_backends", "type": {".class": "Instance", "args": ["moto.resourcegroupstaggingapi.models.ResourceGroupsTaggingAPIBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "s3_backends": {".class": "SymbolTableNode", "cross_ref": "moto.s3.models.s3_backends", "kind": "Gdef"}, "sqs_backends": {".class": "SymbolTableNode", "cross_ref": "moto.sqs.models.sqs_backends", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/resourcegroupstaggingapi/models.py"}