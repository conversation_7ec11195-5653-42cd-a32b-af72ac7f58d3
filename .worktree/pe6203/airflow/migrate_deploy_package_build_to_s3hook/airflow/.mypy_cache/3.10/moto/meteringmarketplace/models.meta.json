{"data_mtime": 1756972668, "dep_lines": [5, 4, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["moto.moto_api._internal", "moto.core", "collections", "typing", "builtins", "os", "re", "sys", "string", "inspect", "traceback", "pprint", "types", "itertools", "warnings", "copy", "html", "_frozen_importlib", "_random", "_typeshed", "abc", "moto.core.base_backend", "moto.core.common_models", "moto.moto_api", "moto.moto_api._internal.moto_random", "random", "uuid"], "hash": "7cdb497176b077d06da847e6a3eb3dfd2f8d179e", "id": "moto.meteringmarketplace.models", "ignore_all": true, "interface_hash": "ba7d7f4dfbd2635d5eee2ec29a6acbde93b727c8", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/meteringmarketplace/models.py", "plugin_data": null, "size": 4523, "suppressed": [], "version_id": "1.15.0"}