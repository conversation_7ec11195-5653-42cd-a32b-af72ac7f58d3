{"data_mtime": 1756972668, "dep_lines": [11, 12, 14, 20, 441, 10, 11, 1, 2, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8], "dep_prios": [10, 5, 5, 5, 20, 5, 20, 10, 10, 10, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["moto.sagemaker.validators", "moto.utilities.paginator", "moto.sagemaker.exceptions", "moto.sagemaker.utils", "moto.cloudformation.exceptions", "moto.core", "moto.sagemaker", "json", "os", "random", "string", "datetime", "typing", "builtins", "re", "sys", "inspect", "traceback", "pprint", "collections", "types", "itertools", "warnings", "copy", "html", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "fractions", "moto.core.base_backend", "moto.core.common_models", "moto.core.exceptions", "moto.utilities", "numbers", "werkzeug", "werkzeug.exceptions"], "hash": "150cd769cfc81eaf7d9c3b476971f63a9cfdcad4", "id": "moto.sagemaker.models", "ignore_all": true, "interface_hash": "7dd4a303445f4dccffbd9b677b5b5c675495e454", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/sagemaker/models.py", "plugin_data": null, "size": 137932, "suppressed": ["dateutil.tz"], "version_id": "1.15.0"}