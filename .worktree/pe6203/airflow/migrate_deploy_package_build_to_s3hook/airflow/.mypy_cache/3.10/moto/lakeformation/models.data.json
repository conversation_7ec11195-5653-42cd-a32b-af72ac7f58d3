{".class": "MypyFile", "_fullname": "moto.lakeformation.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AlreadyExists": {".class": "SymbolTableNode", "cross_ref": "moto.lakeformation.exceptions.AlreadyExists", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EntityNotFound": {".class": "SymbolTableNode", "cross_ref": "moto.lakeformation.exceptions.EntityNotFound", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "ExcludedColumnNames": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.lakeformation.models.ExcludedColumnNames", "name": "ExcludedColumnNames", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ExcludedColumnNames", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.lakeformation.models", "mro": ["moto.lakeformation.models.ExcludedColumnNames", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "excluded_column_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ExcludedColumnNames.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "excluded_column_names"], "arg_types": ["moto.lakeformation.models.ExcludedColumnNames", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExcludedColumnNames", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "excluded_column_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ExcludedColumnNames.excluded_column_names", "name": "excluded_column_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.lakeformation.models.ExcludedColumnNames.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.lakeformation.models.ExcludedColumnNames", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidInput": {".class": "SymbolTableNode", "cross_ref": "moto.lakeformation.exceptions.InvalidInput", "kind": "Gdef"}, "LFTag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.lakeformation.models.LFTag", "name": "LFTag", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LFTag", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.lakeformation.models", "mro": ["moto.lakeformation.models.LFTag", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag_key", "tag_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LFTag.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tag_key", "tag_values"], "arg_types": ["moto.lakeformation.models.LFTag", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LFTag", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tag_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.LFTag.tag_key", "name": "tag_key", "type": "builtins.str"}}, "tag_values": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.LFTag.tag_values", "name": "tag_values", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.lakeformation.models.LFTag.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.lakeformation.models.LFTag", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LakeFormationBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.lakeformation.models.LakeFormationBackend", "name": "LakeFormationBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.lakeformation.models", "mro": ["moto.lakeformation.models.LakeFormationBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LakeFormationBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_lf_tags_to_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "catalog_id", "resource", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.add_lf_tags_to_resource", "name": "add_lf_tags_to_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "catalog_id", "resource", "tags"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_lf_tags_to_resource of LakeFormationBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_grant_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "entries"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.batch_grant_permissions", "name": "batch_grant_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "entries"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_grant_permissions of LakeFormationBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_revoke_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "entries"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.batch_revoke_permissions", "name": "batch_revoke_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "entries"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "batch_revoke_permissions of LakeFormationBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_lf_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "catalog_id", "key", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.create_lf_tag", "name": "create_lf_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "catalog_id", "key", "values"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_lf_tag of LakeFormationBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_lf_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.delete_lf_tag", "name": "delete_lf_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "key"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_lf_tag of LakeFormationBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deregister_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.deregister_resource", "name": "deregister_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deregister_resource of LakeFormationBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.describe_resource", "name": "describe_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_resource of LakeFormationBackend", "ret_type": "moto.lakeformation.models.Resource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_data_lake_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "catalog_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.get_data_lake_settings", "name": "get_data_lake_settings", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "catalog_id"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_data_lake_settings of LakeFormationBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_lf_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.get_lf_tag", "name": "get_lf_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "key"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_lf_tag of LakeFormationBackend", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resource_lf_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "resource"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.get_resource_lf_tags", "name": "get_resource_lf_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "resource"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_lf_tags of LakeFormationBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "grant_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "catalog_id", "principal", "resource", "permissions", "permissions_with_grant_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.grant_permissions", "name": "grant_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "catalog_id", "principal", "resource", "permissions", "permissions_with_grant_options"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "grant_permissions of LakeFormationBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "grants": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.lakeformation.models.LakeFormationBackend.grants", "name": "grants", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "lf_columns_tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.lakeformation.models.LakeFormationBackend.lf_columns_tags", "name": "lf_columns_tags", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "lf_database_tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.lakeformation.models.LakeFormationBackend.lf_database_tags", "name": "lf_database_tags", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "lf_table_tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.lakeformation.models.LakeFormationBackend.lf_table_tags", "name": "lf_table_tags", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_data_cells_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.list_data_cells_filter", "name": "list_data_cells_filter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_data_cells_filter of LakeFormationBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_lf_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "catalog_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.list_lf_tags", "name": "list_lf_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "catalog_id"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_lf_tags of LakeFormationBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "catalog_id", "principal", "resource", "resource_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.list_permissions", "name": "list_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "catalog_id", "principal", "resource", "resource_type"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResource", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["moto.lakeformation.models.RessourceType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_permissions of LakeFormationBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_resources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.list_resources", "name": "list_resources", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_resources of LakeFormationBackend", "ret_type": {".class": "Instance", "args": ["moto.lakeformation.models.Resource"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "put_data_lake_settings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.put_data_lake_settings", "name": "put_data_lake_settings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "settings"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put_data_lake_settings of LakeFormationBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arn", "role_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.register_resource", "name": "register_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arn", "role_arn"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_resource of LakeFormationBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_lf_tags_from_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "catalog_id", "resource", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.remove_lf_tags_from_resource", "name": "remove_lf_tags_from_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "catalog_id", "resource", "tags"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_lf_tags_from_resource of LakeFormationBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resources": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.lakeformation.models.LakeFormationBackend.resources", "name": "resources", "type": {".class": "Instance", "args": ["builtins.str", "moto.lakeformation.models.Resource"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "revoke_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "catalog_id", "principal", "resource", "permissions_to_revoke", "permissions_with_grant_options_to_revoke"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.revoke_permissions", "name": "revoke_permissions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "catalog_id", "principal", "resource", "permissions_to_revoke", "permissions_with_grant_options_to_revoke"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "revoke_permissions of LakeFormationBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "settings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.lakeformation.models.LakeFormationBackend.settings", "name": "settings", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "tagger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.LakeFormationBackend.tagger", "name": "tagger", "type": "moto.utilities.tagging_service.TaggingService"}}, "update_lf_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "catalog_id", "tag_key", "to_delete", "to_add"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.LakeFormationBackend.update_lf_tag", "name": "update_lf_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "catalog_id", "tag_key", "to_delete", "to_add"], "arg_types": ["moto.lakeformation.models.LakeFormationBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_lf_tag of LakeFormationBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.lakeformation.models.LakeFormationBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.lakeformation.models.LakeFormationBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ListPermissionsResource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.lakeformation.models.ListPermissionsResource", "name": "ListPermissionsResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.lakeformation.models", "mro": ["moto.lakeformation.models.ListPermissionsResource", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "catalog", "database", "table", "table_with_columns", "data_location", "data_cells_filter", "lf_tag", "lf_tag_policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResource.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "catalog", "database", "table", "table_with_columns", "data_location", "data_cells_filter", "lf_tag", "lf_tag_policy"], "arg_types": ["moto.lakeformation.models.ListPermissionsResource", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceDatabase", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceTable", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceTableWithColumns", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceDataLocation", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceDataCellsFilter", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceLFTag", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceLFTagPolicy", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ListPermissionsResource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "catalog": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResource.catalog", "name": "catalog", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "data_cells_filter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResource.data_cells_filter", "name": "data_cells_filter", "type": {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceDataCellsFilter", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "data_location": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResource.data_location", "name": "data_location", "type": {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceDataLocation", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "database": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResource.database", "name": "database", "type": {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceDatabase", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "lf_tag": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResource.lf_tag", "name": "lf_tag", "type": {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceLFTag", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "lf_tag_policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResource.lf_tag_policy", "name": "lf_tag_policy", "type": {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceLFTagPolicy", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResource.table", "name": "table", "type": {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceTable", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table_with_columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResource.table_with_columns", "name": "table_with_columns", "type": {".class": "UnionType", "items": ["moto.lakeformation.models.ListPermissionsResourceTableWithColumns", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.lakeformation.models.ListPermissionsResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.lakeformation.models.ListPermissionsResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListPermissionsResourceDataCellsFilter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.lakeformation.models.ListPermissionsResourceDataCellsFilter", "name": "ListPermissionsResourceDataCellsFilter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceDataCellsFilter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.lakeformation.models", "mro": ["moto.lakeformation.models.ListPermissionsResourceDataCellsFilter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "table_catalog_id", "database_name", "table_name", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceDataCellsFilter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "table_catalog_id", "database_name", "table_name", "name"], "arg_types": ["moto.lakeformation.models.ListPermissionsResourceDataCellsFilter", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ListPermissionsResourceDataCellsFilter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "database_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceDataCellsFilter.database_name", "name": "database_name", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceDataCellsFilter.name", "name": "name", "type": "builtins.str"}}, "table_catalog_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceDataCellsFilter.table_catalog_id", "name": "table_catalog_id", "type": "builtins.str"}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceDataCellsFilter.table_name", "name": "table_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.lakeformation.models.ListPermissionsResourceDataCellsFilter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.lakeformation.models.ListPermissionsResourceDataCellsFilter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListPermissionsResourceDataLocation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.lakeformation.models.ListPermissionsResourceDataLocation", "name": "ListPermissionsResourceDataLocation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceDataLocation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.lakeformation.models", "mro": ["moto.lakeformation.models.ListPermissionsResourceDataLocation", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "resource_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceDataLocation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "resource_arn"], "arg_types": ["moto.lakeformation.models.ListPermissionsResourceDataLocation", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ListPermissionsResourceDataLocation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "catalog_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceDataLocation.catalog_id", "name": "catalog_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "resource_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceDataLocation.resource_arn", "name": "resource_arn", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.lakeformation.models.ListPermissionsResourceDataLocation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.lakeformation.models.ListPermissionsResourceDataLocation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListPermissionsResourceDatabase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.lakeformation.models.ListPermissionsResourceDatabase", "name": "ListPermissionsResourceDatabase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceDatabase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.lakeformation.models", "mro": ["moto.lakeformation.models.ListPermissionsResourceDatabase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceDatabase.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "catalog_id", "name"], "arg_types": ["moto.lakeformation.models.ListPermissionsResourceDatabase", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ListPermissionsResourceDatabase", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "catalog_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceDatabase.catalog_id", "name": "catalog_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceDatabase.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.lakeformation.models.ListPermissionsResourceDatabase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.lakeformation.models.ListPermissionsResourceDatabase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListPermissionsResourceLFTag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTag", "name": "ListPermissionsResourceLFTag", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTag", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.lakeformation.models", "mro": ["moto.lakeformation.models.ListPermissionsResourceLFTag", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "catalog_id", "tag_key", "tag_values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTag.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "catalog_id", "tag_key", "tag_values"], "arg_types": ["moto.lakeformation.models.ListPermissionsResourceLFTag", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ListPermissionsResourceLFTag", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "catalog_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTag.catalog_id", "name": "catalog_id", "type": "builtins.str"}}, "tag_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTag.tag_key", "name": "tag_key", "type": "builtins.str"}}, "tag_values": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTag.tag_values", "name": "tag_values", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTag.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.lakeformation.models.ListPermissionsResourceLFTag", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListPermissionsResourceLFTagPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTagPolicy", "name": "ListPermissionsResourceLFTagPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTagPolicy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.lakeformation.models", "mro": ["moto.lakeformation.models.ListPermissionsResourceLFTagPolicy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "catalog_id", "resource_type", "expression"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTagPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "catalog_id", "resource_type", "expression"], "arg_types": ["moto.lakeformation.models.ListPermissionsResourceLFTagPolicy", "builtins.str", "builtins.str", {".class": "Instance", "args": ["moto.lakeformation.models.LFTag"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ListPermissionsResourceLFTagPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "catalog_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTagPolicy.catalog_id", "name": "catalog_id", "type": "builtins.str"}}, "expression": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTagPolicy.expression", "name": "expression", "type": {".class": "Instance", "args": ["moto.lakeformation.models.LFTag"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "resource_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTagPolicy.resource_type", "name": "resource_type", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.lakeformation.models.ListPermissionsResourceLFTagPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.lakeformation.models.ListPermissionsResourceLFTagPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListPermissionsResourceTable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.lakeformation.models.ListPermissionsResourceTable", "name": "ListPermissionsResourceTable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceTable", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.lakeformation.models", "mro": ["moto.lakeformation.models.ListPermissionsResourceTable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "catalog_id", "database_name", "name", "table_wildcard"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceTable.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "catalog_id", "database_name", "name", "table_wildcard"], "arg_types": ["moto.lakeformation.models.ListPermissionsResourceTable", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ListPermissionsResourceTable", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "catalog_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceTable.catalog_id", "name": "catalog_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "database_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceTable.database_name", "name": "database_name", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceTable.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "table_wildcard": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceTable.table_wildcard", "name": "table_wildcard", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.lakeformation.models.ListPermissionsResourceTable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.lakeformation.models.ListPermissionsResourceTable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListPermissionsResourceTableWithColumns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.lakeformation.models.ListPermissionsResourceTableWithColumns", "name": "ListPermissionsResourceTableWithColumns", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceTableWithColumns", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.lakeformation.models", "mro": ["moto.lakeformation.models.ListPermissionsResourceTableWithColumns", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "catalog_id", "database_name", "name", "column_names", "column_wildcard"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.ListPermissionsResourceTableWithColumns.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "catalog_id", "database_name", "name", "column_names", "column_wildcard"], "arg_types": ["moto.lakeformation.models.ListPermissionsResourceTableWithColumns", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "moto.lakeformation.models.ExcludedColumnNames"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ListPermissionsResourceTableWithColumns", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "catalog_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceTableWithColumns.catalog_id", "name": "catalog_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "column_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceTableWithColumns.column_names", "name": "column_names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "column_wildcard": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceTableWithColumns.column_wildcard", "name": "column_wildcard", "type": "moto.lakeformation.models.ExcludedColumnNames"}}, "database_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceTableWithColumns.database_name", "name": "database_name", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.ListPermissionsResourceTableWithColumns.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.lakeformation.models.ListPermissionsResourceTableWithColumns.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.lakeformation.models.ListPermissionsResourceTableWithColumns", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.lakeformation.models.Resource", "name": "Resource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.Resource", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.lakeformation.models", "mro": ["moto.lakeformation.models.Resource", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "arn", "role_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.Resource.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arn", "role_arn"], "arg_types": ["moto.lakeformation.models.Resource", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Resource", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.Resource.arn", "name": "arn", "type": "builtins.str"}}, "role_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.lakeformation.models.Resource.role_arn", "name": "role_arn", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.Resource.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.lakeformation.models.Resource"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of Resource", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.lakeformation.models.Resource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.lakeformation.models.Resource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RessourceType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.lakeformation.models.RessourceType", "name": "RessourceType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "moto.lakeformation.models.RessourceType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "moto.lakeformation.models", "mro": ["moto.lakeformation.models.RessourceType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "catalog": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.lakeformation.models.RessourceType.catalog", "name": "catalog", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "CATALOG"}, "type_ref": "builtins.str"}}}, "data_location": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.lakeformation.models.RessourceType.data_location", "name": "data_location", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "DATA_LOCATION"}, "type_ref": "builtins.str"}}}, "database": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.lakeformation.models.RessourceType.database", "name": "database", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "DATABASE"}, "type_ref": "builtins.str"}}}, "table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.lakeformation.models.RessourceType.table", "name": "table", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "TABLE"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.lakeformation.models.RessourceType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.lakeformation.models.RessourceType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TaggingService": {".class": "SymbolTableNode", "cross_ref": "moto.utilities.tagging_service.TaggingService", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.lakeformation.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.lakeformation.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.lakeformation.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.lakeformation.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.lakeformation.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.lakeformation.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "default_settings": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.lakeformation.models.default_settings", "name": "default_settings", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_settings", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "lakeformation_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.lakeformation.models.lakeformation_backends", "name": "lakeformation_backends", "type": {".class": "Instance", "args": ["moto.lakeformation.models.LakeFormationBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/lakeformation/models.py"}