{".class": "MypyFile", "_fullname": "moto.servicequotas.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NoSuchResource": {".class": "SymbolTableNode", "cross_ref": "moto.servicequotas.exceptions.NoSuchResource", "kind": "Gdef"}, "ServiceQuotasBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.servicequotas.models.ServiceQuotasBackend", "name": "ServiceQuotasBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.servicequotas.models.ServiceQuotasBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.servicequotas.models", "mro": ["moto.servicequotas.models.ServiceQuotasBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.servicequotas.models.ServiceQuotasBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.servicequotas.models.ServiceQuotasBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ServiceQuotasBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_service_quota": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "service_code", "quota_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.servicequotas.models.ServiceQuotasBackend.get_service_quota", "name": "get_service_quota", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "service_code", "quota_code"], "arg_types": ["moto.servicequotas.models.ServiceQuotasBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_service_quota of ServiceQuotasBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_aws_default_service_quotas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "service_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.servicequotas.models.ServiceQuotasBackend.list_aws_default_service_quotas", "name": "list_aws_default_service_quotas", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "service_code"], "arg_types": ["moto.servicequotas.models.ServiceQuotasBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_aws_default_service_quotas of ServiceQuotasBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.servicequotas.models.ServiceQuotasBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.servicequotas.models.ServiceQuotasBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VPC_DEFAULT_QUOTAS": {".class": "SymbolTableNode", "cross_ref": "moto.servicequotas.resources.default_quotas.vpc.VPC_DEFAULT_QUOTAS", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.servicequotas.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.servicequotas.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.servicequotas.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.servicequotas.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.servicequotas.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.servicequotas.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "servicequotas_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.servicequotas.models.servicequotas_backends", "name": "servicequotas_backends", "type": {".class": "Instance", "args": ["moto.servicequotas.models.ServiceQuotasBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/servicequotas/models.py"}