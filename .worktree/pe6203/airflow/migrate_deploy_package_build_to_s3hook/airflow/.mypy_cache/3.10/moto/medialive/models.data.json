{".class": "MypyFile", "_fullname": "moto.medialive.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "Channel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.medialive.models.Channel", "name": "Channel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.medialive.models.Channel", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.medialive.models", "mro": ["moto.medialive.models.Channel", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.Channel.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["moto.medialive.models.Channel", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Channel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_previous_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel._previous_state", "name": "_previous_state", "type": {".class": "NoneType"}}}, "_resolve_transient_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.Channel._resolve_transient_states", "name": "_resolve_transient_states", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.medialive.models.Channel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_transient_states of Channel", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.arn", "name": "arn", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cdi_input_specification": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.cdi_input_specification", "name": "cdi_input_specification", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "channel_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.channel_class", "name": "channel_class", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "channel_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.channel_id", "name": "channel_id", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "destinations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.destinations", "name": "destinations", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "egress_endpoints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.egress_endpoints", "name": "egress_endpoints", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "encoder_settings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.encoder_settings", "name": "encoder_settings", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "input_attachments": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.input_attachments", "name": "input_attachments", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "input_specification": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.input_specification", "name": "input_specification", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "log_level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.log_level", "name": "log_level", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.name", "name": "name", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "pipeline_details": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.pipeline_details", "name": "pipeline_details", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "role_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.role_arn", "name": "role_arn", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.state", "name": "state", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Channel.tags", "name": "tags", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.Channel.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "exclude"], "arg_types": ["moto.medialive.models.Channel", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of Channel", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.medialive.models.Channel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.medialive.models.Channel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Input": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.medialive.models.Input", "name": "Input", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.medialive.models.Input", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.medialive.models", "mro": ["moto.medialive.models.Input", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.Input.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["moto.medialive.models.Input", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Input", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_transient_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.Input._resolve_transient_states", "name": "_resolve_transient_states", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.medialive.models.Input"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_transient_states of Input", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.arn", "name": "arn", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "attached_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.attached_channels", "name": "attached_channels", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "destinations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.destinations", "name": "destinations", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "input_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.input_class", "name": "input_class", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "input_devices": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.input_devices", "name": "input_devices", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "input_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.input_id", "name": "input_id", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "input_source_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.input_source_type", "name": "input_source_type", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "input_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.input_type", "name": "input_type", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "media_connect_flows": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.media_connect_flows", "name": "media_connect_flows", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.name", "name": "name", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "role_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.role_arn", "name": "role_arn", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "security_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.security_groups", "name": "security_groups", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "sources": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.sources", "name": "sources", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.state", "name": "state", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.medialive.models.Input.tags", "name": "tags", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.Input.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.medialive.models.Input"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of Input", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.medialive.models.Input.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.medialive.models.Input", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MediaLiveBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.medialive.models.MediaLiveBackend", "name": "MediaLiveBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.medialive.models", "mro": ["moto.medialive.models.MediaLiveBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.medialive.models.MediaLiveBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MediaLiveBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_channels": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.medialive.models.MediaLiveBackend._channels", "name": "_channels", "type": {".class": "Instance", "args": ["builtins.str", "moto.medialive.models.Channel"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_inputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.medialive.models.MediaLiveBackend._inputs", "name": "_inputs", "type": {".class": "Instance", "args": ["builtins.str", "moto.medialive.models.Input"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "create_channel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cdi_input_specification", "channel_class", "destinations", "encoder_settings", "input_attachments", "input_specification", "log_level", "name", "role_arn", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend.create_channel", "name": "create_channel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "cdi_input_specification", "channel_class", "destinations", "encoder_settings", "input_attachments", "input_specification", "log_level", "name", "role_arn", "tags"], "arg_types": ["moto.medialive.models.MediaLiveBackend", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_channel of MediaLiveBackend", "ret_type": "moto.medialive.models.Channel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "destinations", "input_devices", "input_security_groups", "media_connect_flows", "name", "role_arn", "sources", "tags", "input_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend.create_input", "name": "create_input", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "destinations", "input_devices", "input_security_groups", "media_connect_flows", "name", "role_arn", "sources", "tags", "input_type"], "arg_types": ["moto.medialive.models.MediaLiveBackend", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_input of MediaLiveBackend", "ret_type": "moto.medialive.models.Input", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_channel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "channel_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend.delete_channel", "name": "delete_channel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "channel_id"], "arg_types": ["moto.medialive.models.MediaLiveBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_channel of MediaLiveBackend", "ret_type": "moto.medialive.models.Channel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend.delete_input", "name": "delete_input", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "input_id"], "arg_types": ["moto.medialive.models.MediaLiveBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_input of MediaLiveBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_channel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "channel_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend.describe_channel", "name": "describe_channel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "channel_id"], "arg_types": ["moto.medialive.models.MediaLiveBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_channel of MediaLiveBackend", "ret_type": "moto.medialive.models.Channel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "input_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend.describe_input", "name": "describe_input", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "input_id"], "arg_types": ["moto.medialive.models.MediaLiveBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_input of MediaLiveBackend", "ret_type": "moto.medialive.models.Input", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_channels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_results"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend.list_channels", "name": "list_channels", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_results"], "arg_types": ["moto.medialive.models.MediaLiveBackend", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_channels of MediaLiveBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_inputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_results"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend.list_inputs", "name": "list_inputs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_results"], "arg_types": ["moto.medialive.models.MediaLiveBackend", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_inputs of MediaLiveBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_channel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "channel_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend.start_channel", "name": "start_channel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "channel_id"], "arg_types": ["moto.medialive.models.MediaLiveBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_channel of MediaLiveBackend", "ret_type": "moto.medialive.models.Channel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stop_channel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "channel_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend.stop_channel", "name": "stop_channel", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "channel_id"], "arg_types": ["moto.medialive.models.MediaLiveBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop_channel of MediaLiveBackend", "ret_type": "moto.medialive.models.Channel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_channel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "channel_id", "cdi_input_specification", "destinations", "encoder_settings", "input_attachments", "input_specification", "log_level", "name", "role_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend.update_channel", "name": "update_channel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "channel_id", "cdi_input_specification", "destinations", "encoder_settings", "input_attachments", "input_specification", "log_level", "name", "role_arn"], "arg_types": ["moto.medialive.models.MediaLiveBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_channel of MediaLiveBackend", "ret_type": "moto.medialive.models.Channel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "destinations", "input_devices", "input_id", "input_security_groups", "media_connect_flows", "name", "role_arn", "sources"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.medialive.models.MediaLiveBackend.update_input", "name": "update_input", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "destinations", "input_devices", "input_id", "input_security_groups", "media_connect_flows", "name", "role_arn", "sources"], "arg_types": ["moto.medialive.models.MediaLiveBackend", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_input of MediaLiveBackend", "ret_type": "moto.medialive.models.Input", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.medialive.models.MediaLiveBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.medialive.models.MediaLiveBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.medialive.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.medialive.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.medialive.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.medialive.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.medialive.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.medialive.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "medialive_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.medialive.models.medialive_backends", "name": "medialive_backends", "type": {".class": "Instance", "args": ["moto.medialive.models.MediaLiveBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "mock_random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/medialive/models.py"}