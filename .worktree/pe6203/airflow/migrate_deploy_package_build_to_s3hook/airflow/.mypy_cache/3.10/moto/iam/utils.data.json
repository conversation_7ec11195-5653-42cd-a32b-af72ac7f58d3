{".class": "MypyFile", "_fullname": "moto.iam.utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACCOUNT_OFFSET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.iam.utils.ACCOUNT_OFFSET", "name": "ACCOUNT_OFFSET", "type": "builtins.int"}}, "AWS_ROLE_ALPHABET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.iam.utils.AWS_ROLE_ALPHABET", "name": "AWS_ROLE_ALPHABET", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iam.utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iam.utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iam.utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iam.utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iam.utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iam.utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_random_uppercase_or_digit_sequence": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.utils._random_uppercase_or_digit_sequence", "name": "_random_uppercase_or_digit_sequence", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["length"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_random_uppercase_or_digit_sequence", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef"}, "generate_access_key_id_from_account_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["account_id", "prefix", "total_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.utils.generate_access_key_id_from_account_id", "name": "generate_access_key_id_from_account_id", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["account_id", "prefix", "total_length"], "arg_types": ["builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_access_key_id_from_account_id", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}, "random_access_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.utils.random_access_key", "name": "random_access_key", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "random_access_key", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "random_alphanumeric": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.utils.random_alphanumeric", "name": "random_alphanumeric", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["length"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "random_alphanumeric", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "random_policy_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.utils.random_policy_id", "name": "random_policy_id", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "random_policy_id", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "random_resource_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.utils.random_resource_id", "name": "random_resource_id", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["size"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "random_resource_id", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "random_role_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iam.utils.random_role_id", "name": "random_role_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["account_id"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "random_role_id", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "string": {".class": "SymbolTableNode", "cross_ref": "string", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/iam/utils.py"}