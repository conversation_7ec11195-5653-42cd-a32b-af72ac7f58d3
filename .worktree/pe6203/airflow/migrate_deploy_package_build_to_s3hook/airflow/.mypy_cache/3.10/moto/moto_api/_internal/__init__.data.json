{".class": "MypyFile", "_fullname": "moto.moto_api._internal", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MotoRandom": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.moto_random.MotoRandom", "kind": "Gdef"}, "Recorder": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.recorder.models.Recorder", "kind": "Gdef"}, "StateManager": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.state_manager.StateManager", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.moto_api._internal.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.moto_api._internal.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.moto_api._internal.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.moto_api._internal.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.moto_api._internal.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.moto_api._internal.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.moto_api._internal.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "mock_random": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.moto_api._internal.mock_random", "name": "mock_random", "type": "moto.moto_api._internal.moto_random.MotoRandom"}}, "moto_api_backend": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.models.moto_api_backend", "kind": "Gdef"}, "moto_api_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.moto_api._internal.moto_api_backends", "name": "moto_api_backends", "type": {".class": "Instance", "args": ["builtins.str", "moto.moto_api._internal.models.MotoAPIBackend"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/moto_api/_internal/__init__.py"}