{".class": "MypyFile", "_fullname": "moto.swf.models.decision_task", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "DecisionTask": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.swf.models.decision_task.DecisionTask", "name": "DecisionTask", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.swf.models.decision_task.DecisionTask", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.swf.models.decision_task", "mro": ["moto.swf.models.decision_task.DecisionTask", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "workflow_execution", "scheduled_event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.decision_task.DecisionTask.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "workflow_execution", "scheduled_event_id"], "arg_types": ["moto.swf.models.decision_task.DecisionTask", "moto.swf.models.workflow_execution.WorkflowExecution", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DecisionTask", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_workflow_execution_open": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.decision_task.DecisionTask._check_workflow_execution_open", "name": "_check_workflow_execution_open", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.swf.models.decision_task.DecisionTask"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_workflow_execution_open of DecisionTask", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.decision_task.DecisionTask.complete", "name": "complete", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.swf.models.decision_task.DecisionTask"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "complete of DecisionTask", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "first_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.decision_task.DecisionTask.first_timeout", "name": "first_timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.swf.models.decision_task.DecisionTask"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "first_timeout of DecisionTask", "ret_type": {".class": "UnionType", "items": ["moto.swf.models.timeout.Timeout", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "previous_started_event_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.swf.models.decision_task.DecisionTask.previous_started_event_id", "name": "previous_started_event_id", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "process_timeouts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.decision_task.DecisionTask.process_timeouts", "name": "process_timeouts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.swf.models.decision_task.DecisionTask"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_timeouts of DecisionTask", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scheduled_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.swf.models.decision_task.DecisionTask.scheduled_at", "name": "scheduled_at", "type": "datetime.datetime"}}, "scheduled_event_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.swf.models.decision_task.DecisionTask.scheduled_event_id", "name": "scheduled_event_id", "type": "builtins.int"}}, "start": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "started_event_id", "previous_started_event_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.decision_task.DecisionTask.start", "name": "start", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "started_event_id", "previous_started_event_id"], "arg_types": ["moto.swf.models.decision_task.DecisionTask", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start of DecisionTask", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_to_close_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.swf.models.decision_task.DecisionTask.start_to_close_timeout", "name": "start_to_close_timeout", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "started": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.swf.models.decision_task.DecisionTask.started", "name": "started", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.swf.models.decision_task.DecisionTask"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "started of DecisionTask", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.swf.models.decision_task.DecisionTask.started", "name": "started", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.swf.models.decision_task.DecisionTask"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "started of DecisionTask", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "started_event_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.swf.models.decision_task.DecisionTask.started_event_id", "name": "started_event_id", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "started_timestamp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.swf.models.decision_task.DecisionTask.started_timestamp", "name": "started_timestamp", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.swf.models.decision_task.DecisionTask.state", "name": "state", "type": "builtins.str"}}, "task_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.swf.models.decision_task.DecisionTask.task_token", "name": "task_token", "type": "builtins.str"}}, "timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.decision_task.DecisionTask.timeout", "name": "timeout", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_timeout"], "arg_types": ["moto.swf.models.decision_task.DecisionTask", "moto.swf.models.timeout.Timeout"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timeout of DecisionTask", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "timeout_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.swf.models.decision_task.DecisionTask.timeout_type", "name": "timeout_type", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "to_full_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "reverse_order"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.decision_task.DecisionTask.to_full_dict", "name": "to_full_dict", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "reverse_order"], "arg_types": ["moto.swf.models.decision_task.DecisionTask", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_full_dict of DecisionTask", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "workflow_execution": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.swf.models.decision_task.DecisionTask.workflow_execution", "name": "workflow_execution", "type": "moto.swf.models.workflow_execution.WorkflowExecution"}}, "workflow_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.swf.models.decision_task.DecisionTask.workflow_type", "name": "workflow_type", "type": "moto.swf.models.workflow_type.WorkflowType"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.swf.models.decision_task.DecisionTask.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.swf.models.decision_task.DecisionTask", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SWFWorkflowExecutionClosedError": {".class": "SymbolTableNode", "cross_ref": "moto.swf.exceptions.SWFWorkflowExecutionClosedError", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "moto.swf.models.timeout.Timeout", "kind": "Gdef"}, "WorkflowExecution": {".class": "SymbolTableNode", "cross_ref": "moto.swf.models.workflow_execution.WorkflowExecution", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.swf.models.decision_task.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.swf.models.decision_task.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.swf.models.decision_task.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.swf.models.decision_task.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.swf.models.decision_task.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.swf.models.decision_task.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "mock_random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}, "unix_time": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.unix_time", "kind": "Gdef"}, "utcnow": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.utcnow", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/swf/models/decision_task.py"}