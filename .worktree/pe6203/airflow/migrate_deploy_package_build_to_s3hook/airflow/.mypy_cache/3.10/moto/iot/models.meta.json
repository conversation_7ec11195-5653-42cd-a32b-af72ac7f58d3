{"data_mtime": 1756972668, "dep_lines": [12, 11, 11, 12, 9, 10, 11, 15, 16, 17, 19, 30, 33, 8, 14, 1253, 1, 2, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 20, 5, 5, 20, 5, 5, 5, 5, 5, 25, 10, 5, 20, 10, 10, 10, 5, 5, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.primitives.asymmetric.rsa", "cryptography.hazmat.primitives.hashes", "cryptography.hazmat.primitives.serialization", "cryptography.hazmat.primitives.asymmetric", "cryptography.hazmat._oid", "cryptography.hazmat.backends", "cryptography.hazmat.primitives", "moto.core.utils", "moto.moto_api._internal", "moto.utilities.paginator", "moto.iot.exceptions", "moto.iot.utils", "moto.iotdata.models", "cryptography.x509", "moto.core", "moto.cognitoidentity", "<PERSON><PERSON><PERSON>", "re", "time", "collections", "datetime", "typing", "cryptography", "builtins", "os", "sys", "string", "inspect", "traceback", "pprint", "types", "itertools", "warnings", "copy", "html", "_collections_abc", "_frozen_importlib", "_hashlib", "_random", "_typeshed", "abc", "cryptography.hazmat", "cryptography.x509.name", "enum", "moto.core.base_backend", "moto.core.common_models", "moto.core.exceptions", "moto.iotdata", "moto.moto_api", "moto.moto_api._internal.moto_random", "moto.utilities", "random", "typing_extensions", "uuid", "werkzeug", "werkzeug.exceptions"], "hash": "e8d91b8858d64e5240e71570d353f337b5cbccf3", "id": "moto.iot.models", "ignore_all": true, "interface_hash": "bc998f4d820f34b39335c8e9eb9e2b091b447195", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/iot/models.py", "plugin_data": null, "size": 72044, "suppressed": [], "version_id": "1.15.0"}