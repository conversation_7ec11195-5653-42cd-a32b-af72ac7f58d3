{".class": "MypyFile", "_fullname": "moto.mediaconnect.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Flow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.mediaconnect.models.Flow", "name": "Flow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.Flow", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.mediaconnect.models", "mro": ["moto.mediaconnect.models.Flow", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "account_id", "region_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.Flow.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "account_id", "region_name", "kwargs"], "arg_types": ["moto.mediaconnect.models.Flow", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Flow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_previous_status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.mediaconnect.models.Flow._previous_status", "name": "_previous_status", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "availability_zone": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.mediaconnect.models.Flow.availability_zone", "name": "availability_zone", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.mediaconnect.models.Flow.description", "name": "description", "type": "builtins.str"}}, "egress_ip": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.mediaconnect.models.Flow.egress_ip", "name": "egress_ip", "type": "builtins.str"}}, "entitlements": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.mediaconnect.models.Flow.entitlements", "name": "entitlements", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "flow_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.mediaconnect.models.Flow.flow_arn", "name": "flow_arn", "type": "builtins.str"}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.mediaconnect.models.Flow.id", "name": "id", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.mediaconnect.models.Flow.name", "name": "name", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "outputs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.mediaconnect.models.Flow.outputs", "name": "outputs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "resolve_transient_states": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.Flow.resolve_transient_states", "name": "resolve_transient_states", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.mediaconnect.models.Flow"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_transient_states of Flow", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "source": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.mediaconnect.models.Flow.source", "name": "source", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "source_failover_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.mediaconnect.models.Flow.source_failover_config", "name": "source_failover_config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "sources": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.mediaconnect.models.Flow.sources", "name": "sources", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.mediaconnect.models.Flow.status", "name": "status", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "include"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.Flow.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "include"], "arg_types": ["moto.mediaconnect.models.Flow", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of Flow", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vpc_interfaces": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.mediaconnect.models.Flow.vpc_interfaces", "name": "vpc_interfaces", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.mediaconnect.models.Flow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.mediaconnect.models.Flow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MediaConnectBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.mediaconnect.models.MediaConnectBackend", "name": "MediaConnectBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.mediaconnect.models", "mro": ["moto.mediaconnect.models.MediaConnectBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MediaConnectBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_entitlement_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "entitlement", "entitlement_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend._add_entitlement_details", "name": "_add_entitlement_details", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "entitlement", "entitlement_id"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_entitlement_details of MediaConnectBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_source_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "source", "flow_id", "ingest_ip"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend._add_source_details", "name": "_add_source_details", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "source", "flow_id", "ingest_ip"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_source_details of MediaConnectBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_flow_add_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "flow"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend._create_flow_add_details", "name": "_create_flow_add_details", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "flow"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "moto.mediaconnect.models.Flow"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_flow_add_details of MediaConnectBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_flows": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.mediaconnect.models.MediaConnectBackend._flows", "name": "_flows", "type": {".class": "Instance", "args": ["builtins.str", "moto.mediaconnect.models.Flow"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "add_flow_outputs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "outputs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.add_flow_outputs", "name": "add_flow_outputs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "outputs"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_flow_outputs of MediaConnectBackend", "ret_type": "moto.mediaconnect.models.Flow", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_flow_sources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "sources"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.add_flow_sources", "name": "add_flow_sources", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "sources"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_flow_sources of MediaConnectBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_flow_vpc_interfaces": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "vpc_interfaces"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.add_flow_vpc_interfaces", "name": "add_flow_vpc_interfaces", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "vpc_interfaces"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_flow_vpc_interfaces of MediaConnectBackend", "ret_type": "moto.mediaconnect.models.Flow", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "availability_zone", "entitlements", "name", "outputs", "source", "source_failover_config", "sources", "vpc_interfaces"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.create_flow", "name": "create_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "availability_zone", "entitlements", "name", "outputs", "source", "source_failover_config", "sources", "vpc_interfaces"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_flow of MediaConnectBackend", "ret_type": "moto.mediaconnect.models.Flow", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "flow_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.delete_flow", "name": "delete_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "flow_arn"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_flow of MediaConnectBackend", "ret_type": "moto.mediaconnect.models.Flow", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "flow_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.describe_flow", "name": "describe_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "flow_arn"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_flow of MediaConnectBackend", "ret_type": "moto.mediaconnect.models.Flow", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "grant_flow_entitlements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "entitlements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.grant_flow_entitlements", "name": "grant_flow_entitlements", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "entitlements"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "grant_flow_entitlements of MediaConnectBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_flows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_results"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.list_flows", "name": "list_flows", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_results"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_flows of MediaConnectBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_tags_for_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.list_tags_for_resource", "name": "list_tags_for_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_tags_for_resource of MediaConnectBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_flow_output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "output_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.remove_flow_output", "name": "remove_flow_output", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "output_name"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_flow_output of MediaConnectBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_flow_vpc_interface": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "vpc_interface_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.remove_flow_vpc_interface", "name": "remove_flow_vpc_interface", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "vpc_interface_name"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_flow_vpc_interface of MediaConnectBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "revoke_flow_entitlement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "entitlement_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.revoke_flow_entitlement", "name": "revoke_flow_entitlement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "flow_arn", "entitlement_arn"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "revoke_flow_entitlement of MediaConnectBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "flow_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.start_flow", "name": "start_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "flow_arn"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_flow of MediaConnectBackend", "ret_type": "moto.mediaconnect.models.Flow", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stop_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "flow_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.stop_flow", "name": "stop_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "flow_arn"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop_flow of MediaConnectBackend", "ret_type": "moto.mediaconnect.models.Flow", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tag_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arn", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.tag_resource", "name": "tag_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "resource_arn", "tags"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tag_resource of MediaConnectBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tagger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.mediaconnect.models.MediaConnectBackend.tagger", "name": "tagger", "type": "moto.utilities.tagging_service.TaggingService"}}, "update_flow_entitlement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "flow_arn", "entitlement_arn", "description", "encryption", "entitlement_status", "name", "subscribers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.update_flow_entitlement", "name": "update_flow_entitlement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "flow_arn", "entitlement_arn", "description", "encryption", "entitlement_status", "name", "subscribers"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_flow_entitlement of MediaConnectBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_flow_output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "flow_arn", "output_arn", "cidr_allow_list", "description", "destination", "encryption", "max_latency", "media_stream_output_configuration", "min_latency", "port", "protocol", "remote_id", "sender_control_port", "sender_ip_address", "smoothing_latency", "stream_id", "vpc_interface_attachment"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.update_flow_output", "name": "update_flow_output", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "flow_arn", "output_arn", "cidr_allow_list", "description", "destination", "encryption", "max_latency", "media_stream_output_configuration", "min_latency", "port", "protocol", "remote_id", "sender_control_port", "sender_ip_address", "smoothing_latency", "stream_id", "vpc_interface_attachment"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", "builtins.str", "builtins.str", "builtins.int", "builtins.str", "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_flow_output of MediaConnectBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_flow_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "flow_arn", "source_arn", "decryption", "description", "entitlement_arn", "ingest_port", "max_bitrate", "max_latency", "max_sync_buffer", "media_stream_source_configurations", "min_latency", "protocol", "sender_control_port", "sender_ip_address", "stream_id", "vpc_interface_name", "whitelist_cidr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.mediaconnect.models.MediaConnectBackend.update_flow_source", "name": "update_flow_source", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "flow_arn", "source_arn", "decryption", "description", "entitlement_arn", "ingest_port", "max_bitrate", "max_latency", "max_sync_buffer", "media_stream_source_configurations", "min_latency", "protocol", "sender_control_port", "sender_ip_address", "stream_id", "vpc_interface_name", "whitelist_cidr"], "arg_types": ["moto.mediaconnect.models.MediaConnectBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.str", "builtins.int", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_flow_source of MediaConnectBackend", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.mediaconnect.models.MediaConnectBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.mediaconnect.models.MediaConnectBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotFoundException": {".class": "SymbolTableNode", "cross_ref": "moto.mediaconnect.exceptions.NotFoundException", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "TaggingService": {".class": "SymbolTableNode", "cross_ref": "moto.utilities.tagging_service.TaggingService", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.mediaconnect.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.mediaconnect.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.mediaconnect.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.mediaconnect.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.mediaconnect.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.mediaconnect.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "mediaconnect_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.mediaconnect.models.mediaconnect_backends", "name": "mediaconnect_backends", "type": {".class": "Instance", "args": ["moto.mediaconnect.models.MediaConnectBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/mediaconnect/models.py"}