{".class": "MypyFile", "_fullname": "moto.iot.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "CertificateStateException": {".class": "SymbolTableNode", "cross_ref": "moto.iot.exceptions.CertificateStateException", "kind": "Gdef"}, "DeleteConflictException": {".class": "SymbolTableNode", "cross_ref": "moto.iot.exceptions.DeleteConflictException", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FakeCaCertificate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.iot.models.FakeCertificate"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iot.models.FakeCaCertificate", "name": "FakeCaCertificate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeCaCertificate", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.iot.models", "mro": ["moto.iot.models.FakeCaCertificate", "moto.iot.models.FakeCertificate", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "ca_certificate", "status", "account_id", "region_name", "registration_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeCaCertificate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "ca_certificate", "status", "account_id", "region_name", "registration_config"], "arg_types": ["moto.iot.models.FakeCaCertificate", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeCaCertificate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "registration_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeCaCertificate.registration_config", "name": "registration_config", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iot.models.FakeCaCertificate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iot.models.FakeCaCertificate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeCertificate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iot.models.FakeCertificate", "name": "FakeCertificate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeCertificate", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.iot.models", "mro": ["moto.iot.models.FakeCertificate", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "certificate_pem", "status", "account_id", "region_name", "ca_certificate_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeCertificate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "certificate_pem", "status", "account_id", "region_name", "ca_certificate_id"], "arg_types": ["moto.iot.models.FakeCertificate", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeCertificate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeCertificate.arn", "name": "arn", "type": "builtins.str"}}, "ca_certificate_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeCertificate.ca_certificate_id", "name": "ca_certificate_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "certificate_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeCertificate.certificate_id", "name": "certificate_id", "type": "builtins.str"}}, "certificate_pem": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeCertificate.certificate_pem", "name": "certificate_pem", "type": "builtins.str"}}, "creation_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeCertificate.creation_date", "name": "creation_date", "type": "builtins.float"}}, "last_modified_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeCertificate.last_modified_date", "name": "last_modified_date", "type": "builtins.float"}}, "owner": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeCertificate.owner", "name": "owner", "type": "builtins.str"}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeCertificate.status", "name": "status", "type": "builtins.str"}}, "to_description_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeCertificate.to_description_dict", "name": "to_description_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakeCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_description_dict of FakeCertificate", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeCertificate.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakeCertificate"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeCertificate", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transfer_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.FakeCertificate.transfer_data", "name": "transfer_data", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "validity_not_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeCertificate.validity_not_after", "name": "validity_not_after", "type": "builtins.float"}}, "validity_not_before": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeCertificate.validity_not_before", "name": "validity_not_before", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iot.models.FakeCertificate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iot.models.FakeCertificate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeDomainConfiguration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iot.models.FakeDomainConfiguration", "name": "FakeDomainConfiguration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeDomainConfiguration", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.iot.models", "mro": ["moto.iot.models.FakeDomainConfiguration", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "domain_configuration_name", "domain_name", "server_certificate_arns", "domain_configuration_status", "service_type", "authorizer_config", "domain_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeDomainConfiguration.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region_name", "domain_configuration_name", "domain_name", "server_certificate_arns", "domain_configuration_status", "service_type", "authorizer_config", "domain_type"], "arg_types": ["moto.iot.models.FakeDomainConfiguration", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeDomainConfiguration", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "authorizer_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeDomainConfiguration.authorizer_config", "name": "authorizer_config", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "domain_configuration_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeDomainConfiguration.domain_configuration_arn", "name": "domain_configuration_arn", "type": "builtins.str"}}, "domain_configuration_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeDomainConfiguration.domain_configuration_name", "name": "domain_configuration_name", "type": "builtins.str"}}, "domain_configuration_status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeDomainConfiguration.domain_configuration_status", "name": "domain_configuration_status", "type": "builtins.str"}}, "domain_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeDomainConfiguration.domain_name", "name": "domain_name", "type": "builtins.str"}}, "domain_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeDomainConfiguration.domain_type", "name": "domain_type", "type": "builtins.str"}}, "last_status_change_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeDomainConfiguration.last_status_change_date", "name": "last_status_change_date", "type": "builtins.float"}}, "server_certificates": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeDomainConfiguration.server_certificates", "name": "server_certificates", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "service_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeDomainConfiguration.service_type", "name": "service_type", "type": "builtins.str"}}, "to_description_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeDomainConfiguration.to_description_dict", "name": "to_description_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakeDomainConfiguration"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_description_dict of FakeDomainConfiguration", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeDomainConfiguration.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakeDomainConfiguration"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeDomainConfiguration", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iot.models.FakeDomainConfiguration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iot.models.FakeDomainConfiguration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeEndpoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iot.models.FakeEndpoint", "name": "FakeEndpoint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeEndpoint", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.iot.models", "mro": ["moto.iot.models.FakeEndpoint", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "endpoint_type", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeEndpoint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "endpoint_type", "region_name"], "arg_types": ["moto.iot.models.FakeEndpoint", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeEndpoint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endpoint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeEndpoint.endpoint", "name": "endpoint", "type": "builtins.str"}}, "endpoint_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeEndpoint.endpoint_type", "name": "endpoint_type", "type": "builtins.str"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeEndpoint.region_name", "name": "region_name", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeEndpoint.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakeEndpoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeEndpoint", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_get_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeEndpoint.to_get_dict", "name": "to_get_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakeEndpoint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_get_dict of FakeEndpoint", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iot.models.FakeEndpoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iot.models.FakeEndpoint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeJob": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iot.models.FakeJob", "name": "<PERSON>ake<PERSON>ob", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeJob", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.iot.models", "mro": ["moto.iot.models.FakeJob", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "JOB_ID_REGEX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.iot.models.FakeJob.JOB_ID_REGEX", "name": "JOB_ID_REGEX", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "JOB_ID_REGEX_PATTERN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.iot.models.FakeJob.JOB_ID_REGEX_PATTERN", "name": "JOB_ID_REGEX_PATTERN", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "job_id", "targets", "document_source", "document", "description", "presigned_url_config", "target_selection", "job_executions_rollout_config", "document_parameters", "account_id", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeJob.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "job_id", "targets", "document_source", "document", "description", "presigned_url_config", "target_selection", "job_executions_rollout_config", "document_parameters", "account_id", "region_name"], "arg_types": ["moto.iot.models.FakeJob", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeJob", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_job_id_matcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "regex", "argument"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeJob._job_id_matcher", "name": "_job_id_matcher", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "regex", "argument"], "arg_types": ["moto.iot.models.FakeJob", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_job_id_matcher of FakeJob", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.account_id", "name": "account_id", "type": "builtins.str"}}, "comment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.FakeJob.comment", "name": "comment", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "completed_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.completed_at", "name": "completed_at", "type": {".class": "NoneType"}}}, "created_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.created_at", "name": "created_at", "type": "builtins.float"}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.description", "name": "description", "type": "builtins.str"}}, "document": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.document", "name": "document", "type": "builtins.str"}}, "document_parameters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.document_parameters", "name": "document_parameters", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "document_source": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.document_source", "name": "document_source", "type": "builtins.str"}}, "force": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.force", "name": "force", "type": "builtins.bool"}}, "job_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.job_arn", "name": "job_arn", "type": "builtins.str"}}, "job_executions_rollout_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.job_executions_rollout_config", "name": "job_executions_rollout_config", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "job_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.job_id", "name": "job_id", "type": "builtins.str"}}, "job_process_details": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.job_process_details", "name": "job_process_details", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "last_updated_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.last_updated_at", "name": "last_updated_at", "type": "builtins.float"}}, "presigned_url_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.presigned_url_config", "name": "presigned_url_config", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "reason_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.FakeJob.reason_code", "name": "reason_code", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.region_name", "name": "region_name", "type": "builtins.str"}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.status", "name": "status", "type": "builtins.str"}}, "target_selection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.target_selection", "name": "target_selection", "type": "builtins.str"}}, "targets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJob.targets", "name": "targets", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeJob.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakeJob"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeJob", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iot.models.FakeJob.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iot.models.FakeJob", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeJobExecution": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iot.models.FakeJobExecution", "name": "FakeJobExecution", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeJobExecution", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.iot.models", "mro": ["moto.iot.models.FakeJobExecution", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "job_id", "thing_arn", "status", "force_canceled", "status_details_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeJobExecution.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "job_id", "thing_arn", "status", "force_canceled", "status_details_map"], "arg_types": ["moto.iot.models.FakeJobExecution", "builtins.str", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeJobExecution", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "approximate_seconds_before_time_out": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJobExecution.approximate_seconds_before_time_out", "name": "approximate_seconds_before_time_out", "type": "builtins.int"}}, "execution_number": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJobExecution.execution_number", "name": "execution_number", "type": "builtins.int"}}, "force_canceled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJobExecution.force_canceled", "name": "force_canceled", "type": "builtins.bool"}}, "job_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJobExecution.job_id", "name": "job_id", "type": "builtins.str"}}, "last_updated_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJobExecution.last_updated_at", "name": "last_updated_at", "type": "builtins.float"}}, "queued_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJobExecution.queued_at", "name": "queued_at", "type": "builtins.float"}}, "started_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJobExecution.started_at", "name": "started_at", "type": "builtins.float"}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJobExecution.status", "name": "status", "type": "builtins.str"}}, "status_details_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJobExecution.status_details_map", "name": "status_details_map", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "thing_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJobExecution.thing_arn", "name": "thing_arn", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeJobExecution.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakeJobExecution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeJobExecution", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_get_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeJobExecution.to_get_dict", "name": "to_get_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakeJobExecution"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_get_dict of FakeJobExecution", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version_number": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeJobExecution.version_number", "name": "version_number", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iot.models.FakeJobExecution.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iot.models.FakeJobExecution", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakePolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iot.models.FakePolicy", "name": "FakePolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakePolicy", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.iot.models", "mro": ["moto.iot.models.FakePolicy", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "document", "account_id", "region_name", "default_version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakePolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "document", "account_id", "region_name", "default_version_id"], "arg_types": ["moto.iot.models.FakePolicy", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakePolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_max_version_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakePolicy._max_version_id", "name": "_max_version_id", "type": "builtins.int"}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakePolicy.arn", "name": "arn", "type": "builtins.str"}}, "default_version_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakePolicy.default_version_id", "name": "default_version_id", "type": "builtins.str"}}, "document": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakePolicy.document", "name": "document", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakePolicy.name", "name": "name", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakePolicy.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakePolicy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakePolicy", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_dict_at_creation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakePolicy.to_dict_at_creation", "name": "to_dict_at_creation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakePolicy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict_at_creation of FakePolicy", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_get_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakePolicy.to_get_dict", "name": "to_get_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakePolicy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_get_dict of FakePolicy", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "versions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakePolicy.versions", "name": "versions", "type": {".class": "Instance", "args": ["moto.iot.models.FakePolicyVersion"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iot.models.FakePolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iot.models.FakePolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakePolicyVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iot.models.FakePolicyVersion", "name": "FakePolicyVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakePolicyVersion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.iot.models", "mro": ["moto.iot.models.FakePolicyVersion", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "policy_name", "document", "is_default", "account_id", "region_name", "version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakePolicyVersion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "policy_name", "document", "is_default", "account_id", "region_name", "version_id"], "arg_types": ["moto.iot.models.FakePolicyVersion", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakePolicyVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_version_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakePolicyVersion._version_id", "name": "_version_id", "type": "builtins.int"}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakePolicyVersion.arn", "name": "arn", "type": "builtins.str"}}, "create_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakePolicyVersion.create_datetime", "name": "create_datetime", "type": "builtins.float"}}, "document": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakePolicyVersion.document", "name": "document", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "is_default": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakePolicyVersion.is_default", "name": "is_default", "type": "builtins.bool"}}, "last_modified_datetime": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakePolicyVersion.last_modified_datetime", "name": "last_modified_datetime", "type": "builtins.float"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakePolicyVersion.name", "name": "name", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakePolicyVersion.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakePolicyVersion"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakePolicyVersion", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_dict_at_creation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakePolicyVersion.to_dict_at_creation", "name": "to_dict_at_creation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakePolicyVersion"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict_at_creation of FakePolicyVersion", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_get_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakePolicyVersion.to_get_dict", "name": "to_get_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakePolicyVersion"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_get_dict of FakePolicyVersion", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.iot.models.FakePolicyVersion.version_id", "name": "version_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakePolicyVersion"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version_id of FakePolicyVersion", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.iot.models.FakePolicyVersion.version_id", "name": "version_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakePolicyVersion"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version_id of FakePolicyVersion", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iot.models.FakePolicyVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iot.models.FakePolicyVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeRule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iot.models.FakeRule", "name": "FakeRule", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeRule", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.iot.models", "mro": ["moto.iot.models.FakeRule", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "rule_name", "description", "created_at", "rule_disabled", "topic_pattern", "actions", "error_action", "sql", "aws_iot_sql_version", "account_id", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeRule.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "rule_name", "description", "created_at", "rule_disabled", "topic_pattern", "actions", "error_action", "sql", "aws_iot_sql_version", "account_id", "region_name"], "arg_types": ["moto.iot.models.FakeRule", "builtins.str", "builtins.str", "builtins.int", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeRule", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeRule.account_id", "name": "account_id", "type": "builtins.str"}}, "actions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeRule.actions", "name": "actions", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeRule.arn", "name": "arn", "type": "builtins.str"}}, "aws_iot_sql_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeRule.aws_iot_sql_version", "name": "aws_iot_sql_version", "type": "builtins.str"}}, "created_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeRule.created_at", "name": "created_at", "type": "builtins.int"}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeRule.description", "name": "description", "type": "builtins.str"}}, "error_action": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeRule.error_action", "name": "error_action", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeRule.region_name", "name": "region_name", "type": "builtins.str"}}, "rule_disabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeRule.rule_disabled", "name": "rule_disabled", "type": "builtins.bool"}}, "rule_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeRule.rule_name", "name": "rule_name", "type": "builtins.str"}}, "sql": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeRule.sql", "name": "sql", "type": "builtins.str"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeRule.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakeRule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeRule", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_get_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeRule.to_get_dict", "name": "to_get_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakeRule"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_get_dict of FakeRule", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "topic_pattern": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeRule.topic_pattern", "name": "topic_pattern", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iot.models.FakeRule.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iot.models.FakeRule", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeShadow": {".class": "SymbolTableNode", "cross_ref": "moto.iotdata.models.FakeShadow", "kind": "Gdef"}, "FakeThing": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iot.models.FakeThing", "name": "FakeThing", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeThing", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.iot.models", "mro": ["moto.iot.models.FakeThing", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "thing_name", "thing_type", "attributes", "account_id", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeThing.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "thing_name", "thing_type", "attributes", "account_id", "region_name"], "arg_types": ["moto.iot.models.FakeThing", "builtins.str", {".class": "UnionType", "items": ["moto.iot.models.FakeThingType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeThing", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThing.arn", "name": "arn", "type": "builtins.str"}}, "attributes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThing.attributes", "name": "attributes", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query_string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeThing.matches", "name": "matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query_string"], "arg_types": ["moto.iot.models.FakeThing", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches of FakeThing", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThing.region_name", "name": "region_name", "type": "builtins.str"}}, "thing_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThing.thing_id", "name": "thing_id", "type": "builtins.str"}}, "thing_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThing.thing_name", "name": "thing_name", "type": "builtins.str"}}, "thing_shadows": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.FakeThing.thing_shadows", "name": "thing_shadows", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "moto.iotdata.models.FakeShadow"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "thing_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThing.thing_type", "name": "thing_type", "type": {".class": "UnionType", "items": ["moto.iot.models.FakeThingType", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "include_default_client_id", "include_connectivity", "include_thing_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeThing.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "include_default_client_id", "include_connectivity", "include_thing_id"], "arg_types": ["moto.iot.models.FakeThing", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeThing", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThing.version", "name": "version", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iot.models.FakeThing.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iot.models.FakeThing", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeThingGroup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iot.models.FakeThingGroup", "name": "FakeThingGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeThingGroup", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.iot.models", "mro": ["moto.iot.models.FakeThingGroup", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "thing_group_name", "parent_group_name", "thing_group_properties", "account_id", "region_name", "thing_groups"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeThingGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "thing_group_name", "parent_group_name", "thing_group_properties", "account_id", "region_name", "thing_groups"], "arg_types": ["moto.iot.models.FakeThingGroup", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "moto.iot.models.FakeThingGroup"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeThingGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingGroup.account_id", "name": "account_id", "type": "builtins.str"}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingGroup.arn", "name": "arn", "type": "builtins.str"}}, "metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.FakeThingGroup.metadata", "name": "metadata", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "parent_group_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingGroup.parent_group_name", "name": "parent_group_name", "type": "builtins.str"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingGroup.region_name", "name": "region_name", "type": "builtins.str"}}, "thing_group_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingGroup.thing_group_id", "name": "thing_group_id", "type": "builtins.str"}}, "thing_group_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingGroup.thing_group_name", "name": "thing_group_name", "type": "builtins.str"}}, "thing_group_properties": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingGroup.thing_group_properties", "name": "thing_group_properties", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "things": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.FakeThingGroup.things", "name": "things", "type": {".class": "Instance", "args": ["builtins.str", "moto.iot.models.FakeThing"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeThingGroup.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakeThingGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeThingGroup", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingGroup.version", "name": "version", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iot.models.FakeThingGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iot.models.FakeThingGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeThingType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iot.models.FakeThingType", "name": "FakeThingType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeThingType", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.iot.models", "mro": ["moto.iot.models.FakeThingType", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "thing_type_name", "thing_type_properties", "account_id", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeThingType.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "thing_type_name", "thing_type_properties", "account_id", "region_name"], "arg_types": ["moto.iot.models.FakeThingType", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeThingType", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingType.account_id", "name": "account_id", "type": "builtins.str"}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingType.arn", "name": "arn", "type": "builtins.str"}}, "metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingType.metadata", "name": "metadata", "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingType.region_name", "name": "region_name", "type": "builtins.str"}}, "thing_type_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingType.thing_type_id", "name": "thing_type_id", "type": "builtins.str"}}, "thing_type_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingType.thing_type_name", "name": "thing_type_name", "type": "builtins.str"}}, "thing_type_properties": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.iot.models.FakeThingType.thing_type_properties", "name": "thing_type_properties", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.FakeThingType.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.FakeThingType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeThingType", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iot.models.FakeThingType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iot.models.FakeThingType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidRequestException": {".class": "SymbolTableNode", "cross_ref": "moto.iot.exceptions.InvalidRequestException", "kind": "Gdef"}, "InvalidStateTransitionException": {".class": "SymbolTableNode", "cross_ref": "moto.iot.exceptions.InvalidStateTransitionException", "kind": "Gdef"}, "IoTBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.iot.models.IoTBackend", "name": "IoTBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.iot.models", "mro": ["moto.iot.models.IoTBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__raise_if_certificate_already_exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "certificate_id", "certificate_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.__raise_if_certificate_already_exists", "name": "__raise_if_certificate_already_exists", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "certificate_id", "certificate_arn"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__raise_if_certificate_already_exists of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_ca_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ca_certificate_pem"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend._find_ca_certificate", "name": "_find_ca_certificate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ca_certificate_pem"], "arg_types": ["moto.iot.models.IoTBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_ca_certificate of IoTBackend", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_certificate_pem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "domain_name", "subject", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend._generate_certificate_pem", "name": "_generate_certificate_pem", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "domain_name", "subject", "key"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "cryptography.x509.name.Name", {".class": "UnionType", "items": ["cryptography.hazmat.primitives.asymmetric.rsa.RSAPrivateKey", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_certificate_pem of IoTBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_principal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "principal_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend._get_principal", "name": "_get_principal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "principal_arn"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_principal of IoTBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_identify_thing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_name", "thing_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend._identify_thing", "name": "_identify_thing", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_name", "thing_arn"], "arg_types": ["moto.iot.models.IoTBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_identify_thing of IoTBackend", "ret_type": "moto.iot.models.FakeThing", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_identify_thing_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_group_name", "thing_group_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend._identify_thing_group", "name": "_identify_thing_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_group_name", "thing_group_arn"], "arg_types": ["moto.iot.models.IoTBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_identify_thing_group of IoTBackend", "ret_type": "moto.iot.models.FakeThingGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validation_delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "cert", "force_delete"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend._validation_delete", "name": "_validation_delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "cert", "force_delete"], "arg_types": ["moto.iot.models.IoTBackend", "moto.iot.models.FakeCertificate", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validation_delete of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_thing_to_thing_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "thing_group_name", "thing_group_arn", "thing_name", "thing_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.add_thing_to_thing_group", "name": "add_thing_to_thing_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "thing_group_name", "thing_group_arn", "thing_name", "thing_arn"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_thing_to_thing_group of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attach_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.attach_policy", "name": "attach_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "target"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attach_policy of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attach_principal_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "principal_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.attach_principal_policy", "name": "attach_principal_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "principal_arn"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attach_principal_policy of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attach_thing_principal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_name", "principal_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.attach_thing_principal", "name": "attach_thing_principal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_name", "principal_arn"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attach_thing_principal of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ca_certificates": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.ca_certificates", "name": "ca_certificates", "type": {".class": "Instance", "args": ["builtins.str", "moto.iot.models.FakeCaCertificate"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "cancel_job": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "job_id", "reason_code", "comment", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.cancel_job", "name": "cancel_job", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "job_id", "reason_code", "comment", "force"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancel_job of IoTBackend", "ret_type": "moto.iot.models.FakeJob", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cancel_job_execution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "job_id", "thing_name", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.cancel_job_execution", "name": "cancel_job_execution", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "job_id", "thing_name", "force"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancel_job_execution of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "certificates": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.certificates", "name": "certificates", "type": {".class": "Instance", "args": ["builtins.str", "moto.iot.models.FakeCertificate"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "create_certificate_from_csr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "csr", "set_as_active"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.create_certificate_from_csr", "name": "create_certificate_from_csr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "csr", "set_as_active"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_certificate_from_csr of IoTBackend", "ret_type": "moto.iot.models.FakeCertificate", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_domain_configuration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "domain_configuration_name", "domain_name", "server_certificate_arns", "authorizer_config", "service_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.create_domain_configuration", "name": "create_domain_configuration", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "domain_configuration_name", "domain_name", "server_certificate_arns", "authorizer_config", "service_type"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_domain_configuration of IoTBackend", "ret_type": "moto.iot.models.FakeDomainConfiguration", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_job": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "job_id", "targets", "document_source", "document", "description", "presigned_url_config", "target_selection", "job_executions_rollout_config", "document_parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.create_job", "name": "create_job", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "job_id", "targets", "document_source", "document", "description", "presigned_url_config", "target_selection", "job_executions_rollout_config", "document_parameters"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_job of IoTBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_keys_and_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "set_as_active"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.create_keys_and_certificate", "name": "create_keys_and_certificate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "set_as_active"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_keys_and_certificate of IoTBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": ["moto.iot.models.FakeCertificate", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "policy_document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.create_policy", "name": "create_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "policy_document"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_policy of IoTBackend", "ret_type": "moto.iot.models.FakePolicy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_policy_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "policy_name", "policy_document", "set_as_default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.create_policy_version", "name": "create_policy_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "policy_name", "policy_document", "set_as_default"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_policy_version of IoTBackend", "ret_type": "moto.iot.models.FakePolicyVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_thing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "thing_name", "thing_type_name", "attribute_payload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.create_thing", "name": "create_thing", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "thing_name", "thing_type_name", "attribute_payload"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_thing of IoTBackend", "ret_type": "moto.iot.models.FakeThing", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_thing_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "thing_group_name", "parent_group_name", "thing_group_properties"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.create_thing_group", "name": "create_thing_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "thing_group_name", "parent_group_name", "thing_group_properties"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_thing_group of IoTBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_thing_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_type_name", "thing_type_properties"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.create_thing_type", "name": "create_thing_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_type_name", "thing_type_properties"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_thing_type of IoTBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_topic_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "rule_name", "sql", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.create_topic_rule", "name": "create_topic_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "rule_name", "sql", "kwargs"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_topic_rule of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_vpc_endpoint_service": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["service_region", "zones"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.iot.models.IoTBackend.default_vpc_endpoint_service", "name": "default_vpc_endpoint_service", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["service_region", "zones"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_vpc_endpoint_service of IoTBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.default_vpc_endpoint_service", "name": "default_vpc_endpoint_service", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["service_region", "zones"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_vpc_endpoint_service of IoTBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_ca_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "certificate_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.delete_ca_certificate", "name": "delete_ca_certificate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "certificate_id"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_ca_certificate of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "certificate_id", "force_delete"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.delete_certificate", "name": "delete_certificate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "certificate_id", "force_delete"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_certificate of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_domain_configuration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "domain_configuration_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.delete_domain_configuration", "name": "delete_domain_configuration", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "domain_configuration_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_domain_configuration of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_job": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "job_id", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.delete_job", "name": "delete_job", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "job_id", "force"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_job of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_job_execution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "job_id", "thing_name", "execution_number", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.delete_job_execution", "name": "delete_job_execution", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "job_id", "thing_name", "execution_number", "force"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_job_execution of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "policy_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.delete_policy", "name": "delete_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "policy_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_policy of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_policy_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.delete_policy_version", "name": "delete_policy_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "version_id"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_policy_version of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_thing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thing_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.delete_thing", "name": "delete_thing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "thing_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_thing of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_thing_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thing_group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.delete_thing_group", "name": "delete_thing_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "thing_group_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_thing_group of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_thing_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thing_type_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.delete_thing_type", "name": "delete_thing_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "thing_type_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_thing_type of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_topic_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rule_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.delete_topic_rule", "name": "delete_topic_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rule_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_topic_rule of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deprecate_thing_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_type_name", "undo_deprecate"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.deprecate_thing_type", "name": "deprecate_thing_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_type_name", "undo_deprecate"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deprecate_thing_type of IoTBackend", "ret_type": "moto.iot.models.FakeThingType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_ca_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "certificate_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.describe_ca_certificate", "name": "describe_ca_certificate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "certificate_id"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_ca_certificate of IoTBackend", "ret_type": "moto.iot.models.FakeCaCertificate", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "certificate_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.describe_certificate", "name": "describe_certificate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "certificate_id"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_certificate of IoTBackend", "ret_type": "moto.iot.models.FakeCertificate", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_domain_configuration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "domain_configuration_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.describe_domain_configuration", "name": "describe_domain_configuration", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "domain_configuration_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_domain_configuration of IoTBackend", "ret_type": "moto.iot.models.FakeDomainConfiguration", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "endpoint_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.describe_endpoint", "name": "describe_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "endpoint_type"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_endpoint of IoTBackend", "ret_type": "moto.iot.models.FakeEndpoint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_job": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "job_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.describe_job", "name": "describe_job", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "job_id"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_job of IoTBackend", "ret_type": "moto.iot.models.FakeJob", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_job_execution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "job_id", "thing_name", "execution_number"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.describe_job_execution", "name": "describe_job_execution", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "job_id", "thing_name", "execution_number"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_job_execution of IoTBackend", "ret_type": "moto.iot.models.FakeJobExecution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_thing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thing_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.describe_thing", "name": "describe_thing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "thing_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_thing of IoTBackend", "ret_type": "moto.iot.models.FakeThing", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_thing_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thing_group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.describe_thing_group", "name": "describe_thing_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "thing_group_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_thing_group of IoTBackend", "ret_type": "moto.iot.models.FakeThingGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_thing_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thing_type_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.describe_thing_type", "name": "describe_thing_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "thing_type_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_thing_type of IoTBackend", "ret_type": "moto.iot.models.FakeThingType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detach_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.detach_policy", "name": "detach_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "target"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detach_policy of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detach_principal_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "principal_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.detach_principal_policy", "name": "detach_principal_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "principal_arn"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detach_principal_policy of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detach_thing_principal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_name", "principal_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.detach_thing_principal", "name": "detach_thing_principal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_name", "principal_arn"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detach_thing_principal of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "disable_topic_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rule_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.disable_topic_rule", "name": "disable_topic_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rule_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disable_topic_rule of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "domain_configurations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.domain_configurations", "name": "domain_configurations", "type": {".class": "Instance", "args": ["builtins.str", "moto.iot.models.FakeDomainConfiguration"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "enable_topic_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rule_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.enable_topic_rule", "name": "enable_topic_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rule_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_topic_rule of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "endpoint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.endpoint", "name": "endpoint", "type": {".class": "UnionType", "items": ["moto.iot.models.FakeEndpoint", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get_job_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "job_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.get_job_document", "name": "get_job_document", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "job_id"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_job_document of IoTBackend", "ret_type": "moto.iot.models.FakeJob", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "policy_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.get_policy", "name": "get_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "policy_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_policy of IoTBackend", "ret_type": "moto.iot.models.FakePolicy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_policy_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.get_policy_version", "name": "get_policy_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "version_id"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_policy_version of IoTBackend", "ret_type": "moto.iot.models.FakePolicyVersion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_registration_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.get_registration_code", "name": "get_registration_code", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.IoTBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_registration_code of IoTBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_topic_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rule_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.get_topic_rule", "name": "get_topic_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rule_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_topic_rule of IoTBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "job_executions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.job_executions", "name": "job_executions", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "moto.iot.models.FakeJobExecution"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "jobs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.jobs", "name": "jobs", "type": {".class": "Instance", "args": ["builtins.str", "moto.iot.models.FakeJob"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_attached_policies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_attached_policies", "name": "list_attached_policies", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "target"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_attached_policies of IoTBackend", "ret_type": {".class": "Instance", "args": ["moto.iot.models.FakePolicy"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_certificates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_certificates", "name": "list_certificates", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.IoTBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_certificates of IoTBackend", "ret_type": {".class": "Instance", "args": ["moto.iot.models.FakeCertificate"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_certificates_by_ca": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ca_certificate_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_certificates_by_ca", "name": "list_certificates_by_ca", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ca_certificate_id"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_certificates_by_ca of IoTBackend", "ret_type": {".class": "Instance", "args": ["moto.iot.models.FakeCertificate"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_domain_configurations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_domain_configurations", "name": "list_domain_configurations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.IoTBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_domain_configurations of IoTBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_job_executions_for_job": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "job_id", "status", "max_results", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_job_executions_for_job", "name": "list_job_executions_for_job", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "job_id", "status", "max_results", "token"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_job_executions_for_job of IoTBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_job_executions_for_thing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_name", "status"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.iot.models.IoTBackend.list_job_executions_for_thing", "name": "list_job_executions_for_thing", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "thing_name", "status"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_job_executions_for_thing of IoTBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.list_job_executions_for_thing", "name": "list_job_executions_for_thing", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "list_jobs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "max_results", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_jobs", "name": "list_jobs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "max_results", "token"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_jobs of IoTBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_policies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_policies", "name": "list_policies", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.IoTBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_policies of IoTBackend", "ret_type": {".class": "Instance", "args": ["moto.iot.models.FakePolicy"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_policy_principals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "policy_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_policy_principals", "name": "list_policy_principals", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "policy_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_policy_principals of IoTBackend", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_policy_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "policy_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_policy_versions", "name": "list_policy_versions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "policy_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_policy_versions of IoTBackend", "ret_type": {".class": "Instance", "args": ["moto.iot.models.FakePolicyVersion"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_principal_policies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "principal_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_principal_policies", "name": "list_principal_policies", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "principal_arn"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_principal_policies of IoTBackend", "ret_type": {".class": "Instance", "args": ["moto.iot.models.FakePolicy"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_principal_things": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "principal_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_principal_things", "name": "list_principal_things", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "principal_arn"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_principal_things of IoTBackend", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_targets_for_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "policy_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_targets_for_policy", "name": "list_targets_for_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "policy_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_targets_for_policy of IoTBackend", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_thing_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "parent_group", "name_prefix_filter", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_thing_groups", "name": "list_thing_groups", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "parent_group", "name_prefix_filter", "recursive"], "arg_types": ["moto.iot.models.IoTBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_thing_groups of IoTBackend", "ret_type": {".class": "Instance", "args": ["moto.iot.models.FakeThingGroup"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_thing_groups_for_thing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thing_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_thing_groups_for_thing", "name": "list_thing_groups_for_thing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "thing_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_thing_groups_for_thing of IoTBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_thing_principals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thing_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_thing_principals", "name": "list_thing_principals", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "thing_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_thing_principals of IoTBackend", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_thing_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "thing_type_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_thing_types", "name": "list_thing_types", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "thing_type_name"], "arg_types": ["moto.iot.models.IoTBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_thing_types of IoTBackend", "ret_type": {".class": "Instance", "args": ["moto.iot.models.FakeThingType"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_things": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "attribute_name", "attribute_value", "thing_type_name", "max_results", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_things", "name": "list_things", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "attribute_name", "attribute_value", "thing_type_name", "max_results", "token"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_things of IoTBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["moto.iot.models.FakeThing"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_things_in_thing_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "thing_group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_things_in_thing_group", "name": "list_things_in_thing_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "thing_group_name"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_things_in_thing_group of IoTBackend", "ret_type": {".class": "Instance", "args": ["moto.iot.models.FakeThing"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_topic_rules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.list_topic_rules", "name": "list_topic_rules", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.iot.models.IoTBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_topic_rules of IoTBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "policies": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.policies", "name": "policies", "type": {".class": "Instance", "args": ["builtins.str", "moto.iot.models.FakePolicy"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "principal_policies": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.principal_policies", "name": "principal_policies", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "moto.iot.models.FakePolicy"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "principal_things": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.principal_things", "name": "principal_things", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", "moto.iot.models.FakeThing"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "register_ca_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ca_certificate", "set_as_active", "registration_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.register_ca_certificate", "name": "register_ca_certificate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ca_certificate", "set_as_active", "registration_config"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.bool", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_ca_certificate of IoTBackend", "ret_type": "moto.iot.models.FakeCaCertificate", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "certificate_pem", "ca_certificate_pem", "set_as_active", "status"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.register_certificate", "name": "register_certificate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "certificate_pem", "ca_certificate_pem", "set_as_active", "status"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_certificate of IoTBackend", "ret_type": "moto.iot.models.FakeCertificate", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_certificate_without_ca": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "certificate_pem", "status"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.register_certificate_without_ca", "name": "register_certificate_without_ca", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "certificate_pem", "status"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_certificate_without_ca of IoTBackend", "ret_type": "moto.iot.models.FakeCertificate", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_thing_from_thing_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "thing_group_name", "thing_group_arn", "thing_name", "thing_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.remove_thing_from_thing_group", "name": "remove_thing_from_thing_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "thing_group_name", "thing_group_arn", "thing_name", "thing_arn"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_thing_from_thing_group of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "replace_topic_rule": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "rule_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.replace_topic_rule", "name": "replace_topic_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "rule_name", "kwargs"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_topic_rule of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rules": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.rules", "name": "rules", "type": {".class": "Instance", "args": ["builtins.str", "moto.iot.models.FakeRule"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "search_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query_string"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.search_index", "name": "search_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query_string"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search_index of IoTBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_default_policy_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.set_default_policy_version", "name": "set_default_policy_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "policy_name", "version_id"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_default_policy_version of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "thing_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.thing_groups", "name": "thing_groups", "type": {".class": "Instance", "args": ["builtins.str", "moto.iot.models.FakeThingGroup"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "thing_types": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.thing_types", "name": "thing_types", "type": {".class": "Instance", "args": ["builtins.str", "moto.iot.models.FakeThingType"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "things": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.iot.models.IoTBackend.things", "name": "things", "type": {".class": "Instance", "args": ["builtins.str", "moto.iot.models.FakeThing"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "update_ca_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "certificate_id", "new_status", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.update_ca_certificate", "name": "update_ca_certificate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "certificate_id", "new_status", "config"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_ca_certificate of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_certificate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "certificate_id", "new_status"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.update_certificate", "name": "update_certificate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "certificate_id", "new_status"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_certificate of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_domain_configuration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "domain_configuration_name", "authorizer_config", "domain_configuration_status", "remove_authorizer_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.update_domain_configuration", "name": "update_domain_configuration", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "domain_configuration_name", "authorizer_config", "domain_configuration_status", "remove_authorizer_config"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_domain_configuration of IoTBackend", "ret_type": "moto.iot.models.FakeDomainConfiguration", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_thing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "thing_name", "thing_type_name", "attribute_payload", "remove_thing_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.update_thing", "name": "update_thing", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "thing_name", "thing_type_name", "attribute_payload", "remove_thing_type"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_thing of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_thing_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "thing_group_name", "thing_group_properties", "expected_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.update_thing_group", "name": "update_thing_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "thing_group_name", "thing_group_properties", "expected_version"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_thing_group of IoTBackend", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_thing_groups_for_thing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "thing_name", "thing_groups_to_add", "thing_groups_to_remove"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.iot.models.IoTBackend.update_thing_groups_for_thing", "name": "update_thing_groups_for_thing", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "thing_name", "thing_groups_to_add", "thing_groups_to_remove"], "arg_types": ["moto.iot.models.IoTBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_thing_groups_for_thing of IoTBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.iot.models.IoTBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.iot.models.IoTBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NameOID": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat._oid.NameOID", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "PAGINATION_MODEL": {".class": "SymbolTableNode", "cross_ref": "moto.iot.utils.PAGINATION_MODEL", "kind": "Gdef"}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef"}, "ResourceAlreadyExistsException": {".class": "SymbolTableNode", "cross_ref": "moto.iot.exceptions.ResourceAlreadyExistsException", "kind": "Gdef"}, "ResourceNotFoundException": {".class": "SymbolTableNode", "cross_ref": "moto.iot.exceptions.ResourceNotFoundException", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "ThingStillAttached": {".class": "SymbolTableNode", "cross_ref": "moto.iot.exceptions.ThingStillAttached", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "VersionConflictException": {".class": "SymbolTableNode", "cross_ref": "moto.iot.exceptions.VersionConflictException", "kind": "Gdef"}, "VersionsLimitExceededException": {".class": "SymbolTableNode", "cross_ref": "moto.iot.exceptions.VersionsLimitExceededException", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iot.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iot.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iot.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iot.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iot.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.iot.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "default_backend": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.backends.default_backend", "kind": "Gdef"}, "hashes": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.hashes", "kind": "Gdef"}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "iot_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.iot.models.iot_backends", "name": "iot_backends", "type": {".class": "Instance", "args": ["moto.iot.models.IoTBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "paginate": {".class": "SymbolTableNode", "cross_ref": "moto.utilities.paginator.paginate", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "rsa": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.asymmetric.rsa", "kind": "Gdef"}, "serialization": {".class": "SymbolTableNode", "cross_ref": "cryptography.hazmat.primitives.serialization", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "utcnow": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.utcnow", "kind": "Gdef"}, "x509": {".class": "SymbolTableNode", "cross_ref": "cryptography.x509", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/iot/models.py"}