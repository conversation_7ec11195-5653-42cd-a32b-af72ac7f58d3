{".class": "MypyFile", "_fullname": "moto.wafv2.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIGATEWAY_REGEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.wafv2.models.APIGATEWAY_REGEX", "name": "APIGATEWAY_REGEX", "type": "builtins.str"}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FakeWebACL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.wafv2.models.FakeWebACL", "name": "FakeWebACL", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.FakeWebACL", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.wafv2.models", "mro": ["moto.wafv2.models.FakeWebACL", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "arn", "wacl_id", "visibility_config", "default_action", "description", "rules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.FakeWebACL.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "arn", "wacl_id", "visibility_config", "default_action", "description", "rules"], "arg_types": ["moto.wafv2.models.FakeWebACL", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeWebACL", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.wafv2.models.FakeWebACL.arn", "name": "arn", "type": "builtins.str"}}, "capacity": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.wafv2.models.FakeWebACL.capacity", "name": "capacity", "type": "builtins.int"}}, "created_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.wafv2.models.FakeWebACL.created_time", "name": "created_time", "type": "builtins.str"}}, "default_action": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.wafv2.models.FakeWebACL.default_action", "name": "default_action", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.wafv2.models.FakeWebACL.description", "name": "description", "type": "builtins.str"}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.wafv2.models.FakeWebACL.id", "name": "id", "type": "builtins.str"}}, "lock_token": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.wafv2.models.FakeWebACL.lock_token", "name": "lock_token", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.wafv2.models.FakeWebACL.name", "name": "name", "type": "builtins.str"}}, "rules": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.wafv2.models.FakeWebACL.rules", "name": "rules", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.FakeWebACL.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.wafv2.models.FakeWebACL"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeWebACL", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "default_action", "rules", "description", "visibility_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.FakeWebACL.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "default_action", "rules", "description", "visibility_config"], "arg_types": ["moto.wafv2.models.FakeWebACL", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of FakeWebACL", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visibility_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.wafv2.models.FakeWebACL.visibility_config", "name": "visibility_config", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.wafv2.models.FakeWebACL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.wafv2.models.FakeWebACL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GLOBAL_REGION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.wafv2.models.GLOBAL_REGION", "name": "GLOBAL_REGION", "type": "builtins.str"}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "Stage": {".class": "SymbolTableNode", "cross_ref": "moto.apigateway.models.Stage", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TaggingService": {".class": "SymbolTableNode", "cross_ref": "moto.utilities.tagging_service.TaggingService", "kind": "Gdef"}, "US_EAST_1_REGION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.wafv2.models.US_EAST_1_REGION", "name": "US_EAST_1_REGION", "type": "builtins.str"}}, "WAFNonexistentItemException": {".class": "SymbolTableNode", "cross_ref": "moto.wafv2.exceptions.WAFNonexistentItemException", "kind": "Gdef"}, "WAFV2Backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.wafv2.models.WAFV2Backend", "name": "WAFV2Backend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.wafv2.models", "mro": ["moto.wafv2.models.WAFV2Backend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.wafv2.models.WAFV2Backend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WAFV2Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_apigw_stage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend._find_apigw_stage", "name": "_find_apigw_stage", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "arg_types": ["moto.wafv2.models.WAFV2Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_apigw_stage of WAFV2Backend", "ret_type": {".class": "UnionType", "items": ["moto.apigateway.models.Stage", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_duplicate_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend._is_duplicate_name", "name": "_is_duplicate_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["moto.wafv2.models.WAFV2Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_duplicate_name of WAFV2Backend", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "associate_web_acl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "web_acl_arn", "resource_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend.associate_web_acl", "name": "associate_web_acl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "web_acl_arn", "resource_arn"], "arg_types": ["moto.wafv2.models.WAFV2Backend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "associate_web_acl of WAFV2Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_web_acl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "visibility_config", "default_action", "scope", "description", "tags", "rules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend.create_web_acl", "name": "create_web_acl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "visibility_config", "default_action", "scope", "description", "tags", "rules"], "arg_types": ["moto.wafv2.models.WAFV2Backend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_web_acl of WAFV2Backend", "ret_type": "moto.wafv2.models.FakeWebACL", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_web_acl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend.delete_web_acl", "name": "delete_web_acl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "_id"], "arg_types": ["moto.wafv2.models.WAFV2Backend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_web_acl of WAFV2Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "disassociate_web_acl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend.disassociate_web_acl", "name": "disassociate_web_acl", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "arg_types": ["moto.wafv2.models.WAFV2Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "disassociate_web_acl of WAFV2Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_web_acl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend.get_web_acl", "name": "get_web_acl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "_id"], "arg_types": ["moto.wafv2.models.WAFV2Backend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_web_acl of WAFV2Backend", "ret_type": "moto.wafv2.models.FakeWebACL", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_web_acl_for_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend.get_web_acl_for_resource", "name": "get_web_acl_for_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "resource_arn"], "arg_types": ["moto.wafv2.models.WAFV2Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_web_acl_for_resource of WAFV2Backend", "ret_type": {".class": "UnionType", "items": ["moto.wafv2.models.FakeWebACL", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_rule_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend.list_rule_groups", "name": "list_rule_groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.wafv2.models.WAFV2Backend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_rule_groups of WAFV2Backend", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_tags_for_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend.list_tags_for_resource", "name": "list_tags_for_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arn"], "arg_types": ["moto.wafv2.models.WAFV2Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_tags_for_resource of WAFV2Backend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_web_acls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend.list_web_acls", "name": "list_web_acls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.wafv2.models.WAFV2Backend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_web_acls of WAFV2Backend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tag_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "arn", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend.tag_resource", "name": "tag_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arn", "tags"], "arg_types": ["moto.wafv2.models.WAFV2Backend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tag_resource of WAFV2Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tagging_service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.wafv2.models.WAFV2Backend.tagging_service", "name": "tagging_service", "type": "moto.utilities.tagging_service.TaggingService"}}, "untag_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "arn", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend.untag_resource", "name": "untag_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arn", "tag_keys"], "arg_types": ["moto.wafv2.models.WAFV2Backend", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "untag_resource of WAFV2Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_web_acl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "_id", "default_action", "rules", "description", "visibility_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.wafv2.models.WAFV2Backend.update_web_acl", "name": "update_web_acl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "_id", "default_action", "rules", "description", "visibility_config"], "arg_types": ["moto.wafv2.models.WAFV2Backend", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_web_acl of WAFV2Backend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wacls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.wafv2.models.WAFV2Backend.wacls", "name": "wacls", "type": {".class": "Instance", "args": ["builtins.str", "moto.wafv2.models.FakeWebACL"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.wafv2.models.WAFV2Backend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.wafv2.models.WAFV2Backend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WAFV2DuplicateItemException": {".class": "SymbolTableNode", "cross_ref": "moto.wafv2.exceptions.WAFV2DuplicateItemException", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.wafv2.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.wafv2.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.wafv2.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.wafv2.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.wafv2.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.wafv2.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "iso_8601_datetime_with_milliseconds": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.iso_8601_datetime_with_milliseconds", "kind": "Gdef"}, "make_arn_for_wacl": {".class": "SymbolTableNode", "cross_ref": "moto.wafv2.utils.make_arn_for_wacl", "kind": "Gdef"}, "mock_random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "wafv2_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.wafv2.models.wafv2_backends", "name": "wafv2_backends", "type": {".class": "Instance", "args": ["moto.wafv2.models.WAFV2Backend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/wafv2/models.py"}