{"data_mtime": 1756972668, "dep_lines": [12, 22, 23, 29, 33, 34, 36, 37, 38, 54, 847, 9, 11, 15, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 13, 858, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 10, 5, 10, 10, 10, 10, 10, 10, 5, 5, 20, 20, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.backends", "moto.core.exceptions", "moto.core.utils", "moto.iam.policy_validation", "moto.moto_api._internal", "moto.utilities.utils", "moto.utilities.tagging_service", "moto.iam.aws_managed_policies", "moto.iam.exceptions", "moto.iam.utils", "moto.cloudformation.exceptions", "urllib.parse", "cryptography.x509", "moto.core", "base64", "copy", "json", "os", "re", "string", "datetime", "typing", "urllib", "cryptography", "jinja2", "html", "builtins", "sys", "inspect", "traceback", "pprint", "collections", "types", "itertools", "warnings", "_frozen_importlib", "_random", "_typeshed", "abc", "enum", "json.encoder", "moto.core.base_backend", "moto.core.common_models", "moto.moto_api", "moto.moto_api._internal.moto_random", "moto.utilities", "random", "typing_extensions", "werkzeug", "werkzeug.exceptions"], "hash": "d1dd21355850ff981fb6a97b53ef93daab2725a2", "id": "moto.iam.models", "ignore_all": true, "interface_hash": "5a8ef4ed50e57d7b984ef7647502f5830937cb7f", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/iam/models.py", "plugin_data": null, "size": 120253, "suppressed": [], "version_id": "1.15.0"}