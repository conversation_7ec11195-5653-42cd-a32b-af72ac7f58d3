{".class": "MypyFile", "_fullname": "moto.sesv2.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "Contact": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.sesv2.models.Contact", "name": "Contact", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.Contact", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.sesv2.models", "mro": ["moto.sesv2.models.Contact", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "contact_list_name", "email_address", "topic_preferences", "unsubscribe_all"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.Contact.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "contact_list_name", "email_address", "topic_preferences", "unsubscribe_all"], "arg_types": ["moto.sesv2.models.Contact", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Contact", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contact_list_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.sesv2.models.Contact.contact_list_name", "name": "contact_list_name", "type": "builtins.str"}}, "created_timestamp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.sesv2.models.Contact.created_timestamp", "name": "created_timestamp", "type": "builtins.str"}}, "email_address": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.sesv2.models.Contact.email_address", "name": "email_address", "type": "builtins.str"}}, "last_updated_timestamp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.sesv2.models.Contact.last_updated_timestamp", "name": "last_updated_timestamp", "type": "builtins.str"}}, "response_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.sesv2.models.Contact.response_object", "name": "response_object", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.sesv2.models.Contact"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "response_object of Contact", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.sesv2.models.Contact.response_object", "name": "response_object", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.sesv2.models.Contact"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "response_object of Contact", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "topic_default_preferences": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.sesv2.models.Contact.topic_default_preferences", "name": "topic_default_preferences", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "topic_preferences": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.sesv2.models.Contact.topic_preferences", "name": "topic_preferences", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "unsubscribe_all": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.sesv2.models.Contact.unsubscribe_all", "name": "unsubscribe_all", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.sesv2.models.Contact.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.sesv2.models.Contact", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContactList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.sesv2.models.ContactList", "name": "ContactList", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.ContactList", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.sesv2.models", "mro": ["moto.sesv2.models.ContactList", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "contact_list_name", "description", "topics"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.ContactList.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "contact_list_name", "description", "topics"], "arg_types": ["moto.sesv2.models.ContactList", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ContactList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contact_list_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.sesv2.models.ContactList.contact_list_name", "name": "contact_list_name", "type": "builtins.str"}}, "contacts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.sesv2.models.ContactList.contacts", "name": "contacts", "type": {".class": "Instance", "args": ["builtins.str", "moto.sesv2.models.Contact"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "create_contact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "contact_list_name", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.ContactList.create_contact", "name": "create_contact", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "contact_list_name", "params"], "arg_types": ["moto.sesv2.models.ContactList", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_contact of ContactList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "created_timestamp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.sesv2.models.ContactList.created_timestamp", "name": "created_timestamp", "type": "builtins.str"}}, "delete_contact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "email"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.ContactList.delete_contact", "name": "delete_contact", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "email"], "arg_types": ["moto.sesv2.models.ContactList", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_contact of ContactList", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.sesv2.models.ContactList.description", "name": "description", "type": "builtins.str"}}, "get_contact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "email"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.ContactList.get_contact", "name": "get_contact", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "email"], "arg_types": ["moto.sesv2.models.ContactList", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_contact of ContactList", "ret_type": "moto.sesv2.models.Contact", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "last_updated_timestamp": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.sesv2.models.ContactList.last_updated_timestamp", "name": "last_updated_timestamp", "type": "builtins.str"}}, "list_contacts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.ContactList.list_contacts", "name": "list_contacts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.sesv2.models.ContactList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_contacts of ContactList", "ret_type": {".class": "Instance", "args": ["moto.sesv2.models.Contact"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "response_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.sesv2.models.ContactList.response_object", "name": "response_object", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.sesv2.models.ContactList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "response_object of ContactList", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.sesv2.models.ContactList.response_object", "name": "response_object", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.sesv2.models.ContactList"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "response_object of ContactList", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "topics": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.sesv2.models.ContactList.topics", "name": "topics", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.sesv2.models.ContactList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.sesv2.models.ContactList", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Message": {".class": "SymbolTableNode", "cross_ref": "moto.ses.models.Message", "kind": "Gdef"}, "NotFoundException": {".class": "SymbolTableNode", "cross_ref": "moto.sesv2.exceptions.NotFoundException", "kind": "Gdef"}, "RawMessage": {".class": "SymbolTableNode", "cross_ref": "moto.ses.models.RawMessage", "kind": "Gdef"}, "SESV2Backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.sesv2.models.SESV2Backend", "name": "SESV2Backend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.SESV2Backend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.sesv2.models", "mro": ["moto.sesv2.models.SESV2Backend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.SESV2Backend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.sesv2.models.SESV2Backend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SESV2Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contacts": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.sesv2.models.SESV2Backend.contacts", "name": "contacts", "type": {".class": "Instance", "args": ["builtins.str", "moto.sesv2.models.Contact"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "contacts_lists": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.sesv2.models.SESV2Backend.contacts_lists", "name": "contacts_lists", "type": {".class": "Instance", "args": ["builtins.str", "moto.sesv2.models.ContactList"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "create_contact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "contact_list_name", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.SESV2Backend.create_contact", "name": "create_contact", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "contact_list_name", "params"], "arg_types": ["moto.sesv2.models.SESV2Backend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_contact of SESV2Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_contact_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.SESV2Backend.create_contact_list", "name": "create_contact_list", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "params"], "arg_types": ["moto.sesv2.models.SESV2Backend", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_contact_list of SESV2Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_contact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "email", "contact_list_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.SESV2Backend.delete_contact", "name": "delete_contact", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "email", "contact_list_name"], "arg_types": ["moto.sesv2.models.SESV2Backend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_contact of SESV2Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_contact_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.SESV2Backend.delete_contact_list", "name": "delete_contact_list", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["moto.sesv2.models.SESV2Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_contact_list of SESV2Backend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_contact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "email", "contact_list_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.SESV2Backend.get_contact", "name": "get_contact", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "email", "contact_list_name"], "arg_types": ["moto.sesv2.models.SESV2Backend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_contact of SESV2Backend", "ret_type": "moto.sesv2.models.Contact", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_contact_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "contact_list_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.SESV2Backend.get_contact_list", "name": "get_contact_list", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "contact_list_name"], "arg_types": ["moto.sesv2.models.SESV2Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_contact_list of SESV2Backend", "ret_type": "moto.sesv2.models.ContactList", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_contact_lists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.SESV2Backend.list_contact_lists", "name": "list_contact_lists", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.sesv2.models.SESV2Backend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_contact_lists of SESV2Backend", "ret_type": {".class": "Instance", "args": ["moto.sesv2.models.ContactList"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_contacts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "contact_list_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.SESV2Backend.list_contacts", "name": "list_contacts", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "contact_list_name"], "arg_types": ["moto.sesv2.models.SESV2Backend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_contacts of SESV2Backend", "ret_type": {".class": "Instance", "args": ["moto.sesv2.models.Contact"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_email": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "source", "destinations", "subject", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.SESV2Backend.send_email", "name": "send_email", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "source", "destinations", "subject", "body"], "arg_types": ["moto.sesv2.models.SESV2Backend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_email of SESV2Backend", "ret_type": "moto.ses.models.Message", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "send_raw_email": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "source", "destinations", "raw_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sesv2.models.SESV2Backend.send_raw_email", "name": "send_raw_email", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "source", "destinations", "raw_data"], "arg_types": ["moto.sesv2.models.SESV2Backend", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "send_raw_email of SESV2Backend", "ret_type": "moto.ses.models.RawMessage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.sesv2.models.SESV2Backend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.sesv2.models.SESV2Backend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.sesv2.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.sesv2.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.sesv2.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.sesv2.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.sesv2.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.sesv2.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "iso_8601_datetime_with_milliseconds": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.iso_8601_datetime_with_milliseconds", "kind": "Gdef"}, "ses_backends": {".class": "SymbolTableNode", "cross_ref": "moto.ses.models.ses_backends", "kind": "Gdef"}, "sesv2_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.sesv2.models.sesv2_backends", "name": "sesv2_backends", "type": {".class": "Instance", "args": ["moto.sesv2.models.SESV2Backend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/sesv2/models.py"}