{"data_mtime": 1756972668, "dep_lines": [11, 9, 12, 14, 225, 8, 10, 1, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], "dep_prios": [5, 5, 5, 5, 20, 5, 5, 10, 10, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["moto.ec2.models.security_groups", "moto.core.utils", "moto.moto_api._internal", "moto.redshift.exceptions", "moto.cloudformation.exceptions", "moto.core", "moto.ec2", "copy", "datetime", "collections", "typing", "builtins", "os", "re", "sys", "string", "inspect", "traceback", "pprint", "types", "itertools", "warnings", "html", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "moto.core.base_backend", "moto.core.common_models", "moto.core.exceptions", "moto.ec2.models", "moto.ec2.models.amis", "moto.ec2.models.availability_zones_and_regions", "moto.ec2.models.carrier_gateways", "moto.ec2.models.core", "moto.ec2.models.customer_gateways", "moto.ec2.models.dhcp_options", "moto.ec2.models.elastic_block_store", "moto.ec2.models.elastic_ip_addresses", "moto.ec2.models.elastic_network_interfaces", "moto.ec2.models.fleets", "moto.ec2.models.flow_logs", "moto.ec2.models.hosts", "moto.ec2.models.iam_instance_profile", "moto.ec2.models.instance_types", "moto.ec2.models.instances", "moto.ec2.models.internet_gateways", "moto.ec2.models.key_pairs", "moto.ec2.models.launch_templates", "moto.ec2.models.managed_prefixes", "moto.ec2.models.nat_gateways", "moto.ec2.models.network_acls", "moto.ec2.models.route_tables", "moto.ec2.models.spot_requests", "moto.ec2.models.subnets", "moto.ec2.models.tags", "moto.ec2.models.transit_gateway", "moto.ec2.models.transit_gateway_attachments", "moto.ec2.models.transit_gateway_route_tables", "moto.ec2.models.vpc_peering_connections", "moto.ec2.models.vpc_service_configuration", "moto.ec2.models.vpcs", "moto.ec2.models.vpn_connections", "moto.ec2.models.vpn_gateway", "moto.ec2.models.windows", "typing_extensions", "werkzeug", "werkzeug.exceptions"], "hash": "309a8fc8335bccced65775764919c490fb9c44e5", "id": "moto.redshift.models", "ignore_all": true, "interface_hash": "1e0e4c96f9b5678a1eeedd66e445d7c20792b2f8", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/redshift/models.py", "plugin_data": null, "size": 43065, "suppressed": ["dateutil.tz"], "version_id": "1.15.0"}