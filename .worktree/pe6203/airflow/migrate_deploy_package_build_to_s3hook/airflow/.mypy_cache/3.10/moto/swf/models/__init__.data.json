{".class": "MypyFile", "_fullname": "moto.swf.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ActivityTask": {".class": "SymbolTableNode", "cross_ref": "moto.swf.models.activity_task.ActivityTask", "kind": "Gdef"}, "ActivityType": {".class": "SymbolTableNode", "cross_ref": "moto.swf.models.activity_type.ActivityType", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "DecisionTask": {".class": "SymbolTableNode", "cross_ref": "moto.swf.models.decision_task.DecisionTask", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Domain": {".class": "SymbolTableNode", "cross_ref": "moto.swf.models.domain.Domain", "kind": "Gdef"}, "GenericType": {".class": "SymbolTableNode", "cross_ref": "moto.swf.models.generic_type.GenericType", "kind": "Gdef"}, "HistoryEvent": {".class": "SymbolTableNode", "cross_ref": "moto.swf.models.history_event.HistoryEvent", "kind": "Gdef"}, "KNOWN_SWF_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.swf.models.KNOWN_SWF_TYPES", "name": "KNOWN_SWF_TYPES", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["name", "version", "kwargs"], "arg_types": ["builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": ["moto.swf.models.workflow_type.WorkflowType"], "def_extras": {"first_arg": "self"}, "fallback": "moto.core.base_backend.InstanceTrackerMeta", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "moto.swf.models.generic_type.GenericType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "SWFBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.swf.models.SWFBackend", "name": "SWFBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.swf.models", "mro": ["moto.swf.models.SWFBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_activity_task_from_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "task_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend._find_activity_task_from_token", "name": "_find_activity_task_from_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "task_token"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_activity_task_from_token of SWFBackend", "ret_type": "moto.swf.models.activity_task.ActivityTask", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "ignore_empty"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend._get_domain", "name": "_get_domain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "ignore_empty"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_domain of SWFBackend", "ret_type": "moto.swf.models.domain.Domain", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_timeouts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend._process_timeouts", "name": "_process_timeouts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.swf.models.SWFBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_timeouts of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "count_pending_activity_tasks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "domain_name", "task_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.count_pending_activity_tasks", "name": "count_pending_activity_tasks", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "domain_name", "task_list"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count_pending_activity_tasks of SWFBackend", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "count_pending_decision_tasks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "domain_name", "task_list"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.count_pending_decision_tasks", "name": "count_pending_decision_tasks", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "domain_name", "task_list"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count_pending_decision_tasks of SWFBackend", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deprecate_domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.deprecate_domain", "name": "deprecate_domain", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deprecate_domain of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deprecate_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "kind", "domain_name", "name", "version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.deprecate_type", "name": "deprecate_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "kind", "domain_name", "name", "version"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deprecate_type of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.describe_domain", "name": "describe_domain", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_domain of SWFBackend", "ret_type": {".class": "UnionType", "items": ["moto.swf.models.domain.Domain", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "kind", "domain_name", "name", "version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.describe_type", "name": "describe_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "kind", "domain_name", "name", "version"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_type of SWFBackend", "ret_type": "moto.swf.models.generic_type.GenericType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_workflow_execution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "domain_name", "run_id", "workflow_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.describe_workflow_execution", "name": "describe_workflow_execution", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "domain_name", "run_id", "workflow_id"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_workflow_execution of SWFBackend", "ret_type": {".class": "UnionType", "items": ["moto.swf.models.workflow_execution.WorkflowExecution", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "domains": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.swf.models.SWFBackend.domains", "name": "domains", "type": {".class": "Instance", "args": ["moto.swf.models.domain.Domain"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "list_closed_workflow_executions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "domain_name", "tag_filter", "close_status_filter", "maximum_page_size", "reverse_order"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.list_closed_workflow_executions", "name": "list_closed_workflow_executions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "domain_name", "tag_filter", "close_status_filter", "maximum_page_size", "reverse_order"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_closed_workflow_executions of SWFBackend", "ret_type": {".class": "Instance", "args": ["moto.swf.models.workflow_execution.WorkflowExecution"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_domains": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "status", "reverse_order"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.list_domains", "name": "list_domains", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "status", "reverse_order"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_domains of SWFBackend", "ret_type": {".class": "Instance", "args": ["moto.swf.models.domain.Domain"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_open_workflow_executions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "domain_name", "maximum_page_size", "tag_filter", "reverse_order"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.list_open_workflow_executions", "name": "list_open_workflow_executions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "domain_name", "maximum_page_size", "tag_filter", "reverse_order"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", "builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_open_workflow_executions of SWFBackend", "ret_type": {".class": "Instance", "args": ["moto.swf.models.workflow_execution.WorkflowExecution"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "kind", "domain_name", "status", "reverse_order"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.list_types", "name": "list_types", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "kind", "domain_name", "status", "reverse_order"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_types of SWFBackend", "ret_type": {".class": "Instance", "args": ["moto.swf.models.generic_type.GenericType"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "poll_for_activity_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "domain_name", "task_list", "identity"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.poll_for_activity_task", "name": "poll_for_activity_task", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "domain_name", "task_list", "identity"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "poll_for_activity_task of SWFBackend", "ret_type": {".class": "UnionType", "items": ["moto.swf.models.activity_task.ActivityTask", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "poll_for_decision_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "domain_name", "task_list", "identity"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.poll_for_decision_task", "name": "poll_for_decision_task", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "domain_name", "task_list", "identity"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "poll_for_decision_task of SWFBackend", "ret_type": {".class": "UnionType", "items": ["moto.swf.models.decision_task.DecisionTask", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "record_activity_task_heartbeat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "task_token", "details"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.record_activity_task_heartbeat", "name": "record_activity_task_heartbeat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "task_token", "details"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "record_activity_task_heartbeat of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "workflow_execution_retention_period_in_days", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.register_domain", "name": "register_domain", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "name", "workflow_execution_retention_period_in_days", "description"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_domain of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "kind", "domain_name", "name", "version", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.register_type", "name": "register_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "kind", "domain_name", "name", "version", "kwargs"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_type of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "respond_activity_task_completed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "task_token", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.respond_activity_task_completed", "name": "respond_activity_task_completed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "task_token", "result"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "respond_activity_task_completed of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "respond_activity_task_failed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "task_token", "reason", "details"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.respond_activity_task_failed", "name": "respond_activity_task_failed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "task_token", "reason", "details"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "respond_activity_task_failed of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "respond_decision_task_completed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "task_token", "decisions", "execution_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.respond_decision_task_completed", "name": "respond_decision_task_completed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "task_token", "decisions", "execution_context"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "respond_decision_task_completed of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "signal_workflow_execution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "domain_name", "signal_name", "workflow_id", "workflow_input", "run_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.signal_workflow_execution", "name": "signal_workflow_execution", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "domain_name", "signal_name", "workflow_id", "workflow_input", "run_id"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signal_workflow_execution of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_workflow_execution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "domain_name", "workflow_id", "workflow_name", "workflow_version", "tag_list", "workflow_input", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.start_workflow_execution", "name": "start_workflow_execution", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 4], "arg_names": ["self", "domain_name", "workflow_id", "workflow_name", "workflow_version", "tag_list", "workflow_input", "kwargs"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_workflow_execution of SWFBackend", "ret_type": "moto.swf.models.workflow_execution.WorkflowExecution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "terminate_workflow_execution": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "domain_name", "workflow_id", "child_policy", "details", "reason", "run_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.terminate_workflow_execution", "name": "terminate_workflow_execution", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "domain_name", "workflow_id", "child_policy", "details", "reason", "run_id"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "terminate_workflow_execution of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "undeprecate_domain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.undeprecate_domain", "name": "undeprecate_domain", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "undeprecate_domain of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "undeprecate_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "kind", "domain_name", "name", "version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.swf.models.SWFBackend.undeprecate_type", "name": "undeprecate_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "kind", "domain_name", "name", "version"], "arg_types": ["moto.swf.models.SWFBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "undeprecate_type of SWFBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.swf.models.SWFBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.swf.models.SWFBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SWFDomainAlreadyExistsFault": {".class": "SymbolTableNode", "cross_ref": "moto.swf.exceptions.SWFDomainAlreadyExistsFault", "kind": "Gdef"}, "SWFDomainDeprecatedFault": {".class": "SymbolTableNode", "cross_ref": "moto.swf.exceptions.SWFDomainDeprecatedFault", "kind": "Gdef"}, "SWFTypeAlreadyExistsFault": {".class": "SymbolTableNode", "cross_ref": "moto.swf.exceptions.SWFTypeAlreadyExistsFault", "kind": "Gdef"}, "SWFTypeDeprecatedFault": {".class": "SymbolTableNode", "cross_ref": "moto.swf.exceptions.SWFTypeDeprecatedFault", "kind": "Gdef"}, "SWFUnknownResourceFault": {".class": "SymbolTableNode", "cross_ref": "moto.swf.exceptions.SWFUnknownResourceFault", "kind": "Gdef"}, "SWFValidationException": {".class": "SymbolTableNode", "cross_ref": "moto.swf.exceptions.SWFValidationException", "kind": "Gdef"}, "TGenericType": {".class": "SymbolTableNode", "cross_ref": "moto.swf.models.generic_type.TGenericType", "kind": "Gdef"}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "moto.swf.models.timeout.Timeout", "kind": "Gdef"}, "Timer": {".class": "SymbolTableNode", "cross_ref": "moto.swf.models.timer.Timer", "kind": "Gdef"}, "WorkflowExecution": {".class": "SymbolTableNode", "cross_ref": "moto.swf.models.workflow_execution.WorkflowExecution", "kind": "Gdef"}, "WorkflowType": {".class": "SymbolTableNode", "cross_ref": "moto.swf.models.workflow_type.WorkflowType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.swf.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.swf.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.swf.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.swf.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.swf.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.swf.models.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.swf.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "sleep": {".class": "SymbolTableNode", "cross_ref": "time.sleep", "kind": "Gdef"}, "swf_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.swf.models.swf_backends", "name": "swf_backends", "type": {".class": "Instance", "args": ["moto.swf.models.SWFBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/swf/models/__init__.py"}