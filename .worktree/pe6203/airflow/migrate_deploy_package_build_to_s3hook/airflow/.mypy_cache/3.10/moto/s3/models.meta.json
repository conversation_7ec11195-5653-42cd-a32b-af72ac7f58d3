{"data_mtime": 1756972668, "dep_lines": [33, 17, 25, 32, 34, 58, 59, 61, 67, 68, 69, 70, 1027, 1355, 1673, 12, 18, 62, 67, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 361, 1673, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2698, 2705], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 20, 20, 20, 10, 5, 5, 20, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 20, 5, 5, 5, 20, 20, 5, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["moto.moto_api._internal.managed_state_model", "moto.cloudwatch.models", "moto.core.utils", "moto.moto_api._internal", "moto.s3.exceptions", "moto.utilities.tagging_service", "moto.utilities.utils", "moto.events.notifications", "moto.s3.notifications", "moto.s3.cloud_formation", "moto.s3.select_object_content", "moto.s3.utils", "moto.iam.access_control", "moto.cloudformation.exceptions", "moto.s3.urls", "urllib.parse", "moto.core", "moto.settings", "moto.s3", "base64", "codecs", "copy", "datetime", "itertools", "json", "os", "string", "sys", "tempfile", "threading", "urllib", "bisect", "importlib", "typing", "warnings", "moto", "builtins", "re", "inspect", "traceback", "pprint", "collections", "types", "html", "_collections_abc", "_frozen_importlib", "_random", "_thread", "_typeshed", "abc", "moto.cloudwatch", "moto.core.base_backend", "moto.core.common_models", "moto.core.exceptions", "moto.moto_api", "moto.moto_api._internal.moto_random", "moto.utilities", "random", "typing_extensions", "uuid", "werkzeug", "werkzeug.exceptions"], "hash": "c15d1a3d8fcf53ee7496d4a89e6629260b047a32", "id": "moto.s3.models", "ignore_all": true, "interface_hash": "056161c096d1399528063d2b19120e6a56012664", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/s3/models.py", "plugin_data": null, "size": 100608, "suppressed": ["py_partiql_parser._internal.csv_converter", "py_partiql_parser"], "version_id": "1.15.0"}