{"data_mtime": 1756972668, "dep_lines": [13, 13, 13, 14, 14, 11, 11, 12, 13, 14, 10, 11, 16, 18, 9, 1, 2, 3, 4, 5, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 5, 20, 5, 5, 20, 5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["cryptography.hazmat.primitives.asymmetric.ec", "cryptography.hazmat.primitives.asymmetric.padding", "cryptography.hazmat.primitives.asymmetric.rsa", "cryptography.hazmat.primitives.ciphers.algorithms", "cryptography.hazmat.primitives.ciphers.modes", "cryptography.hazmat.primitives.hashes", "cryptography.hazmat.primitives.serialization", "cryptography.hazmat.primitives._asymmetric", "cryptography.hazmat.primitives.asymmetric", "cryptography.hazmat.primitives.ciphers", "cryptography.hazmat.backends", "cryptography.hazmat.primitives", "moto.moto_api._internal", "moto.kms.exceptions", "cryptography.exceptions", "io", "os", "struct", "abc", "collections", "enum", "typing", "builtins", "re", "sys", "string", "inspect", "traceback", "pprint", "types", "itertools", "warnings", "copy", "html", "_collections_abc", "_frozen_importlib", "cryptography", "cryptography.hazmat", "moto.core", "moto.core.exceptions", "typing_extensions", "werkzeug", "werkzeug.exceptions"], "hash": "60e6d0c2d4198d26ac859a41a97309541697d9eb", "id": "moto.kms.utils", "ignore_all": true, "interface_hash": "67ff3f6b1d6836f38680a60726e789ea4e0af097", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/kms/utils.py", "plugin_data": null, "size": 17111, "suppressed": [], "version_id": "1.15.0"}