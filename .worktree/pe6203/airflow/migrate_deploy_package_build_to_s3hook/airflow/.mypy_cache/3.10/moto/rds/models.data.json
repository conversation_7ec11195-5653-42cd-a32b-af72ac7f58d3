{".class": "MypyFile", "_fullname": "moto.rds.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "CloudFormationModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.CloudFormationModel", "kind": "Gdef"}, "Cluster": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.rds.models.Cluster", "name": "Cluster", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.rds.models.Cluster", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.rds.models", "mro": ["moto.rds.models.Cluster", "builtins.object"], "names": {".class": "SymbolTable", "SUPPORTED_FILTERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.rds.models.Cluster.SUPPORTED_FILTERS", "name": "SUPPORTED_FILTERS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": "moto.rds.utils.FilterDef"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Cluster.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["moto.rds.models.Cluster", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Cluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_enable_http_endpoint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster._enable_http_endpoint", "name": "_enable_http_endpoint", "type": "builtins.bool"}}, "_master_user_password": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster._master_user_password", "name": "_master_user_password", "type": "builtins.str"}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.account_id", "name": "account_id", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "add_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Cluster.add_tags", "name": "add_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "arg_types": ["moto.rds.models.Cluster", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tags of Cluster", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allocated_storage": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.allocated_storage", "name": "allocated_storage", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.Cluster.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of Cluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of Cluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "availability_zones": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.availability_zones", "name": "availability_zones", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cluster_create_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.cluster_create_time", "name": "cluster_create_time", "type": "builtins.str"}}, "cluster_members": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.cluster_members", "name": "cluster_members", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "copy_tags_to_snapshot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.copy_tags_to_snapshot", "name": "copy_tags_to_snapshot", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "db_cluster_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.Cluster.db_cluster_arn", "name": "db_cluster_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "db_cluster_arn of Cluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.db_cluster_arn", "name": "db_cluster_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "db_cluster_arn of Cluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "db_cluster_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.db_cluster_identifier", "name": "db_cluster_identifier", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "db_cluster_instance_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.db_cluster_instance_class", "name": "db_cluster_instance_class", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "db_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.db_name", "name": "db_name", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "default_allocated_storage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["engine", "storage_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.Cluster.default_allocated_storage", "name": "default_allocated_storage", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["engine", "storage_type"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_allocated_storage of Cluster", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.default_allocated_storage", "name": "default_allocated_storage", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["engine", "storage_type"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_allocated_storage of Cluster", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default_engine_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["engine"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.Cluster.default_engine_version", "name": "default_engine_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["engine"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_engine_version of Cluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.default_engine_version", "name": "default_engine_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["engine"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_engine_version of Cluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["engine"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.Cluster.default_port", "name": "default_port", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["engine"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_port of Cluster", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.default_port", "name": "default_port", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["engine"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_port of Cluster", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default_storage_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["iops"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.Cluster.default_storage_type", "name": "default_storage_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["iops"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_storage_type of Cluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.default_storage_type", "name": "default_storage_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["iops"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_storage_type of Cluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "deletion_protection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.deletion_protection", "name": "deletion_protection", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "earliest_restorable_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.earliest_restorable_time", "name": "earliest_restorable_time", "type": "builtins.str"}}, "enable_http_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "moto.rds.models.Cluster.enable_http_endpoint", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "moto.rds.models.Cluster.enable_http_endpoint", "name": "enable_http_endpoint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_http_endpoint of Cluster", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.enable_http_endpoint", "name": "enable_http_endpoint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_http_endpoint of Cluster", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.rds.models.Cluster.enable_http_endpoint", "name": "enable_http_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["moto.rds.models.Cluster", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_http_endpoint of Cluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "enable_http_endpoint", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_http_endpoint of Cluster", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "enabled_cloudwatch_logs_exports": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.enabled_cloudwatch_logs_exports", "name": "enabled_cloudwatch_logs_exports", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}}}, "endpoint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.endpoint", "name": "endpoint", "type": "builtins.str"}}, "engine": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.engine", "name": "engine", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "engine_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.engine_mode", "name": "engine_mode", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "uses_pep604_syntax": false}}}, "engine_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.engine_version", "name": "engine_version", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "uses_pep604_syntax": false}}}, "get_cfg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Cluster.get_cfg", "name": "get_cfg", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cfg of Cluster", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Cluster.get_tags", "name": "get_tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tags of Cluster", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "global_cluster_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.global_cluster_identifier", "name": "global_cluster_identifier", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "hosted_zone_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.hosted_zone_id", "name": "hosted_zone_id", "type": "builtins.str"}}, "iops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.iops", "name": "iops", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "is_multi_az": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.Cluster.is_multi_az", "name": "is_multi_az", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_multi_az of Cluster", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.is_multi_az", "name": "is_multi_az", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_multi_az of Cluster", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_writer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.is_writer", "name": "is_writer", "type": "builtins.bool"}}, "kms_key_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.kms_key_id", "name": "kms_key_id", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "master_user_password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "moto.rds.models.Cluster.master_user_password", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "moto.rds.models.Cluster.master_user_password", "name": "master_user_password", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "master_user_password of Cluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.master_user_password", "name": "master_user_password", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "master_user_password of Cluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.rds.models.Cluster.master_user_password", "name": "master_user_password", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "val"], "arg_types": ["moto.rds.models.Cluster", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "master_user_password of Cluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "master_user_password", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "master_user_password of Cluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "master_username": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.master_username", "name": "master_username", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "network_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.network_type", "name": "network_type", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "uses_pep604_syntax": false}}}, "parameter_group": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.parameter_group", "name": "parameter_group", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "uses_pep604_syntax": false}}}, "port": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.port", "name": "port", "type": "builtins.int"}}, "preferred_backup_window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.preferred_backup_window", "name": "preferred_backup_window", "type": "builtins.str"}}, "preferred_maintenance_window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.preferred_maintenance_window", "name": "preferred_maintenance_window", "type": "builtins.str"}}, "read_replica_identifiers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.read_replica_identifiers", "name": "read_replica_identifiers", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "reader_endpoint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.reader_endpoint", "name": "reader_endpoint", "type": "builtins.str"}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.region_name", "name": "region_name", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "remove_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Cluster.remove_tags", "name": "remove_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "arg_types": ["moto.rds.models.Cluster", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_tags of Cluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "replication_source_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.replication_source_identifier", "name": "replication_source_identifier", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "resource_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.resource_id", "name": "resource_id", "type": "builtins.str"}}, "scaling_configuration": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.scaling_configuration", "name": "scaling_configuration", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "serverless_v2_scaling_configuration": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.serverless_v2_scaling_configuration", "name": "serverless_v2_scaling_configuration", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.status", "name": "status", "type": "builtins.str"}}, "storage_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.storage_type", "name": "storage_type", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "subnet_group": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.subnet_group", "name": "subnet_group", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str"], "uses_pep604_syntax": false}}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.tags", "name": "tags", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Cluster.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Cluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of Cluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "url_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Cluster.url_identifier", "name": "url_identifier", "type": "builtins.str"}}, "vpc_security_group_ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Cluster.vpc_security_group_ids", "name": "vpc_security_group_ids", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.rds.models.Cluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.rds.models.Cluster", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ClusterEngine": {".class": "SymbolTableNode", "cross_ref": "moto.rds.utils.ClusterEngine", "kind": "Gdef"}, "ClusterSnapshot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.rds.models.ClusterSnapshot", "name": "ClusterSnapshot", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.rds.models.ClusterSnapshot", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.rds.models", "mro": ["moto.rds.models.ClusterSnapshot", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "SUPPORTED_FILTERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.rds.models.ClusterSnapshot.SUPPORTED_FILTERS", "name": "SUPPORTED_FILTERS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": "moto.rds.utils.FilterDef"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "cluster", "snapshot_id", "snapshot_type", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.ClusterSnapshot.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "cluster", "snapshot_id", "snapshot_type", "tags"], "arg_types": ["moto.rds.models.ClusterSnapshot", "moto.rds.models.Cluster", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ClusterSnapshot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.ClusterSnapshot.add_tags", "name": "add_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "arg_types": ["moto.rds.models.ClusterSnapshot", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tags of ClusterSnapshot", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.ClusterSnapshot.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.ClusterSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of ClusterSnapshot", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.ClusterSnapshot.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.ClusterSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of ClusterSnapshot", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cluster": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ClusterSnapshot.cluster", "name": "cluster", "type": "moto.rds.models.Cluster"}}, "created_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ClusterSnapshot.created_at", "name": "created_at", "type": "builtins.str"}}, "get_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.ClusterSnapshot.get_tags", "name": "get_tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.ClusterSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tags of ClusterSnapshot", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.ClusterSnapshot.remove_tags", "name": "remove_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "arg_types": ["moto.rds.models.ClusterSnapshot", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_tags of ClusterSnapshot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "snapshot_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.ClusterSnapshot.snapshot_arn", "name": "snapshot_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.ClusterSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "snapshot_arn of ClusterSnapshot", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.ClusterSnapshot.snapshot_arn", "name": "snapshot_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.ClusterSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "snapshot_arn of ClusterSnapshot", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "snapshot_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ClusterSnapshot.snapshot_id", "name": "snapshot_id", "type": "builtins.str"}}, "snapshot_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ClusterSnapshot.snapshot_type", "name": "snapshot_type", "type": "builtins.str"}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ClusterSnapshot.status", "name": "status", "type": "builtins.str"}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ClusterSnapshot.tags", "name": "tags", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.ClusterSnapshot.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.ClusterSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of ClusterSnapshot", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.rds.models.ClusterSnapshot.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.rds.models.ClusterSnapshot", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBClusterNotFoundError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.DBClusterNotFoundError", "kind": "Gdef"}, "DBClusterParameterGroup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["cloudformation_name_type", 1], ["cloudformation_type", 1], ["create_from_cloudformation_json", 1], ["delete_from_cloudformation_json", 1], ["has_cfn_attr", 1], ["is_created", 1], ["update_from_cloudformation_json", 1]], "alt_promote": null, "bases": ["moto.core.common_models.CloudFormationModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.rds.models.DBClusterParameterGroup", "name": "DBClusterParameterGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "moto.rds.models.DBClusterParameterGroup", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.rds.models", "mro": ["moto.rds.models.DBClusterParameterGroup", "moto.core.common_models.CloudFormationModel", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "name", "description", "family"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DBClusterParameterGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "name", "description", "family"], "arg_types": ["moto.rds.models.DBClusterParameterGroup", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DBClusterParameterGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DBClusterParameterGroup.arn", "name": "arn", "type": "builtins.str"}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DBClusterParameterGroup.description", "name": "description", "type": "builtins.str"}}, "family": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DBClusterParameterGroup.family", "name": "family", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DBClusterParameterGroup.name", "name": "name", "type": "builtins.str"}}, "parameters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.DBClusterParameterGroup.parameters", "name": "parameters", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DBClusterParameterGroup.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.DBClusterParameterGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of DBClusterParameterGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.rds.models.DBClusterParameterGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.rds.models.DBClusterParameterGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBClusterParameterGroupNotFoundError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.DBClusterParameterGroupNotFoundError", "kind": "Gdef"}, "DBClusterSnapshotAlreadyExistsError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.DBClusterSnapshotAlreadyExistsError", "kind": "Gdef"}, "DBClusterSnapshotNotFoundError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.DBClusterSnapshotNotFoundError", "kind": "Gdef"}, "DBClusterToBeDeletedHasActiveMembers": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.DBClusterToBeDeletedHasActiveMembers", "kind": "Gdef"}, "DBInstanceNotFoundError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.DBInstanceNotFoundError", "kind": "Gdef"}, "DBParameterGroup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["delete_from_cloudformation_json", 1], ["has_cfn_attr", 1], ["is_created", 1], ["update_from_cloudformation_json", 1]], "alt_promote": null, "bases": ["moto.core.common_models.CloudFormationModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.rds.models.DBParameterGroup", "name": "DBParameterGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "moto.rds.models.DBParameterGroup", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.rds.models", "mro": ["moto.rds.models.DBParameterGroup", "moto.core.common_models.CloudFormationModel", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "name", "description", "family", "tags", "region"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DBParameterGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "name", "description", "family", "tags", "region"], "arg_types": ["moto.rds.models.DBParameterGroup", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DBParameterGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DBParameterGroup.add_tags", "name": "add_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "arg_types": ["moto.rds.models.DBParameterGroup", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tags of DBParameterGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DBParameterGroup.arn", "name": "arn", "type": "builtins.str"}}, "cloudformation_name_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.DBParameterGroup.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of DBParameterGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.DBParameterGroup.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of DBParameterGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cloudformation_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.DBParameterGroup.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of DBParameterGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.DBParameterGroup.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of DBParameterGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_from_cloudformation_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "moto.rds.models.DBParameterGroup.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.rds.models.DBParameterGroup"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of DBParameterGroup", "ret_type": "moto.rds.models.DBParameterGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.DBParameterGroup.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.rds.models.DBParameterGroup"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of DBParameterGroup", "ret_type": "moto.rds.models.DBParameterGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DBParameterGroup.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "region_name"], "arg_types": ["moto.rds.models.DBParameterGroup", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete of DBParameterGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DBParameterGroup.description", "name": "description", "type": "builtins.str"}}, "family": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DBParameterGroup.family", "name": "family", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DBParameterGroup.get_tags", "name": "get_tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.DBParameterGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tags of DBParameterGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DBParameterGroup.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "parameters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.DBParameterGroup.parameters", "name": "parameters", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "remove_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DBParameterGroup.remove_tags", "name": "remove_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "arg_types": ["moto.rds.models.DBParameterGroup", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_tags of DBParameterGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DBParameterGroup.tags", "name": "tags", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DBParameterGroup.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.DBParameterGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of DBParameterGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DBParameterGroup.update_parameters", "name": "update_parameters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_parameters"], "arg_types": ["moto.rds.models.DBParameterGroup", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_parameters of DBParameterGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.rds.models.DBParameterGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.rds.models.DBParameterGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DBParameterGroupNotFoundError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.DBParameterGroupNotFoundError", "kind": "Gdef"}, "DBSecurityGroupNotFoundError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.DBSecurityGroupNotFoundError", "kind": "Gdef"}, "DBSnapshotAlreadyExistsError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.DBSnapshotAlreadyExistsError", "kind": "Gdef"}, "DBSnapshotNotFoundError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.DBSnapshotNotFoundError", "kind": "Gdef"}, "DBSubnetGroupNotFoundError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.DBSubnetGroupNotFoundError", "kind": "Gdef"}, "Database": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["delete_from_cloudformation_json", 1], ["is_created", 1], ["update_from_cloudformation_json", 1]], "alt_promote": null, "bases": ["moto.core.common_models.CloudFormationModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.rds.models.Database", "name": "Database", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "moto.rds.models.Database", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.rds.models", "mro": ["moto.rds.models.Database", "moto.core.common_models.CloudFormationModel", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "SUPPORTED_FILTERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.rds.models.Database.SUPPORTED_FILTERS", "name": "SUPPORTED_FILTERS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": "moto.rds.utils.FilterDef"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["moto.rds.models.Database", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Database", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.account_id", "name": "account_id", "type": "builtins.str"}}, "add_replica": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "replica"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.add_replica", "name": "add_replica", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "replica"], "arg_types": ["moto.rds.models.Database", "moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_replica of Database", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.add_tags", "name": "add_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "arg_types": ["moto.rds.models.Database", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tags of Database", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.Database.address", "name": "address", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "address of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.address", "name": "address", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "address of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "allocated_storage": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.allocated_storage", "name": "allocated_storage", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.Database.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "auto_minor_version_upgrade": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.auto_minor_version_upgrade", "name": "auto_minor_version_upgrade", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "availability_zone": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.availability_zone", "name": "availability_zone", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "backup_retention_period": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.backup_retention_period", "name": "backup_retention_period", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "character_set_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.character_set_name", "name": "character_set_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "cloudformation_name_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.Database.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cloudformation_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.Database.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "copy_tags_to_snapshot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.copy_tags_to_snapshot", "name": "copy_tags_to_snapshot", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "create_from_cloudformation_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "moto.rds.models.Database.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.rds.models.Database"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of Database", "ret_type": "moto.rds.models.Database", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.rds.models.Database"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of Database", "ret_type": "moto.rds.models.Database", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "db_cluster_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.db_cluster_identifier", "name": "db_cluster_identifier", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "db_instance_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.Database.db_instance_arn", "name": "db_instance_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "db_instance_arn of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.db_instance_arn", "name": "db_instance_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "db_instance_arn of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "db_instance_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.db_instance_class", "name": "db_instance_class", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "db_instance_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.db_instance_identifier", "name": "db_instance_identifier", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "db_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.db_name", "name": "db_name", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "db_parameter_group_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.db_parameter_group_name", "name": "db_parameter_group_name", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "db_parameter_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.db_parameter_groups", "name": "db_parameter_groups", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "db_parameter_groups of Database", "ret_type": {".class": "Instance", "args": ["moto.rds.models.DBParameterGroup"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "db_subnet_group": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.db_subnet_group", "name": "db_subnet_group", "type": {".class": "UnionType", "items": ["moto.rds.models.SubnetGroup", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "db_subnet_group_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.db_subnet_group_name", "name": "db_subnet_group_name", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "dbi_resource_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.dbi_resource_id", "name": "dbi_resource_id", "type": "builtins.str"}}, "default_allocated_storage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["engine", "storage_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.Database.default_allocated_storage", "name": "default_allocated_storage", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["engine", "storage_type"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_allocated_storage of Database", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.default_allocated_storage", "name": "default_allocated_storage", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["engine", "storage_type"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_allocated_storage of Database", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default_db_parameter_group_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.default_db_parameter_group_details", "name": "default_db_parameter_group_details", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_db_parameter_group_details of Database", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_engine_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.rds.models.Database.default_engine_versions", "name": "default_engine_versions", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "default_option_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.default_option_groups", "name": "default_option_groups", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "default_port": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["engine"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.Database.default_port", "name": "default_port", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["engine"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_port of Database", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.default_port", "name": "default_port", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["engine"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_port of Database", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default_storage_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["iops"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.Database.default_storage_type", "name": "default_storage_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["iops"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_storage_type of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.default_storage_type", "name": "default_storage_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["iops"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_storage_type of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "region_name"], "arg_types": ["moto.rds.models.Database", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete of Database", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deletion_protection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.deletion_protection", "name": "deletion_protection", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "enable_iam_database_authentication": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.enable_iam_database_authentication", "name": "enable_iam_database_authentication", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "enabled_cloudwatch_logs_exports": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.enabled_cloudwatch_logs_exports", "name": "enabled_cloudwatch_logs_exports", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}}}, "engine": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.engine", "name": "engine", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "engine_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.engine_version", "name": "engine_version", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_cfn_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attribute_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.get_cfn_attribute", "name": "get_cfn_attribute", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attribute_name"], "arg_types": ["moto.rds.models.Database", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cfn_attribute of Database", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.get_tags", "name": "get_tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tags of Database", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_cfn_attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "attr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "moto.rds.models.Database.has_cfn_attr", "name": "has_cfn_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "attr"], "arg_types": [{".class": "TypeType", "item": "moto.rds.models.Database"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_cfn_attr of Database", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.has_cfn_attr", "name": "has_cfn_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "attr"], "arg_types": [{".class": "TypeType", "item": "moto.rds.models.Database"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_cfn_attr of Database", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "instance_create_time": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.instance_create_time", "name": "instance_create_time", "type": "builtins.str"}}, "iops": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.iops", "name": "iops", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "is_default_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "param_group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.is_default_parameter_group", "name": "is_default_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "param_group_name"], "arg_types": ["moto.rds.models.Database", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_default_parameter_group of Database", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_replica": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.is_replica", "name": "is_replica", "type": "builtins.bool"}}, "kms_key_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.kms_key_id", "name": "kms_key_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "license_model": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.license_model", "name": "license_model", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "master_user_password": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.master_user_password", "name": "master_user_password", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "master_username": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.master_username", "name": "master_username", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "multi_az": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.multi_az", "name": "multi_az", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "option_group_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.option_group_name", "name": "option_group_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "option_group_supplied": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.option_group_supplied", "name": "option_group_supplied", "type": "builtins.bool"}}, "physical_resource_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.Database.physical_resource_id", "name": "physical_resource_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "physical_resource_id of Database", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.physical_resource_id", "name": "physical_resource_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "physical_resource_id of Database", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "port": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.port", "name": "port", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "preferred_backup_window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.preferred_backup_window", "name": "preferred_backup_window", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "preferred_maintenance_window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.preferred_maintenance_window", "name": "preferred_maintenance_window", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "publicly_accessible": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.publicly_accessible", "name": "publicly_accessible", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.region_name", "name": "region_name", "type": "builtins.str"}}, "remove_replica": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "replica"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.remove_replica", "name": "remove_replica", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "replica"], "arg_types": ["moto.rds.models.Database", "moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_replica of Database", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.remove_tags", "name": "remove_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "arg_types": ["moto.rds.models.Database", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_tags of Database", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "replicas": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.replicas", "name": "replicas", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "security_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.security_groups", "name": "security_groups", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "set_as_replica": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.set_as_replica", "name": "set_as_replica", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_as_replica of Database", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "source_db_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.Database.source_db_identifier", "name": "source_db_identifier", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.status", "name": "status", "type": "builtins.str"}}, "storage_encrypted": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.storage_encrypted", "name": "storage_encrypted", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "storage_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.storage_type", "name": "storage_type", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.tags", "name": "tags", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.to_json", "name": "to_json", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_json of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.Database"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of Database", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.Database.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_kwargs"], "arg_types": ["moto.rds.models.Database", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of Database", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vpc_security_group_ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.Database.vpc_security_group_ids", "name": "vpc_security_group_ids", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.rds.models.Database.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.rds.models.Database", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DatabaseSnapshot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.rds.models.DatabaseSnapshot", "name": "DatabaseSnapshot", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.rds.models.DatabaseSnapshot", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.rds.models", "mro": ["moto.rds.models.DatabaseSnapshot", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "SUPPORTED_FILTERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.rds.models.DatabaseSnapshot.SUPPORTED_FILTERS", "name": "SUPPORTED_FILTERS", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": "moto.rds.utils.FilterDef"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "database", "snapshot_id", "snapshot_type", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DatabaseSnapshot.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "database", "snapshot_id", "snapshot_type", "tags"], "arg_types": ["moto.rds.models.DatabaseSnapshot", "moto.rds.models.Database", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DatabaseSnapshot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DatabaseSnapshot.add_tags", "name": "add_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "arg_types": ["moto.rds.models.DatabaseSnapshot", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tags of DatabaseSnapshot", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.DatabaseSnapshot.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.DatabaseSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of DatabaseSnapshot", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.DatabaseSnapshot.arn", "name": "arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.DatabaseSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn of DatabaseSnapshot", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "created_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DatabaseSnapshot.created_at", "name": "created_at", "type": "builtins.str"}}, "database": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DatabaseSnapshot.database", "name": "database", "type": "moto.rds.models.Database"}}, "get_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DatabaseSnapshot.get_tags", "name": "get_tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.DatabaseSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tags of DatabaseSnapshot", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DatabaseSnapshot.remove_tags", "name": "remove_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "arg_types": ["moto.rds.models.DatabaseSnapshot", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_tags of DatabaseSnapshot", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "snapshot_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.DatabaseSnapshot.snapshot_arn", "name": "snapshot_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.DatabaseSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "snapshot_arn of DatabaseSnapshot", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.DatabaseSnapshot.snapshot_arn", "name": "snapshot_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.DatabaseSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "snapshot_arn of DatabaseSnapshot", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "snapshot_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DatabaseSnapshot.snapshot_id", "name": "snapshot_id", "type": "builtins.str"}}, "snapshot_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DatabaseSnapshot.snapshot_type", "name": "snapshot_type", "type": "builtins.str"}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DatabaseSnapshot.status", "name": "status", "type": "builtins.str"}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.DatabaseSnapshot.tags", "name": "tags", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.DatabaseSnapshot.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.DatabaseSnapshot"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of DatabaseSnapshot", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.rds.models.DatabaseSnapshot.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.rds.models.DatabaseSnapshot", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DbInstanceEngine": {".class": "SymbolTableNode", "cross_ref": "moto.rds.utils.DbInstanceEngine", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EventSubscription": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.rds.models.EventSubscription", "name": "EventSubscription", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.rds.models.EventSubscription", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.rds.models", "mro": ["moto.rds.models.EventSubscription", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.EventSubscription.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "arg_types": ["moto.rds.models.EventSubscription", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EventSubscription", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.EventSubscription.add_tags", "name": "add_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "arg_types": ["moto.rds.models.EventSubscription", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tags of EventSubscription", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "created_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.EventSubscription.created_at", "name": "created_at", "type": "builtins.str"}}, "customer_aws_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.EventSubscription.customer_aws_id", "name": "customer_aws_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.EventSubscription.enabled", "name": "enabled", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "es_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.EventSubscription.es_arn", "name": "es_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.EventSubscription"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "es_arn of EventSubscription", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.EventSubscription.es_arn", "name": "es_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.EventSubscription"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "es_arn of EventSubscription", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "event_categories": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.EventSubscription.event_categories", "name": "event_categories", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.EventSubscription.get_tags", "name": "get_tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.EventSubscription"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tags of EventSubscription", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.EventSubscription.region_name", "name": "region_name", "type": "builtins.str"}}, "remove_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.EventSubscription.remove_tags", "name": "remove_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "arg_types": ["moto.rds.models.EventSubscription", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_tags of EventSubscription", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sns_topic_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.EventSubscription.sns_topic_arn", "name": "sns_topic_arn", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "source_ids": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.EventSubscription.source_ids", "name": "source_ids", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "source_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.EventSubscription.source_type", "name": "source_type", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.EventSubscription.status", "name": "status", "type": "builtins.str"}}, "subscription_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.EventSubscription.subscription_name", "name": "subscription_name", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.EventSubscription.tags", "name": "tags", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.EventSubscription.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.EventSubscription"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of EventSubscription", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.rds.models.EventSubscription.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.rds.models.EventSubscription", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExportTask": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.rds.models.ExportTask", "name": "ExportTask", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.rds.models.ExportTask", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.rds.models", "mro": ["moto.rds.models.ExportTask", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "snapshot", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.ExportTask.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "snapshot", "kwargs"], "arg_types": ["moto.rds.models.ExportTask", {".class": "UnionType", "items": ["moto.rds.models.DatabaseSnapshot", "moto.rds.models.ClusterSnapshot"], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ExportTask", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "created_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ExportTask.created_at", "name": "created_at", "type": "builtins.str"}}, "export_only": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ExportTask.export_only", "name": "export_only", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "export_task_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ExportTask.export_task_identifier", "name": "export_task_identifier", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "iam_role_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ExportTask.iam_role_arn", "name": "iam_role_arn", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "kms_key_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ExportTask.kms_key_id", "name": "kms_key_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "s3_bucket_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ExportTask.s3_bucket_name", "name": "s3_bucket_name", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "s3_prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ExportTask.s3_prefix", "name": "s3_prefix", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "snapshot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ExportTask.snapshot", "name": "snapshot", "type": {".class": "UnionType", "items": ["moto.rds.models.DatabaseSnapshot", "moto.rds.models.ClusterSnapshot"], "uses_pep604_syntax": false}}}, "source_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ExportTask.source_arn", "name": "source_arn", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "source_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ExportTask.source_type", "name": "source_type", "type": "builtins.str"}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.ExportTask.status", "name": "status", "type": "builtins.str"}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.ExportTask.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.ExportTask"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of ExportTask", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.rds.models.ExportTask.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.rds.models.ExportTask", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExportTaskAlreadyExistsError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.ExportTaskAlreadyExistsError", "kind": "Gdef"}, "ExportTaskNotFoundError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.ExportTaskNotFoundError", "kind": "Gdef"}, "FilterDef": {".class": "SymbolTableNode", "cross_ref": "moto.rds.utils.FilterDef", "kind": "Gdef"}, "GlobalCluster": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.rds.models.GlobalCluster", "name": "GlobalCluster", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.rds.models.GlobalCluster", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.rds.models", "mro": ["moto.rds.models.GlobalCluster", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "global_cluster_identifier", "engine", "engine_version", "storage_encrypted", "deletion_protection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.GlobalCluster.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "global_cluster_identifier", "engine", "engine_version", "storage_encrypted", "deletion_protection"], "arg_types": ["moto.rds.models.GlobalCluster", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GlobalCluster", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deletion_protection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.GlobalCluster.deletion_protection", "name": "deletion_protection", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": ""}, {".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": false}}}, "engine": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.GlobalCluster.engine", "name": "engine", "type": "builtins.str"}}, "engine_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.GlobalCluster.engine_version", "name": "engine_version", "type": "builtins.str"}}, "global_cluster_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.GlobalCluster.global_cluster_arn", "name": "global_cluster_arn", "type": "builtins.str"}}, "global_cluster_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.GlobalCluster.global_cluster_identifier", "name": "global_cluster_identifier", "type": "builtins.str"}}, "global_cluster_resource_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.GlobalCluster.global_cluster_resource_id", "name": "global_cluster_resource_id", "type": "builtins.str"}}, "members": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.GlobalCluster.members", "name": "members", "type": {".class": "Instance", "args": ["moto.rds.models.Cluster"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "storage_encrypted": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.GlobalCluster.storage_encrypted", "name": "storage_encrypted", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": ""}, {".class": "NoneType"}, "builtins.bool"], "uses_pep604_syntax": false}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.GlobalCluster.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.GlobalCluster"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of GlobalCluster", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.rds.models.GlobalCluster.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.rds.models.GlobalCluster", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidDBClusterStateFault": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.InvalidDBClusterStateFault", "kind": "Gdef"}, "InvalidDBClusterStateFaultError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.InvalidDBClusterStateFaultError", "kind": "Gdef"}, "InvalidDBInstanceEngine": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.InvalidDBInstanceEngine", "kind": "Gdef"}, "InvalidDBInstanceIdentifier": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.InvalidDBInstanceIdentifier", "kind": "Gdef"}, "InvalidDBInstanceStateError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.InvalidDBInstanceStateError", "kind": "Gdef"}, "InvalidExportSourceStateError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.InvalidExportSourceStateError", "kind": "Gdef"}, "InvalidGlobalClusterStateFault": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.InvalidGlobalClusterStateFault", "kind": "Gdef"}, "InvalidParameterCombination": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.InvalidParameterCombination", "kind": "Gdef"}, "InvalidParameterValue": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.InvalidParameterValue", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NeptuneBackend": {".class": "SymbolTableNode", "cross_ref": "moto.neptune.models.NeptuneBackend", "kind": "Gdef"}, "OptionGroup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.rds.models.OptionGroup", "name": "OptionGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.rds.models.OptionGroup", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.rds.models", "mro": ["moto.rds.models.OptionGroup", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "engine_name", "major_engine_version", "region", "account_id", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.OptionGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "name", "engine_name", "major_engine_version", "region", "account_id", "description"], "arg_types": ["moto.rds.models.OptionGroup", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OptionGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "options_to_add"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.OptionGroup.add_options", "name": "add_options", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "options_to_add"], "arg_types": ["moto.rds.models.OptionGroup", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_options of OptionGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.OptionGroup.add_tags", "name": "add_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "arg_types": ["moto.rds.models.OptionGroup", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tags of OptionGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.OptionGroup.arn", "name": "arn", "type": "builtins.str"}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.OptionGroup.description", "name": "description", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "engine_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.OptionGroup.engine_name", "name": "engine_name", "type": "builtins.str"}}, "get_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.OptionGroup.get_tags", "name": "get_tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.OptionGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tags of OptionGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "major_engine_version": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.OptionGroup.major_engine_version", "name": "major_engine_version", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.OptionGroup.name", "name": "name", "type": "builtins.str"}}, "options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.OptionGroup.options", "name": "options", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "remove_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "options_to_remove"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.OptionGroup.remove_options", "name": "remove_options", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "options_to_remove"], "arg_types": ["moto.rds.models.OptionGroup", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_options of OptionGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.OptionGroup.remove_tags", "name": "remove_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "arg_types": ["moto.rds.models.OptionGroup", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_tags of OptionGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.OptionGroup.tags", "name": "tags", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.OptionGroup.to_json", "name": "to_json", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.OptionGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_json of OptionGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.OptionGroup.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.OptionGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of OptionGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vpcId": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.OptionGroup.vpcId", "name": "vpcId", "type": "builtins.str"}}, "vpc_and_non_vpc_instance_memberships": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.OptionGroup.vpc_and_non_vpc_instance_memberships", "name": "vpc_and_non_vpc_instance_memberships", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.rds.models.OptionGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.rds.models.OptionGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OptionGroupNotFoundFaultError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.OptionGroupNotFoundFaultError", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "RDSBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.rds.models.RDSBackend", "name": "RDSBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.rds.models", "mro": ["moto.rds.models.RDSBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RDSBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_db_cluster_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend._db_cluster_options", "name": "_db_cluster_options", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_filter_resources": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["resources", "filters", "resource_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.RDSBackend._filter_resources", "name": "_filter_resources", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["resources", "filters", "resource_class"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_filter_resources of RDSBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend._filter_resources", "name": "_filter_resources", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["resources", "filters", "resource_class"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_filter_resources of RDSBackend", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_merge_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["old_tags", "new_tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.RDSBackend._merge_tags", "name": "_merge_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old_tags", "new_tags"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_merge_tags of RDSBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend._merge_tags", "name": "_merge_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["old_tags", "new_tags"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_merge_tags of RDSBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_validate_db_identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["db_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.RDSBackend._validate_db_identifier", "name": "_validate_db_identifier", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["db_identifier"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_db_identifier of RDSBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend._validate_db_identifier", "name": "_validate_db_identifier", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["db_identifier"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_db_identifier of RDSBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "add_tags_to_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "arn", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.add_tags_to_resource", "name": "add_tags_to_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arn", "tags"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tags_to_resource of RDSBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn_regex": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.RDSBackend.arn_regex", "name": "arn_regex", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "authorize_security_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "security_group_name", "cidr_ip"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.authorize_security_group", "name": "authorize_security_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "security_group_name", "cidr_ip"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authorize_security_group of RDSBackend", "ret_type": "moto.rds.models.SecurityGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cancel_export_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "export_task_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.cancel_export_task", "name": "cancel_export_task", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "export_task_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancel_export_task of RDSBackend", "ret_type": "moto.rds.models.ExportTask", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cluster_snapshots": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.cluster_snapshots", "name": "cluster_snapshots", "type": {".class": "Instance", "args": ["builtins.str", "moto.rds.models.ClusterSnapshot"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "clusters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.clusters", "name": "clusters", "type": {".class": "Instance", "args": ["builtins.str", "moto.rds.models.Cluster"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "copy_db_cluster_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "source_snapshot_identifier", "target_snapshot_identifier", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.copy_db_cluster_snapshot", "name": "copy_db_cluster_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "source_snapshot_identifier", "target_snapshot_identifier", "tags"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_db_cluster_snapshot of RDSBackend", "ret_type": "moto.rds.models.ClusterSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_db_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "source_snapshot_identifier", "target_snapshot_identifier", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.copy_db_snapshot", "name": "copy_db_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "source_snapshot_identifier", "target_snapshot_identifier", "tags"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_db_snapshot of RDSBackend", "ret_type": "moto.rds.models.DatabaseSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_auto_cluster_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "db_cluster_identifier", "db_snapshot_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_auto_cluster_snapshot", "name": "create_auto_cluster_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "db_cluster_identifier", "db_snapshot_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_auto_cluster_snapshot of RDSBackend", "ret_type": "moto.rds.models.ClusterSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_auto_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "db_instance_identifier", "db_snapshot_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_auto_snapshot", "name": "create_auto_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "db_instance_identifier", "db_snapshot_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_auto_snapshot of RDSBackend", "ret_type": "moto.rds.models.DatabaseSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_db_cluster", "name": "create_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_cluster of RDSBackend", "ret_type": "moto.rds.models.Cluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_db_cluster_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "group_name", "family", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_db_cluster_parameter_group", "name": "create_db_cluster_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "group_name", "family", "description"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_cluster_parameter_group of RDSBackend", "ret_type": "moto.rds.models.DBClusterParameterGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_db_cluster_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "db_cluster_identifier", "db_snapshot_identifier", "snapshot_type", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_db_cluster_snapshot", "name": "create_db_cluster_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "db_cluster_identifier", "db_snapshot_identifier", "snapshot_type", "tags"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_cluster_snapshot of RDSBackend", "ret_type": "moto.rds.models.ClusterSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_db_instance", "name": "create_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_kwargs"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_instance of RDSBackend", "ret_type": "moto.rds.models.Database", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_db_instance_read_replica": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_db_instance_read_replica", "name": "create_db_instance_read_replica", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_kwargs"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_instance_read_replica of RDSBackend", "ret_type": "moto.rds.models.Database", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_db_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_parameter_group_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_db_parameter_group", "name": "create_db_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_parameter_group_kwargs"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_parameter_group of RDSBackend", "ret_type": "moto.rds.models.DBParameterGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_db_security_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "group_name", "description", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_db_security_group", "name": "create_db_security_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "group_name", "description", "tags"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_security_group of RDSBackend", "ret_type": "moto.rds.models.SecurityGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_db_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "db_instance_identifier", "db_snapshot_identifier", "snapshot_type", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_db_snapshot", "name": "create_db_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "db_instance_identifier", "db_snapshot_identifier", "snapshot_type", "tags"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_db_snapshot of RDSBackend", "ret_type": "moto.rds.models.DatabaseSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_event_subscription": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_event_subscription", "name": "create_event_subscription", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_event_subscription of RDSBackend", "ret_type": "moto.rds.models.EventSubscription", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_global_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "global_cluster_identifier", "source_db_cluster_identifier", "engine", "engine_version", "storage_encrypted", "deletion_protection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_global_cluster", "name": "create_global_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "global_cluster_identifier", "source_db_cluster_identifier", "engine", "engine_version", "storage_encrypted", "deletion_protection"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_global_cluster of RDSBackend", "ret_type": "moto.rds.models.GlobalCluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_option_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "option_group_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_option_group", "name": "create_option_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option_group_kwargs"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_option_group of RDSBackend", "ret_type": "moto.rds.models.OptionGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_subnet_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "subnet_name", "description", "subnets", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.create_subnet_group", "name": "create_subnet_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "subnet_name", "description", "subnets", "tags"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_subnet_group of RDSBackend", "ret_type": "moto.rds.models.SubnetGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "database_snapshots": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.database_snapshots", "name": "database_snapshots", "type": {".class": "Instance", "args": ["builtins.str", "moto.rds.models.DatabaseSnapshot"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "databases": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.databases", "name": "databases", "type": {".class": "Instance", "args": ["builtins.str", "moto.rds.models.Database"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "db_cluster_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.RDSBackend.db_cluster_options", "name": "db_cluster_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.RDSBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "db_cluster_options of RDSBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.db_cluster_options", "name": "db_cluster_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.RDSBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "db_cluster_options of RDSBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "db_cluster_parameter_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.db_cluster_parameter_groups", "name": "db_cluster_parameter_groups", "type": {".class": "Instance", "args": ["builtins.str", "moto.rds.models.DBClusterParameterGroup"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "db_parameter_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.db_parameter_groups", "name": "db_parameter_groups", "type": {".class": "Instance", "args": ["builtins.str", "moto.rds.models.DBParameterGroup"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "default_vpc_endpoint_service": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["service_region", "zones"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.RDSBackend.default_vpc_endpoint_service", "name": "default_vpc_endpoint_service", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["service_region", "zones"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_vpc_endpoint_service of RDSBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.default_vpc_endpoint_service", "name": "default_vpc_endpoint_service", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["service_region", "zones"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_vpc_endpoint_service of RDSBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "cluster_identifier", "snapshot_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.delete_db_cluster", "name": "delete_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "cluster_identifier", "snapshot_name"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_cluster of RDSBackend", "ret_type": "moto.rds.models.Cluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_db_cluster_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.delete_db_cluster_parameter_group", "name": "delete_db_cluster_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "group_name"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_cluster_parameter_group of RDSBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_db_cluster_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_snapshot_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.delete_db_cluster_snapshot", "name": "delete_db_cluster_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_snapshot_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_cluster_snapshot of RDSBackend", "ret_type": "moto.rds.models.ClusterSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "db_instance_identifier", "db_snapshot_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.delete_db_instance", "name": "delete_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "db_instance_identifier", "db_snapshot_name"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_instance of RDSBackend", "ret_type": "moto.rds.models.Database", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_db_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_parameter_group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.delete_db_parameter_group", "name": "delete_db_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_parameter_group_name"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_parameter_group of RDSBackend", "ret_type": "moto.rds.models.DBParameterGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_db_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_snapshot_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.delete_db_snapshot", "name": "delete_db_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_snapshot_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_db_snapshot of RDSBackend", "ret_type": "moto.rds.models.DatabaseSnapshot", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_event_subscription": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "subscription_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.delete_event_subscription", "name": "delete_event_subscription", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "subscription_name"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_event_subscription of RDSBackend", "ret_type": "moto.rds.models.EventSubscription", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_global_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "global_cluster_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.delete_global_cluster", "name": "delete_global_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "global_cluster_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_global_cluster of RDSBackend", "ret_type": "moto.rds.models.GlobalCluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_option_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "option_group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.delete_option_group", "name": "delete_option_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option_group_name"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_option_group of RDSBackend", "ret_type": "moto.rds.models.OptionGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_security_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "security_group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.delete_security_group", "name": "delete_security_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "security_group_name"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_security_group of RDSBackend", "ret_type": "moto.rds.models.SecurityGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_subnet_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "subnet_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.delete_subnet_group", "name": "delete_subnet_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "subnet_name"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_subnet_group of RDSBackend", "ret_type": "moto.rds.models.SubnetGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_db_cluster_parameter_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_db_cluster_parameter_groups", "name": "describe_db_cluster_parameter_groups", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "group_name"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_cluster_parameter_groups of RDSBackend", "ret_type": {".class": "Instance", "args": ["moto.rds.models.DBClusterParameterGroup"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_db_cluster_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_db_cluster_parameters", "name": "describe_db_cluster_parameters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.RDSBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_cluster_parameters of RDSBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_db_cluster_snapshots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "db_cluster_identifier", "db_snapshot_identifier", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_db_cluster_snapshots", "name": "describe_db_cluster_snapshots", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "db_cluster_identifier", "db_snapshot_identifier", "filters"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_cluster_snapshots of RDSBackend", "ret_type": {".class": "Instance", "args": ["moto.rds.models.ClusterSnapshot"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_db_clusters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "cluster_identifier", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_db_clusters", "name": "describe_db_clusters", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "cluster_identifier", "filters"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_clusters of RDSBackend", "ret_type": {".class": "Instance", "args": ["moto.rds.models.Cluster"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_db_instances": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "db_instance_identifier", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_db_instances", "name": "describe_db_instances", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "db_instance_identifier", "filters"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_instances of RDSBackend", "ret_type": {".class": "Instance", "args": ["moto.rds.models.Database"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_db_parameter_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_parameter_group_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_db_parameter_groups", "name": "describe_db_parameter_groups", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_parameter_group_kwargs"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_parameter_groups of RDSBackend", "ret_type": {".class": "Instance", "args": ["moto.rds.models.DBParameterGroup"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_db_snapshots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "db_instance_identifier", "db_snapshot_identifier", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_db_snapshots", "name": "describe_db_snapshots", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "db_instance_identifier", "db_snapshot_identifier", "filters"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_snapshots of RDSBackend", "ret_type": {".class": "Instance", "args": ["moto.rds.models.DatabaseSnapshot"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_db_subnet_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "subnet_group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_db_subnet_groups", "name": "describe_db_subnet_groups", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "subnet_group_name"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_db_subnet_groups of RDSBackend", "ret_type": {".class": "Instance", "args": ["moto.rds.models.SubnetGroup"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_event_subscriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "subscription_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_event_subscriptions", "name": "describe_event_subscriptions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "subscription_name"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_event_subscriptions of RDSBackend", "ret_type": {".class": "Instance", "args": ["moto.rds.models.EventSubscription"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_export_tasks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "export_task_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_export_tasks", "name": "describe_export_tasks", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "export_task_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_export_tasks of RDSBackend", "ret_type": {".class": "Instance", "args": ["moto.rds.models.ExportTask"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_global_clusters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_global_clusters", "name": "describe_global_clusters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.RDSBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_global_clusters of RDSBackend", "ret_type": {".class": "Instance", "args": ["moto.rds.models.GlobalCluster"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_option_group_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["engine_name", "major_engine_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.RDSBackend.describe_option_group_options", "name": "describe_option_group_options", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["engine_name", "major_engine_version"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_option_group_options of RDSBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.describe_option_group_options", "name": "describe_option_group_options", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["engine_name", "major_engine_version"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_option_group_options of RDSBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "describe_option_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "option_group_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_option_groups", "name": "describe_option_groups", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "option_group_kwargs"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_option_groups of RDSBackend", "ret_type": {".class": "Instance", "args": ["moto.rds.models.OptionGroup"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_orderable_db_instance_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "engine", "engine_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_orderable_db_instance_options", "name": "describe_orderable_db_instance_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "engine", "engine_version"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_orderable_db_instance_options of RDSBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_security_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "security_group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.describe_security_groups", "name": "describe_security_groups", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "security_group_name"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_security_groups of RDSBackend", "ret_type": {".class": "Instance", "args": ["moto.rds.models.SecurityGroup"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "event_subscriptions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.event_subscriptions", "name": "event_subscriptions", "type": {".class": "Instance", "args": ["builtins.str", "moto.rds.models.EventSubscription"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "export_tasks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.export_tasks", "name": "export_tasks", "type": {".class": "Instance", "args": ["builtins.str", "moto.rds.models.ExportTask"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "find_db_from_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.find_db_from_id", "name": "find_db_from_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_id"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_db_from_id of RDSBackend", "ret_type": "moto.rds.models.Database", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "global_clusters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.global_clusters", "name": "global_clusters", "type": {".class": "Instance", "args": ["builtins.str", "moto.rds.models.GlobalCluster"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_tags_for_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.list_tags_for_resource", "name": "list_tags_for_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arn"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_tags_for_resource of RDSBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "modify_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.modify_db_cluster", "name": "modify_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_cluster of RDSBackend", "ret_type": "moto.rds.models.Cluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "modify_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "db_instance_identifier", "db_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.modify_db_instance", "name": "modify_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "db_instance_identifier", "db_kwargs"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_instance of RDSBackend", "ret_type": "moto.rds.models.Database", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "modify_db_parameter_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "db_parameter_group_name", "db_parameter_group_parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.modify_db_parameter_group", "name": "modify_db_parameter_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "db_parameter_group_name", "db_parameter_group_parameters"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_parameter_group of RDSBackend", "ret_type": "moto.rds.models.DBParameterGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "modify_db_subnet_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "subnet_name", "description", "subnets"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.modify_db_subnet_group", "name": "modify_db_subnet_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "subnet_name", "description", "subnets"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": ["moto.ec2.models.subnets.Subnet"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_db_subnet_group of RDSBackend", "ret_type": "moto.rds.models.SubnetGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "modify_option_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "option_group_name", "options_to_include", "options_to_remove"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.modify_option_group", "name": "modify_option_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "option_group_name", "options_to_include", "options_to_remove"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_option_group of RDSBackend", "ret_type": "moto.rds.models.OptionGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "neptune": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.RDSBackend.neptune", "name": "neptune", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.RDSBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "neptune of RDSBackend", "ret_type": "moto.neptune.models.NeptuneBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.neptune", "name": "neptune", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.RDSBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "neptune of RDSBackend", "ret_type": "moto.neptune.models.NeptuneBackend", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "option_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.option_groups", "name": "option_groups", "type": {".class": "Instance", "args": ["builtins.str", "moto.rds.models.OptionGroup"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "promote_read_replica": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.promote_read_replica", "name": "promote_read_replica", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_kwargs"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "promote_read_replica of RDSBackend", "ret_type": "moto.rds.models.Database", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "promote_read_replica_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_cluster_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.promote_read_replica_db_cluster", "name": "promote_read_replica_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_cluster_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "promote_read_replica_db_cluster of RDSBackend", "ret_type": "moto.rds.models.Cluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reboot_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_instance_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.reboot_db_instance", "name": "reboot_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_instance_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reboot_db_instance of RDSBackend", "ret_type": "moto.rds.models.Database", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_from_global_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "global_cluster_identifier", "db_cluster_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.remove_from_global_cluster", "name": "remove_from_global_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "global_cluster_identifier", "db_cluster_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_from_global_cluster of RDSBackend", "ret_type": {".class": "UnionType", "items": ["moto.rds.models.GlobalCluster", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_tags_from_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "arn", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.remove_tags_from_resource", "name": "remove_tags_from_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arn", "tag_keys"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_tags_from_resource of RDSBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.RDSBackend"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of RDSBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "restore_db_cluster_from_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_snapshot_id", "overrides"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.restore_db_cluster_from_snapshot", "name": "restore_db_cluster_from_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_snapshot_id", "overrides"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore_db_cluster_from_snapshot of RDSBackend", "ret_type": "moto.rds.models.Cluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "restore_db_instance_from_db_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_snapshot_id", "overrides"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.restore_db_instance_from_db_snapshot", "name": "restore_db_instance_from_db_snapshot", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_snapshot_id", "overrides"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore_db_instance_from_db_snapshot of RDSBackend", "ret_type": "moto.rds.models.Database", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "security_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.security_groups", "name": "security_groups", "type": {".class": "Instance", "args": ["builtins.str", "moto.rds.models.SecurityGroup"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "start_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cluster_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.start_db_cluster", "name": "start_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cluster_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_db_cluster of RDSBackend", "ret_type": "moto.rds.models.Cluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_instance_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.start_db_instance", "name": "start_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_instance_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_db_instance of RDSBackend", "ret_type": "moto.rds.models.Database", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_export_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.start_export_task", "name": "start_export_task", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "kwargs"], "arg_types": ["moto.rds.models.RDSBackend", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_export_task of RDSBackend", "ret_type": "moto.rds.models.ExportTask", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stop_db_cluster": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cluster_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.stop_db_cluster", "name": "stop_db_cluster", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cluster_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop_db_cluster of RDSBackend", "ret_type": "moto.rds.models.Cluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stop_db_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "db_instance_identifier", "db_snapshot_identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.RDSBackend.stop_db_instance", "name": "stop_db_instance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "db_instance_identifier", "db_snapshot_identifier"], "arg_types": ["moto.rds.models.RDSBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop_db_instance of RDSBackend", "ret_type": "moto.rds.models.Database", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "subnet_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.RDSBackend.subnet_groups", "name": "subnet_groups", "type": {".class": "Instance", "args": ["builtins.str", "moto.rds.models.SubnetGroup"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.rds.models.RDSBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.rds.models.RDSBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RDSClientError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.RDSClientError", "kind": "Gdef"}, "SecurityGroup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["delete_from_cloudformation_json", 1], ["has_cfn_attr", 1], ["is_created", 1], ["update_from_cloudformation_json", 1]], "alt_promote": null, "bases": ["moto.core.common_models.CloudFormationModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.rds.models.SecurityGroup", "name": "SecurityGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "moto.rds.models.SecurityGroup", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.rds.models", "mro": ["moto.rds.models.SecurityGroup", "moto.core.common_models.CloudFormationModel", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "group_name", "description", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SecurityGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "group_name", "description", "tags"], "arg_types": ["moto.rds.models.SecurityGroup", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SecurityGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SecurityGroup.add_tags", "name": "add_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "arg_types": ["moto.rds.models.SecurityGroup", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tags of SecurityGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "authorize_cidr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cidr_ip"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SecurityGroup.authorize_cidr", "name": "authorize_cidr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cidr_ip"], "arg_types": ["moto.rds.models.SecurityGroup", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authorize_cidr of SecurityGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "authorize_security_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "security_group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SecurityGroup.authorize_security_group", "name": "authorize_security_group", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "security_group"], "arg_types": ["moto.rds.models.SecurityGroup", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "authorize_security_group of SecurityGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cloudformation_name_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.SecurityGroup.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of SecurityGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.SecurityGroup.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of SecurityGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cloudformation_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.SecurityGroup.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of SecurityGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.SecurityGroup.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of SecurityGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_from_cloudformation_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "moto.rds.models.SecurityGroup.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.rds.models.SecurityGroup"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of SecurityGroup", "ret_type": "moto.rds.models.SecurityGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.SecurityGroup.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.rds.models.SecurityGroup"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of SecurityGroup", "ret_type": "moto.rds.models.SecurityGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SecurityGroup.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "region_name"], "arg_types": ["moto.rds.models.SecurityGroup", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete of SecurityGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SecurityGroup.description", "name": "description", "type": "builtins.str"}}, "ec2_security_groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.SecurityGroup.ec2_security_groups", "name": "ec2_security_groups", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SecurityGroup.get_tags", "name": "get_tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.SecurityGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tags of SecurityGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "group_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SecurityGroup.group_name", "name": "group_name", "type": "builtins.str"}}, "ip_ranges": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.rds.models.SecurityGroup.ip_ranges", "name": "ip_ranges", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "owner_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SecurityGroup.owner_id", "name": "owner_id", "type": "builtins.str"}}, "remove_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SecurityGroup.remove_tags", "name": "remove_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "arg_types": ["moto.rds.models.SecurityGroup", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_tags of SecurityGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SecurityGroup.status", "name": "status", "type": "builtins.str"}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SecurityGroup.tags", "name": "tags", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SecurityGroup.to_json", "name": "to_json", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.SecurityGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_json of SecurityGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SecurityGroup.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.SecurityGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of SecurityGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vpc_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SecurityGroup.vpc_id", "name": "vpc_id", "type": {".class": "NoneType"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.rds.models.SecurityGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.rds.models.SecurityGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SnapshotQuotaExceededError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.SnapshotQuotaExceededError", "kind": "Gdef"}, "Subnet": {".class": "SymbolTableNode", "cross_ref": "moto.ec2.models.subnets.Subnet", "kind": "Gdef"}, "SubnetGroup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["delete_from_cloudformation_json", 1], ["has_cfn_attr", 1], ["is_created", 1], ["update_from_cloudformation_json", 1]], "alt_promote": null, "bases": ["moto.core.common_models.CloudFormationModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.rds.models.SubnetGroup", "name": "SubnetGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "moto.rds.models.SubnetGroup", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.rds.models", "mro": ["moto.rds.models.SubnetGroup", "moto.core.common_models.CloudFormationModel", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "subnet_name", "description", "subnets", "tags", "region", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SubnetGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "subnet_name", "description", "subnets", "tags", "region", "account_id"], "arg_types": ["moto.rds.models.SubnetGroup", "builtins.str", "builtins.str", {".class": "Instance", "args": ["moto.ec2.models.subnets.Subnet"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SubnetGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SubnetGroup.account_id", "name": "account_id", "type": "builtins.str"}}, "add_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SubnetGroup.add_tags", "name": "add_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "arg_types": ["moto.rds.models.SubnetGroup", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_tags of SubnetGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cloudformation_name_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.SubnetGroup.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of SubnetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.SubnetGroup.cloudformation_name_type", "name": "cloudformation_name_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_name_type of SubnetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "cloudformation_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.rds.models.SubnetGroup.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of SubnetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.SubnetGroup.cloudformation_type", "name": "cloudformation_type", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cloudformation_type of SubnetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_from_cloudformation_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "moto.rds.models.SubnetGroup.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.rds.models.SubnetGroup"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of SubnetGroup", "ret_type": "moto.rds.models.SubnetGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "moto.rds.models.SubnetGroup.create_from_cloudformation_json", "name": "create_from_cloudformation_json", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["cls", "resource_name", "cloudformation_json", "account_id", "region_name", "kwargs"], "arg_types": [{".class": "TypeType", "item": "moto.rds.models.SubnetGroup"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_from_cloudformation_json of SubnetGroup", "ret_type": "moto.rds.models.SubnetGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SubnetGroup.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "account_id", "region_name"], "arg_types": ["moto.rds.models.SubnetGroup", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete of SubnetGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SubnetGroup.description", "name": "description", "type": "builtins.str"}}, "get_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SubnetGroup.get_tags", "name": "get_tags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.SubnetGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_tags of SubnetGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "region": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SubnetGroup.region", "name": "region", "type": "builtins.str"}}, "remove_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SubnetGroup.remove_tags", "name": "remove_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tag_keys"], "arg_types": ["moto.rds.models.SubnetGroup", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_tags of SubnetGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sg_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "moto.rds.models.SubnetGroup.sg_arn", "name": "sg_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.SubnetGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sg_arn of SubnetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "moto.rds.models.SubnetGroup.sg_arn", "name": "sg_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.SubnetGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sg_arn of SubnetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "status": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SubnetGroup.status", "name": "status", "type": "builtins.str"}}, "subnet_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SubnetGroup.subnet_name", "name": "subnet_name", "type": "builtins.str"}}, "subnets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SubnetGroup.subnets", "name": "subnets", "type": {".class": "Instance", "args": ["moto.ec2.models.subnets.Subnet"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SubnetGroup.tags", "name": "tags", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SubnetGroup.to_json", "name": "to_json", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.SubnetGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_json of SubnetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_xml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.SubnetGroup.to_xml", "name": "to_xml", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.rds.models.SubnetGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_xml of SubnetGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "vpc_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.rds.models.SubnetGroup.vpc_id", "name": "vpc_id", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.rds.models.SubnetGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.rds.models.SubnetGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SubscriptionAlreadyExistError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.SubscriptionAlreadyExistError", "kind": "Gdef"}, "SubscriptionNotFoundError": {".class": "SymbolTableNode", "cross_ref": "moto.rds.exceptions.SubscriptionNotFoundError", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Template": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Template", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.rds.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.rds.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.rds.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.rds.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.rds.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.rds.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "apply_filter": {".class": "SymbolTableNode", "cross_ref": "moto.rds.utils.apply_filter", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "ec2_backends": {".class": "SymbolTableNode", "cross_ref": "moto.ec2.models.ec2_backends", "kind": "Gdef"}, "find_cluster": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cluster_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.rds.models.find_cluster", "name": "find_cluster", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cluster_arn"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_cluster", "ret_type": "moto.rds.models.Cluster", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iso_8601_datetime_with_milliseconds": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.iso_8601_datetime_with_milliseconds", "kind": "Gdef"}, "load_resource": {".class": "SymbolTableNode", "cross_ref": "moto.utilities.utils.load_resource", "kind": "Gdef"}, "merge_filters": {".class": "SymbolTableNode", "cross_ref": "moto.rds.utils.merge_filters", "kind": "Gdef"}, "neptune_backends": {".class": "SymbolTableNode", "cross_ref": "moto.neptune.models.neptune_backends", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}, "rds_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.rds.models.rds_backends", "name": "rds_backends", "type": {".class": "Instance", "args": ["moto.rds.models.RDSBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "re_compile": {".class": "SymbolTableNode", "cross_ref": "re.compile", "kind": "Gdef"}, "string": {".class": "SymbolTableNode", "cross_ref": "string", "kind": "Gdef"}, "valid_preferred_maintenance_window": {".class": "SymbolTableNode", "cross_ref": "moto.rds.utils.valid_preferred_maintenance_window", "kind": "Gdef"}, "validate_filters": {".class": "SymbolTableNode", "cross_ref": "moto.rds.utils.validate_filters", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/rds/models.py"}