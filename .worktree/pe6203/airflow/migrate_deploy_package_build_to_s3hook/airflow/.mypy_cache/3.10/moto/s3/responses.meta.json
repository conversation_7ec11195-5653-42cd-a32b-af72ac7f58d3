{"data_mtime": 1756972668, "dep_lines": [6, 11, 12, 13, 18, 24, 26, 56, 66, 67, 1313, 3, 6, 10, 259, 1, 2, 3, 4, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 20, 10, 20, 10, 10, 20, 5, 20, 5, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["xml.dom.minidom", "moto.core.common_types", "moto.core.responses", "moto.core.utils", "moto.s3bucket_path.utils", "moto.utilities.aws_headers", "moto.s3.exceptions", "moto.s3.models", "moto.s3.select_object_content", "moto.s3.utils", "moto.iam.access_control", "urllib.parse", "xml.dom", "moto.settings", "moto.s3control", "io", "re", "urllib", "typing", "moto", "builtins", "os", "sys", "string", "inspect", "traceback", "pprint", "collections", "types", "itertools", "warnings", "copy", "html", "_frozen_importlib", "_typeshed", "abc", "enum", "jinja2", "jinja2.environment", "moto.core", "moto.core.base_backend", "moto.core.common_models", "moto.core.exceptions", "moto.moto_api", "moto.moto_api._internal", "moto.moto_api._internal.managed_state_model", "moto.utilities", "typing_extensions", "werkzeug", "werkzeug.exceptions", "xml", "xml.dom.minicompat", "xml.dom.xmlbuilder", "xml.sax", "xml.sax.xmlreader"], "hash": "e14549353f3ae6bf5f8e6526c3f3c29c480f540c", "id": "moto.s3.responses", "ignore_all": true, "interface_hash": "4821883861a13d59da290fa500d24e4fdd4c9ddb", "mtime": 1743775661, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/s3/responses.py", "plugin_data": null, "size": 126501, "suppressed": ["xmltodict"], "version_id": "1.15.0"}