{".class": "MypyFile", "_fullname": "moto.quicksight.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "QuickSightBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.quicksight.models.QuickSightBackend", "name": "QuickSightBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.quicksight.models", "mro": ["moto.quicksight.models.QuickSightBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QuickSightBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_data_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data_set_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.create_data_set", "name": "create_data_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data_set_id", "name"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_data_set of QuickSightBackend", "ret_type": "moto.quicksight.models.QuicksightDataSet", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "group_name", "description", "aws_account_id", "namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.create_group", "name": "create_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "group_name", "description", "aws_account_id", "namespace"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_group of QuickSightBackend", "ret_type": "moto.quicksight.models.QuicksightGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_group_membership": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "group_name", "user_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.create_group_membership", "name": "create_group_membership", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "group_name", "user_name"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_group_membership of QuickSightBackend", "ret_type": "moto.quicksight.models.QuicksightMembership", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_ingestion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data_set_id", "ingestion_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.create_ingestion", "name": "create_ingestion", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data_set_id", "ingestion_id"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_ingestion of QuickSightBackend", "ret_type": "moto.quicksight.models.QuicksightIngestion", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.delete_group", "name": "delete_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "group_name"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_group of QuickSightBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "user_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.delete_user", "name": "delete_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "user_name"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_user of QuickSightBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.describe_group", "name": "describe_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "group_name"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_group of QuickSightBackend", "ret_type": "moto.quicksight.models.QuicksightGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_group_membership": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "group_name", "user_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.describe_group_membership", "name": "describe_group_membership", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "group_name", "user_name"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_group_membership of QuickSightBackend", "ret_type": "moto.quicksight.models.QuicksightMembership", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "user_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.describe_user", "name": "describe_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "user_name"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_user of QuickSightBackend", "ret_type": "moto.quicksight.models.QuicksightUser", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "groups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.quicksight.models.QuickSightBackend.groups", "name": "groups", "type": {".class": "Instance", "args": ["builtins.str", "moto.quicksight.models.QuicksightGroup"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_group_memberships": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "group_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.list_group_memberships", "name": "list_group_memberships", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "group_name"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_group_memberships of QuickSightBackend", "ret_type": {".class": "Instance", "args": ["moto.quicksight.models.QuicksightMembership"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_groups": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.list_groups", "name": "list_groups", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_groups of QuickSightBackend", "ret_type": {".class": "Instance", "args": ["moto.quicksight.models.QuicksightGroup"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_users": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.list_users", "name": "list_users", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_users of QuickSightBackend", "ret_type": {".class": "Instance", "args": ["moto.quicksight.models.QuicksightUser"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "register_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "identity_type", "email", "user_role", "aws_account_id", "namespace", "user_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.register_user", "name": "register_user", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "identity_type", "email", "user_role", "aws_account_id", "namespace", "user_name"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_user of QuickSightBackend", "ret_type": "moto.quicksight.models.QuicksightUser", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "group_name", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuickSightBackend.update_group", "name": "update_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "aws_account_id", "namespace", "group_name", "description"], "arg_types": ["moto.quicksight.models.QuickSightBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_group of QuickSightBackend", "ret_type": "moto.quicksight.models.QuicksightGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "users": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.quicksight.models.QuickSightBackend.users", "name": "users", "type": {".class": "Instance", "args": ["builtins.str", "moto.quicksight.models.QuicksightUser"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.quicksight.models.QuickSightBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.quicksight.models.QuickSightBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QuicksightDataSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.quicksight.models.QuicksightDataSet", "name": "QuicksightDataSet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightDataSet", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.quicksight.models", "mro": ["moto.quicksight.models.QuicksightDataSet", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "_id", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightDataSet.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "_id", "name"], "arg_types": ["moto.quicksight.models.QuicksightDataSet", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QuicksightDataSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightDataSet._id", "name": "_id", "type": "builtins.str"}}, "account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightDataSet.account_id", "name": "account_id", "type": "builtins.str"}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightDataSet.arn", "name": "arn", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightDataSet.name", "name": "name", "type": "builtins.str"}}, "region": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightDataSet.region", "name": "region", "type": "builtins.str"}}, "to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightDataSet.to_json", "name": "to_json", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.quicksight.models.QuicksightDataSet"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_json of QuicksightDataSet", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.quicksight.models.QuicksightDataSet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.quicksight.models.QuicksightDataSet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QuicksightGroup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.quicksight.models.QuicksightGroup", "name": "QuicksightGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightGroup", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.quicksight.models", "mro": ["moto.quicksight.models.QuicksightGroup", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "region", "group_name", "description", "aws_account_id", "namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "region", "group_name", "description", "aws_account_id", "namespace"], "arg_types": ["moto.quicksight.models.QuicksightGroup", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QuicksightGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_member": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "user_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightGroup.add_member", "name": "add_member", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "user_name"], "arg_types": ["moto.quicksight.models.QuicksightGroup", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_member of QuicksightGroup", "ret_type": "moto.quicksight.models.QuicksightMembership", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightGroup.arn", "name": "arn", "type": "builtins.str"}}, "aws_account_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightGroup.aws_account_id", "name": "aws_account_id", "type": "builtins.str"}}, "delete_member": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "user_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightGroup.delete_member", "name": "delete_member", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "user_name"], "arg_types": ["moto.quicksight.models.QuicksightGroup", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_member of QuicksightGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightGroup.description", "name": "description", "type": "builtins.str"}}, "get_member": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "user_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightGroup.get_member", "name": "get_member", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "user_name"], "arg_types": ["moto.quicksight.models.QuicksightGroup", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_member of QuicksightGroup", "ret_type": "moto.quicksight.models.QuicksightMembership", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "group_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightGroup.group_name", "name": "group_name", "type": "builtins.str"}}, "list_members": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightGroup.list_members", "name": "list_members", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.quicksight.models.QuicksightGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_members of QuicksightGroup", "ret_type": {".class": "Instance", "args": ["moto.quicksight.models.QuicksightMembership"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "members": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.quicksight.models.QuicksightGroup.members", "name": "members", "type": {".class": "Instance", "args": ["builtins.str", "moto.quicksight.models.QuicksightMembership"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "namespace": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightGroup.namespace", "name": "namespace", "type": "builtins.str"}}, "region": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightGroup.region", "name": "region", "type": "builtins.str"}}, "to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightGroup.to_json", "name": "to_json", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.quicksight.models.QuicksightGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_json of QuicksightGroup", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.quicksight.models.QuicksightGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.quicksight.models.QuicksightGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QuicksightIngestion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.quicksight.models.QuicksightIngestion", "name": "QuicksightIngestion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightIngestion", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.quicksight.models", "mro": ["moto.quicksight.models.QuicksightIngestion", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "data_set_id", "ingestion_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightIngestion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "data_set_id", "ingestion_id"], "arg_types": ["moto.quicksight.models.QuicksightIngestion", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QuicksightIngestion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightIngestion.arn", "name": "arn", "type": "builtins.str"}}, "ingestion_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightIngestion.ingestion_id", "name": "ingestion_id", "type": "builtins.str"}}, "to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightIngestion.to_json", "name": "to_json", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.quicksight.models.QuicksightIngestion"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_json of QuicksightIngestion", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.quicksight.models.QuicksightIngestion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.quicksight.models.QuicksightIngestion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QuicksightMembership": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.quicksight.models.QuicksightMembership", "name": "QuicksightMembership", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightMembership", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.quicksight.models", "mro": ["moto.quicksight.models.QuicksightMembership", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "group", "user"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightMembership.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "group", "user"], "arg_types": ["moto.quicksight.models.QuicksightMembership", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QuicksightMembership", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightMembership.arn", "name": "arn", "type": "builtins.str"}}, "group": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightMembership.group", "name": "group", "type": "builtins.str"}}, "to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightMembership.to_json", "name": "to_json", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.quicksight.models.QuicksightMembership"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_json of QuicksightMembership", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "user": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightMembership.user", "name": "user", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.quicksight.models.QuicksightMembership.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.quicksight.models.QuicksightMembership", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QuicksightUser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.quicksight.models.QuicksightUser", "name": "QuicksightUser", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightUser", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.quicksight.models", "mro": ["moto.quicksight.models.QuicksightUser", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "email", "identity_type", "username", "user_role"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightUser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "account_id", "region", "email", "identity_type", "username", "user_role"], "arg_types": ["moto.quicksight.models.QuicksightUser", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of QuicksightUser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "active": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightUser.active", "name": "active", "type": "builtins.bool"}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightUser.arn", "name": "arn", "type": "builtins.str"}}, "email": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightUser.email", "name": "email", "type": "builtins.str"}}, "identity_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightUser.identity_type", "name": "identity_type", "type": "builtins.str"}}, "principal_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightUser.principal_id", "name": "principal_id", "type": "builtins.str"}}, "to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models.QuicksightUser.to_json", "name": "to_json", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.quicksight.models.QuicksightUser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_json of QuicksightUser", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "user_role": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightUser.user_role", "name": "user_role", "type": "builtins.str"}}, "username": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.quicksight.models.QuicksightUser.username", "name": "username", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.quicksight.models.QuicksightUser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.quicksight.models.QuicksightUser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceNotFoundException": {".class": "SymbolTableNode", "cross_ref": "moto.quicksight.exceptions.ResourceNotFoundException", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.quicksight.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.quicksight.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.quicksight.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.quicksight.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.quicksight.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.quicksight.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_create_id": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["aws_account_id", "namespace", "_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.quicksight.models._create_id", "name": "_create_id", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["aws_account_id", "namespace", "_id"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_id", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "quicksight_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.quicksight.models.quicksight_backends", "name": "quicksight_backends", "type": {".class": "Instance", "args": ["moto.quicksight.models.QuickSightBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/quicksight/models.py"}