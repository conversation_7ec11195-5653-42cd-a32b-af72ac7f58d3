{".class": "MypyFile", "_fullname": "moto.s3.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AccessDeniedByLock": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.AccessDeniedByLock", "name": "AccessDeniedByLock", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.AccessDeniedByLock", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.AccessDeniedByLock", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.AccessDeniedByLock.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.AccessDeniedByLock"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AccessDeniedByLock", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.AccessDeniedByLock.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.AccessDeniedByLock.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.AccessDeniedByLock", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AccessForbidden": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.AccessForbidden", "name": "AccessForbidden", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.AccessForbidden", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.AccessForbidden", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.AccessForbidden.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "arg_types": ["moto.s3.exceptions.AccessForbidden", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AccessForbidden", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.AccessForbidden.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.AccessForbidden.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.AccessForbidden", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BadRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.BadRequest", "name": "BadRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BadRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.BadRequest", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BadRequest.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "arg_types": ["moto.s3.exceptions.BadRequest", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BadRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.BadRequest.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.BadRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.BadRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BucketAccessDeniedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.BucketError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.BucketAccessDeniedError", "name": "BucketAccessDeniedError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketAccessDeniedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.BucketAccessDeniedError", "moto.s3.exceptions.BucketError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketAccessDeniedError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["moto.s3.exceptions.BucketAccessDeniedError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BucketAccessDeniedError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.BucketAccessDeniedError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.BucketAccessDeniedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.BucketAccessDeniedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BucketAlreadyExists": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.BucketError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.BucketAlreadyExists", "name": "BucketAlreadyExists", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketAlreadyExists", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.BucketAlreadyExists", "moto.s3.exceptions.BucketError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketAlreadyExists.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["moto.s3.exceptions.BucketAlreadyExists", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BucketAlreadyExists", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.BucketAlreadyExists.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.BucketAlreadyExists.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.BucketAlreadyExists", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BucketError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.BucketError", "name": "BucketError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.BucketError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["moto.s3.exceptions.BucketError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BucketError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.BucketError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.BucketError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BucketInvalidAccessKeyIdError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.BucketInvalidAccessKeyIdError", "name": "BucketInvalidAccessKeyIdError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketInvalidAccessKeyIdError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.BucketInvalidAccessKeyIdError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketInvalidAccessKeyIdError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["moto.s3.exceptions.BucketInvalidAccessKeyIdError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BucketInvalidAccessKeyIdError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.BucketInvalidAccessKeyIdError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.BucketInvalidAccessKeyIdError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.BucketInvalidAccessKeyIdError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BucketInvalidTokenError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.BucketError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.BucketInvalidTokenError", "name": "BucketInvalidTokenError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketInvalidTokenError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.BucketInvalidTokenError", "moto.s3.exceptions.BucketError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketInvalidTokenError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["moto.s3.exceptions.BucketInvalidTokenError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BucketInvalidTokenError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.BucketInvalidTokenError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.BucketInvalidTokenError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.BucketInvalidTokenError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BucketMustHaveLockeEnabled": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.BucketMustHaveLockeEnabled", "name": "BucketMustHaveLockeEnabled", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketMustHaveLockeEnabled", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.BucketMustHaveLockeEnabled", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketMustHaveLockeEnabled.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.BucketMustHaveLockeEnabled"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BucketMustHaveLockeEnabled", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.BucketMustHaveLockeEnabled.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.BucketMustHaveLockeEnabled.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.BucketMustHaveLockeEnabled", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BucketNeedsToBeNew": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.BucketNeedsToBeNew", "name": "BucketNeedsToBeNew", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketNeedsToBeNew", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.BucketNeedsToBeNew", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketNeedsToBeNew.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.BucketNeedsToBeNew"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BucketNeedsToBeNew", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.BucketNeedsToBeNew.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.BucketNeedsToBeNew.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.BucketNeedsToBeNew", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BucketSignatureDoesNotMatchError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.BucketSignatureDoesNotMatchError", "name": "BucketSignatureDoesNotMatchError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketSignatureDoesNotMatchError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.BucketSignatureDoesNotMatchError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.BucketSignatureDoesNotMatchError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["moto.s3.exceptions.BucketSignatureDoesNotMatchError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BucketSignatureDoesNotMatchError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.BucketSignatureDoesNotMatchError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.BucketSignatureDoesNotMatchError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.BucketSignatureDoesNotMatchError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CopyObjectMustChangeSomething": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.CopyObjectMustChangeSomething", "name": "CopyObjectMustChangeSomething", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.CopyObjectMustChangeSomething", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.CopyObjectMustChangeSomething", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.CopyObjectMustChangeSomething.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.CopyObjectMustChangeSomething"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CopyObjectMustChangeSomething", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.CopyObjectMustChangeSomething.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.CopyObjectMustChangeSomething.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.CopyObjectMustChangeSomething", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CrossLocationLoggingProhibitted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.CrossLocationLoggingProhibitted", "name": "CrossLocationLoggingProhibitted", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.CrossLocationLoggingProhibitted", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.CrossLocationLoggingProhibitted", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.CrossLocationLoggingProhibitted.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.CrossLocationLoggingProhibitted"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CrossLocationLoggingProhibitted", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.CrossLocationLoggingProhibitted.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.CrossLocationLoggingProhibitted.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.CrossLocationLoggingProhibitted", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DuplicateTagKeys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.DuplicateTagKeys", "name": "DuplicateTagKeys", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.DuplicateTagKeys", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.DuplicateTagKeys", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.DuplicateTagKeys.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.DuplicateTagKeys"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DuplicateTagKeys", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.DuplicateTagKeys.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.DuplicateTagKeys.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.DuplicateTagKeys", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ERROR_WITH_ARGUMENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.ERROR_WITH_ARGUMENT", "name": "ERROR_WITH_ARGUMENT", "type": "builtins.str"}}, "ERROR_WITH_BUCKET_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.ERROR_WITH_BUCKET_NAME", "name": "ERROR_WITH_BUCKET_NAME", "type": "builtins.str"}}, "ERROR_WITH_CONDITION_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.ERROR_WITH_CONDITION_NAME", "name": "ERROR_WITH_CONDITION_NAME", "type": "builtins.str"}}, "ERROR_WITH_KEY_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.ERROR_WITH_KEY_NAME", "name": "ERROR_WITH_KEY_NAME", "type": "builtins.str"}}, "ERROR_WITH_RANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.ERROR_WITH_RANGE", "name": "ERROR_WITH_RANGE", "type": "builtins.str"}}, "ERROR_WITH_STORAGE_CLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.ERROR_WITH_STORAGE_CLASS", "name": "ERROR_WITH_STORAGE_CLASS", "type": "builtins.str"}}, "ERROR_WITH_UPLOADID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.ERROR_WITH_UPLOADID", "name": "ERROR_WITH_UPLOADID", "type": "builtins.str"}}, "EntityTooSmall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.EntityTooSmall", "name": "EntityTooSmall", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.EntityTooSmall", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.EntityTooSmall", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.EntityTooSmall.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.EntityTooSmall"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EntityTooSmall", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.EntityTooSmall.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.EntityTooSmall.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.EntityTooSmall", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FakeDeleteMarker": {".class": "SymbolTableNode", "cross_ref": "moto.s3.models.FakeDeleteMarker", "kind": "Gdef"}, "HeadOnDeleteMarker": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.HeadOnDeleteMarker", "name": "HeadOnDeleteMarker", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.HeadOnDeleteMarker", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.HeadOnDeleteMarker", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "marker"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.HeadOnDeleteMarker.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "marker"], "arg_types": ["moto.s3.exceptions.HeadOnDeleteMarker", "moto.s3.models.FakeDeleteMarker"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HeadOnDeleteMarker", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "marker": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.s3.exceptions.HeadOnDeleteMarker.marker", "name": "marker", "type": "moto.s3.models.FakeDeleteMarker"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.HeadOnDeleteMarker.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.HeadOnDeleteMarker", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IllegalLocationConstraintException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.IllegalLocationConstraintException", "name": "IllegalLocationConstraintException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.IllegalLocationConstraintException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.IllegalLocationConstraintException", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.IllegalLocationConstraintException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.IllegalLocationConstraintException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IllegalLocationConstraintException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.IllegalLocationConstraintException.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.IllegalLocationConstraintException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.IllegalLocationConstraintException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidArgumentError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidArgumentError", "name": "InvalidArgumentError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidArgumentError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidArgumentError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 2, 4], "arg_names": ["self", "message", "name", "value", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidArgumentError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 2, 4], "arg_names": ["self", "message", "name", "value", "args", "kwargs"], "arg_types": ["moto.s3.exceptions.InvalidArgumentError", "builtins.str", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidArgumentError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidArgumentError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidArgumentError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidArgumentError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidBucketName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidBucketName", "name": "InvalidBucketName", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidBucketName", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidBucketName", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidBucketName.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.InvalidBucketName"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidBucketName", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidBucketName.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidBucketName.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidBucketName", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidContentMD5": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidContentMD5", "name": "InvalidContentMD5", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidContentMD5", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidContentMD5", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidContentMD5.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.InvalidContentMD5"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidContentMD5", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidContentMD5.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidContentMD5.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidContentMD5", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidContinuationToken": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidContinuationToken", "name": "InvalidContinuationToken", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidContinuationToken", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidContinuationToken", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidContinuationToken.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.InvalidContinuationToken"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidContinuationToken", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidContinuationToken.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidContinuationToken.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidContinuationToken", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidFilterRuleName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.InvalidArgumentError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidFilterRuleName", "name": "InvalidFilterRuleName", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidFilterRuleName", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidFilterRuleName", "moto.s3.exceptions.InvalidArgumentError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidFilterRuleName.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["moto.s3.exceptions.InvalidFilterRuleName", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidFilterRuleName", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidFilterRuleName.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidFilterRuleName.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidFilterRuleName", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidMaxPartArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidMaxPartArgument", "name": "InvalidMaxPartArgument", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidMaxPartArgument", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidMaxPartArgument", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "arg", "min_val", "max_val"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidMaxPartArgument.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "arg", "min_val", "max_val"], "arg_types": ["moto.s3.exceptions.InvalidMaxPartArgument", "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidMaxPartArgument", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidMaxPartArgument.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidMaxPartArgument.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidMaxPartArgument", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidMaxPartNumberArgument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.InvalidArgumentError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidMaxPartNumberArgument", "name": "InvalidMaxPartNumberArgument", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidMaxPartNumberArgument", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidMaxPartNumberArgument", "moto.s3.exceptions.InvalidArgumentError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidMaxPartNumberArgument.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["moto.s3.exceptions.InvalidMaxPartNumberArgument", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidMaxPartNumberArgument", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidMaxPartNumberArgument.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidMaxPartNumberArgument.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidMaxPartNumberArgument", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidNotificationARN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidNotificationARN", "name": "InvalidNotificationARN", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidNotificationARN", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidNotificationARN", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidNotificationARN.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.InvalidNotificationARN"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidNotificationARN", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidNotificationARN.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidNotificationARN.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidNotificationARN", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidNotificationDestination": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidNotificationDestination", "name": "InvalidNotificationDestination", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidNotificationDestination", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidNotificationDestination", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidNotificationDestination.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.InvalidNotificationDestination"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidNotificationDestination", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidNotificationDestination.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidNotificationDestination.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidNotificationDestination", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidNotificationEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidNotificationEvent", "name": "InvalidNotificationEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidNotificationEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidNotificationEvent", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidNotificationEvent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.InvalidNotificationEvent"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidNotificationEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidNotificationEvent.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidNotificationEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidNotificationEvent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidObjectState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.BucketError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidObjectState", "name": "InvalidObjectState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidObjectState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidObjectState", "moto.s3.exceptions.BucketError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "storage_class", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidObjectState.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "storage_class", "kwargs"], "arg_types": ["moto.s3.exceptions.InvalidObjectState", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidObjectState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidObjectState.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidObjectState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidObjectState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidPart": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidPart", "name": "InvalidPart", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidPart", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidPart", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidPart.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.InvalidPart"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidPart", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidPart.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidPart.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidPart", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidPartOrder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidPartOrder", "name": "InvalidPartOrder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidPartOrder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidPartOrder", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidPartOrder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.InvalidPartOrder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidPartOrder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidPartOrder.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidPartOrder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidPartOrder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidPublicAccessBlockConfiguration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidPublicAccessBlockConfiguration", "name": "InvalidPublicAccessBlockConfiguration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidPublicAccessBlockConfiguration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidPublicAccessBlockConfiguration", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidPublicAccessBlockConfiguration.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.InvalidPublicAccessBlockConfiguration"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidPublicAccessBlockConfiguration", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidPublicAccessBlockConfiguration.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidPublicAccessBlockConfiguration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidPublicAccessBlockConfiguration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidRange": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidRange", "name": "InvalidRange", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidRange", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidRange", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "range_requested", "actual_size", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidRange.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "range_requested", "actual_size", "kwargs"], "arg_types": ["moto.s3.exceptions.InvalidRange", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidRange", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidRange.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidRange.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidRange", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidRequest", "name": "InvalidRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidRequest", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "method"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidRequest.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "method"], "arg_types": ["moto.s3.exceptions.InvalidRequest", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidRequest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidRequest.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidStorageClass": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidStorageClass", "name": "InvalidStorageClass", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidStorageClass", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidStorageClass", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "storage"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidStorageClass.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "storage"], "arg_types": ["moto.s3.exceptions.InvalidStorageClass", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidStorageClass", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidStorageClass.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidStorageClass.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidStorageClass", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidTagError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidTagError", "name": "InvalidTagError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidTagError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidTagError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidTagError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["moto.s3.exceptions.InvalidTagError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidTagError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidTagError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidTagError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidTagError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidTargetBucketForLogging": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidTargetBucketForLogging", "name": "InvalidTargetBucketForLogging", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidTargetBucketForLogging", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidTargetBucketForLogging", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidTargetBucketForLogging.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "msg"], "arg_types": ["moto.s3.exceptions.InvalidTargetBucketForLogging", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidTargetBucketForLogging", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidTargetBucketForLogging.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidTargetBucketForLogging.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidTargetBucketForLogging", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.InvalidVersion", "name": "InvalidVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidVersion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.InvalidVersion", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "version_id", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.InvalidVersion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "version_id", "args", "kwargs"], "arg_types": ["moto.s3.exceptions.InvalidVersion", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InvalidVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.InvalidVersion.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.InvalidVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.InvalidVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LockNotEnabled": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.LockNotEnabled", "name": "LockNotEnabled", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.LockNotEnabled", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.LockNotEnabled", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.LockNotEnabled.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.LockNotEnabled"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LockNotEnabled", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.LockNotEnabled.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.LockNotEnabled.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.LockNotEnabled", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MalformedACLError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.MalformedACLError", "name": "MalformedACLError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.MalformedACLError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.MalformedACLError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.MalformedACLError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.MalformedACLError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MalformedACLError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.MalformedACLError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.MalformedACLError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.MalformedACLError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MalformedXML": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.MalformedXML", "name": "MalformedXML", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.MalformedXML", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.MalformedXML", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.MalformedXML.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.MalformedXML"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MalformedXML", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.MalformedXML.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.MalformedXML.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.MalformedXML", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MissingBucket": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.BucketError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.MissingBucket", "name": "MissingBucket", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.MissingBucket", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.MissingBucket", "moto.s3.exceptions.BucketError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.MissingBucket.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "bucket"], "arg_types": ["moto.s3.exceptions.MissingBucket", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MissingBucket", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.MissingBucket.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.MissingBucket.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.MissingBucket", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MissingKey": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.<PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.<PERSON><PERSON><PERSON>", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.MissingKey.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": ["moto.s3.exceptions.<PERSON><PERSON><PERSON>", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MissingKey", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.MissingKey.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.MissingKey.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.<PERSON><PERSON><PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MissingVersion": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.MissingVersion", "name": "MissingVersion", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.MissingVersion", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.MissingVersion", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.MissingVersion.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.MissingVersion"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MissingVersion", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.MissingVersion.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.MissingVersion.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.MissingVersion", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSuchPublicAccessBlockConfiguration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.NoSuchPublicAccessBlockConfiguration", "name": "NoSuchPublicAccessBlockConfiguration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.NoSuchPublicAccessBlockConfiguration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.NoSuchPublicAccessBlockConfiguration", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.NoSuchPublicAccessBlockConfiguration.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.NoSuchPublicAccessBlockConfiguration"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoSuchPublicAccessBlockConfiguration", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.NoSuchPublicAccessBlockConfiguration.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.NoSuchPublicAccessBlockConfiguration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.NoSuchPublicAccessBlockConfiguration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSuchUpload": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.NoSuchUpload", "name": "NoSuchUpload", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.NoSuchUpload", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.NoSuchUpload", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "upload_id", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.NoSuchUpload.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "upload_id", "args", "kwargs"], "arg_types": ["moto.s3.exceptions.NoSuchUpload", {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoSuchUpload", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.NoSuchUpload.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.NoSuchUpload.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.NoSuchUpload", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSystemTags": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.NoSystemTags", "name": "NoSystemTags", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.NoSystemTags", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.NoSystemTags", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.NoSystemTags.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.NoSystemTags"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoSystemTags", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.NoSystemTags.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.NoSystemTags.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.NoSystemTags", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotAnIntegerException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.InvalidArgumentError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.NotAnIntegerException", "name": "NotAnIntegerException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.NotAnIntegerException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.NotAnIntegerException", "moto.s3.exceptions.InvalidArgumentError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.NotAnIntegerException.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "name", "value"], "arg_types": ["moto.s3.exceptions.NotAnIntegerException", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NotAnIntegerException", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.NotAnIntegerException.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.NotAnIntegerException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.NotAnIntegerException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ObjectLockConfigurationNotFoundError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.ObjectLockConfigurationNotFoundError", "name": "ObjectLockConfigurationNotFoundError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.ObjectLockConfigurationNotFoundError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.ObjectLockConfigurationNotFoundError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.ObjectLockConfigurationNotFoundError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.ObjectLockConfigurationNotFoundError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ObjectLockConfigurationNotFoundError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.ObjectLockConfigurationNotFoundError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.ObjectLockConfigurationNotFoundError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.ObjectLockConfigurationNotFoundError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ObjectNotInActiveTierError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.ObjectNotInActiveTierError", "name": "ObjectNotInActiveTierError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.ObjectNotInActiveTierError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.ObjectNotInActiveTierError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.ObjectNotInActiveTierError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key_name"], "arg_types": ["moto.s3.exceptions.ObjectNotInActiveTierError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ObjectNotInActiveTierError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.ObjectNotInActiveTierError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.ObjectNotInActiveTierError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.ObjectNotInActiveTierError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PreconditionFailed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.PreconditionFailed", "name": "PreconditionFailed", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.PreconditionFailed", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.PreconditionFailed", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "failed_condition", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.PreconditionFailed.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "failed_condition", "kwargs"], "arg_types": ["moto.s3.exceptions.PreconditionFailed", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PreconditionFailed", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.PreconditionFailed.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.PreconditionFailed.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.PreconditionFailed", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RESTError": {".class": "SymbolTableNode", "cross_ref": "moto.core.exceptions.RESTError", "kind": "Gdef"}, "S3AccessDeniedError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.S3AccessDeniedError", "name": "S3AccessDeniedError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.S3AccessDeniedError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.S3AccessDeniedError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.S3AccessDeniedError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.S3AccessDeniedError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3AccessDeniedError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.S3AccessDeniedError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.S3AccessDeniedError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.S3AccessDeniedError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3AclAndGrantError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.S3AclAndGrantError", "name": "S3AclAndGrantError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.S3AclAndGrantError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.S3AclAndGrantError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.S3AclAndGrantError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.S3AclAndGrantError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3AclAndGrantError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.S3AclAndGrantError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.S3AclAndGrantError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.S3AclAndGrantError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3ClientError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.exceptions.RESTError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.S3ClientError", "name": "S3ClientError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.S3ClientError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.S3ClientError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "arg_types": ["moto.s3.exceptions.S3ClientError", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3ClientError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "env": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.S3ClientError.env", "name": "env", "type": "jinja2.environment.Environment"}}, "extended_templates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.S3ClientError.extended_templates", "name": "extended_templates", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "request_id_tag_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.S3ClientError.request_id_tag_name", "name": "request_id_tag_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.S3ClientError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.S3ClientError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3InvalidAccessKeyIdError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.S3InvalidAccessKeyIdError", "name": "S3InvalidAccessKeyIdError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.S3InvalidAccessKeyIdError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.S3InvalidAccessKeyIdError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.S3InvalidAccessKeyIdError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.S3InvalidAccessKeyIdError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3InvalidAccessKeyIdError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.S3InvalidAccessKeyIdError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.S3InvalidAccessKeyIdError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.S3InvalidAccessKeyIdError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3InvalidTokenError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.S3InvalidTokenError", "name": "S3InvalidTokenError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.S3InvalidTokenError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.S3InvalidTokenError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.S3InvalidTokenError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.S3InvalidTokenError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3InvalidTokenError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.S3InvalidTokenError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.S3InvalidTokenError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.S3InvalidTokenError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "S3SignatureDoesNotMatchError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.S3SignatureDoesNotMatchError", "name": "S3SignatureDoesNotMatchError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.S3SignatureDoesNotMatchError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.S3SignatureDoesNotMatchError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.S3SignatureDoesNotMatchError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.S3SignatureDoesNotMatchError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of S3SignatureDoesNotMatchError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.S3SignatureDoesNotMatchError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.S3SignatureDoesNotMatchError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.S3SignatureDoesNotMatchError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WrongPublicAccessBlockAccountIdError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.s3.exceptions.S3ClientError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.s3.exceptions.WrongPublicAccessBlockAccountIdError", "name": "WrongPublicAccessBlockAccountIdError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.WrongPublicAccessBlockAccountIdError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.s3.exceptions", "mro": ["moto.s3.exceptions.WrongPublicAccessBlockAccountIdError", "moto.s3.exceptions.S3ClientError", "moto.core.exceptions.RESTError", "werkzeug.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.s3.exceptions.WrongPublicAccessBlockAccountIdError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.s3.exceptions.WrongPublicAccessBlockAccountIdError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WrongPublicAccessBlockAccountIdError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "moto.s3.exceptions.WrongPublicAccessBlockAccountIdError.code", "name": "code", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.s3.exceptions.WrongPublicAccessBlockAccountIdError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.s3.exceptions.WrongPublicAccessBlockAccountIdError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3.exceptions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3.exceptions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3.exceptions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3.exceptions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3.exceptions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.s3.exceptions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/s3/exceptions.py"}