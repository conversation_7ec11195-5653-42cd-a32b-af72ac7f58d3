{".class": "MypyFile", "_fullname": "moto.sagemaker.utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FakePipeline": {".class": "SymbolTableNode", "cross_ref": "moto.sagemaker.models.FakePipeline", "kind": "Gdef"}, "FakePipelineExecution": {".class": "SymbolTableNode", "cross_ref": "moto.sagemaker.models.FakePipelineExecution", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "moto.sagemaker.exceptions.ValidationError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.sagemaker.utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.sagemaker.utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.sagemaker.utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.sagemaker.utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.sagemaker.utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.sagemaker.utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "arn_formatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["_type", "_id", "account_id", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sagemaker.utils.arn_formatter", "name": "arn_formatter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["_type", "_id", "account_id", "region_name"], "arg_types": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arn_formatter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_pipeline_execution_from_arn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["pipelines", "pipeline_execution_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sagemaker.utils.get_pipeline_execution_from_arn", "name": "get_pipeline_execution_from_arn", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["pipelines", "pipeline_execution_arn"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "moto.sagemaker.models.FakePipeline"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_pipeline_execution_from_arn", "ret_type": "moto.sagemaker.models.FakePipelineExecution", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_pipeline_from_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["pipelines", "pipeline_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sagemaker.utils.get_pipeline_from_name", "name": "get_pipeline_from_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["pipelines", "pipeline_name"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "moto.sagemaker.models.FakePipeline"], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_pipeline_from_name", "ret_type": "moto.sagemaker.models.FakePipeline", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_pipeline_name_from_execution_arn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pipeline_execution_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sagemaker.utils.get_pipeline_name_from_execution_arn", "name": "get_pipeline_name_from_execution_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pipeline_execution_arn"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_pipeline_name_from_execution_arn", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "load_pipeline_definition_from_s3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["pipeline_definition_s3_location", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sagemaker.utils.load_pipeline_definition_from_s3", "name": "load_pipeline_definition_from_s3", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["pipeline_definition_s3_location", "account_id"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_pipeline_definition_from_s3", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "s3_backends": {".class": "SymbolTableNode", "cross_ref": "moto.s3.models.s3_backends", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "validate_model_approval_status": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["model_approval_status"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.sagemaker.utils.validate_model_approval_status", "name": "validate_model_approval_status", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["model_approval_status"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_model_approval_status", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/sagemaker/utils.py"}