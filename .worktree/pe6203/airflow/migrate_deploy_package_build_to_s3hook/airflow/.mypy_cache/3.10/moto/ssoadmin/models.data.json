{".class": "MypyFile", "_fullname": "moto.ssoadmin.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AccountAssignment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.ssoadmin.models.AccountAssignment", "name": "AccountAssignment", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.AccountAssignment", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.ssoadmin.models", "mro": ["moto.ssoadmin.models.AccountAssignment", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "instance_arn", "target_id", "target_type", "permission_set_arn", "principal_type", "principal_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.AccountAssignment.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "instance_arn", "target_id", "target_type", "permission_set_arn", "principal_type", "principal_id"], "arg_types": ["moto.ssoadmin.models.AccountAssignment", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AccountAssignment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "created_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.AccountAssignment.created_date", "name": "created_date", "type": "builtins.float"}}, "instance_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.AccountAssignment.instance_arn", "name": "instance_arn", "type": "builtins.str"}}, "permission_set_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.AccountAssignment.permission_set_arn", "name": "permission_set_arn", "type": "builtins.str"}}, "principal_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.AccountAssignment.principal_id", "name": "principal_id", "type": "builtins.str"}}, "principal_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.AccountAssignment.principal_type", "name": "principal_type", "type": "builtins.str"}}, "request_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.AccountAssignment.request_id", "name": "request_id", "type": "builtins.str"}}, "target_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.AccountAssignment.target_id", "name": "target_id", "type": "builtins.str"}}, "target_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.AccountAssignment.target_type", "name": "target_type", "type": "builtins.str"}}, "to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "include_creation_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.AccountAssignment.to_json", "name": "to_json", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "include_creation_date"], "arg_types": ["moto.ssoadmin.models.AccountAssignment", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_json of AccountAssignment", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.ssoadmin.models.AccountAssignment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.ssoadmin.models.AccountAssignment", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "ConflictException": {".class": "SymbolTableNode", "cross_ref": "moto.ssoadmin.exceptions.ConflictException", "kind": "Gdef"}, "CustomerManagedPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.ssoadmin.models.CustomerManagedPolicy", "name": "CustomerManagedPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.CustomerManagedPolicy", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.ssoadmin.models", "mro": ["moto.ssoadmin.models.CustomerManagedPolicy", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.CustomerManagedPolicy.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["moto.ssoadmin.models.CustomerManagedPolicy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of CustomerManagedPolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.CustomerManagedPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "path"], "arg_types": ["moto.ssoadmin.models.CustomerManagedPolicy", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CustomerManagedPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.CustomerManagedPolicy.name", "name": "name", "type": "builtins.str"}}, "path": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.CustomerManagedPolicy.path", "name": "path", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.ssoadmin.models.CustomerManagedPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.ssoadmin.models.CustomerManagedPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MAX_MANAGED_POLICIES_PER_PERMISSION_SET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "moto.ssoadmin.models.MAX_MANAGED_POLICIES_PER_PERMISSION_SET", "name": "MAX_MANAGED_POLICIES_PER_PERMISSION_SET", "type": "builtins.int"}}, "ManagedPolicy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.ssoadmin.models.ManagedPolicy", "name": "ManagedPolicy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.ManagedPolicy", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.ssoadmin.models", "mro": ["moto.ssoadmin.models.ManagedPolicy", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.ManagedPolicy.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["moto.ssoadmin.models.ManagedPolicy", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of ManagedPolicy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "arn", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.ManagedPolicy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "arn", "name"], "arg_types": ["moto.ssoadmin.models.ManagedPolicy", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ManagedPolicy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.ManagedPolicy.arn", "name": "arn", "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.ManagedPolicy.name", "name": "name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.ssoadmin.models.ManagedPolicy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.ssoadmin.models.ManagedPolicy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PAGINATION_MODEL": {".class": "SymbolTableNode", "cross_ref": "moto.ssoadmin.utils.PAGINATION_MODEL", "kind": "Gdef"}, "PermissionSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.ssoadmin.models.PermissionSet", "name": "PermissionSet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.PermissionSet", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.ssoadmin.models", "mro": ["moto.ssoadmin.models.PermissionSet", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "description", "instance_arn", "session_duration", "relay_state", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.PermissionSet.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "description", "instance_arn", "session_duration", "relay_state", "tags"], "arg_types": ["moto.ssoadmin.models.PermissionSet", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PermissionSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "created_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.PermissionSet.created_date", "name": "created_date", "type": "builtins.float"}}, "customer_managed_policies": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.ssoadmin.models.PermissionSet.customer_managed_policies", "name": "customer_managed_policies", "type": {".class": "Instance", "args": ["moto.ssoadmin.models.CustomerManagedPolicy"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.PermissionSet.description", "name": "description", "type": "builtins.str"}}, "generate_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["instance_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.ssoadmin.models.PermissionSet.generate_id", "name": "generate_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance_arn"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_id of PermissionSet", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.ssoadmin.models.PermissionSet.generate_id", "name": "generate_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance_arn"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_id of PermissionSet", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "inline_policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.PermissionSet.inline_policy", "name": "inline_policy", "type": "builtins.str"}}, "instance_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.PermissionSet.instance_arn", "name": "instance_arn", "type": "builtins.str"}}, "managed_policies": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.ssoadmin.models.PermissionSet.managed_policies", "name": "managed_policies", "type": {".class": "Instance", "args": ["moto.ssoadmin.models.ManagedPolicy"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.PermissionSet.name", "name": "name", "type": "builtins.str"}}, "permission_set_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.PermissionSet.permission_set_arn", "name": "permission_set_arn", "type": "builtins.str"}}, "relay_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.PermissionSet.relay_state", "name": "relay_state", "type": "builtins.str"}}, "session_duration": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.PermissionSet.session_duration", "name": "session_duration", "type": "builtins.str"}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.PermissionSet.tags", "name": "tags", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "include_creation_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.PermissionSet.to_json", "name": "to_json", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "include_creation_date"], "arg_types": ["moto.ssoadmin.models.PermissionSet", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_json of PermissionSet", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "total_managed_policies_attached": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.ssoadmin.models.PermissionSet.total_managed_policies_attached", "name": "total_managed_policies_attached", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.ssoadmin.models.PermissionSet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.ssoadmin.models.PermissionSet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceNotFoundException": {".class": "SymbolTableNode", "cross_ref": "moto.ssoadmin.exceptions.ResourceNotFoundException", "kind": "Gdef"}, "SSOAdminBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.ssoadmin.models.SSOAdminBackend", "name": "SSOAdminBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.ssoadmin.models", "mro": ["moto.ssoadmin.models.SSOAdminBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SSOAdminBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_detach_customer_managed_policy_from_permissionset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "customer_managed_policy_reference"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend._detach_customer_managed_policy_from_permissionset", "name": "_detach_customer_managed_policy_from_permissionset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "customer_managed_policy_reference"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_detach_customer_managed_policy_from_permissionset of SSOAdminBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_detach_managed_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "managed_policy_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend._detach_managed_policy", "name": "_detach_managed_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "managed_policy_arn"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_detach_managed_policy of SSOAdminBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_account": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "instance_arn", "target_id", "target_type", "permission_set_arn", "principal_type", "principal_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend._find_account", "name": "_find_account", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "instance_arn", "target_id", "target_type", "permission_set_arn", "principal_type", "principal_id"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_account of SSOAdminBackend", "ret_type": "moto.ssoadmin.models.AccountAssignment", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_managed_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "managed_policy_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend._find_managed_policy", "name": "_find_managed_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "managed_policy_arn"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_managed_policy of SSOAdminBackend", "ret_type": "moto.ssoadmin.models.ManagedPolicy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend._find_permission_set", "name": "_find_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_permission_set of SSOAdminBackend", "ret_type": "moto.ssoadmin.models.PermissionSet", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "account_assignments": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.ssoadmin.models.SSOAdminBackend.account_assignments", "name": "account_assignments", "type": {".class": "Instance", "args": ["moto.ssoadmin.models.AccountAssignment"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "attach_customer_managed_policy_reference_to_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "customer_managed_policy_reference"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.attach_customer_managed_policy_reference_to_permission_set", "name": "attach_customer_managed_policy_reference_to_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "customer_managed_policy_reference"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attach_customer_managed_policy_reference_to_permission_set of SSOAdminBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attach_managed_policy_to_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "managed_policy_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.attach_managed_policy_to_permission_set", "name": "attach_managed_policy_to_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "managed_policy_arn"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attach_managed_policy_to_permission_set of SSOAdminBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aws_managed_policies": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.ssoadmin.models.SSOAdminBackend.aws_managed_policies", "name": "aws_managed_policies", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "create_account_assignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "instance_arn", "target_id", "target_type", "permission_set_arn", "principal_type", "principal_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.create_account_assignment", "name": "create_account_assignment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "instance_arn", "target_id", "target_type", "permission_set_arn", "principal_type", "principal_id"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_account_assignment of SSOAdminBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "description", "instance_arn", "session_duration", "relay_state", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.create_permission_set", "name": "create_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "name", "description", "instance_arn", "session_duration", "relay_state", "tags"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_permission_set of SSOAdminBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_account_assignment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "instance_arn", "target_id", "target_type", "permission_set_arn", "principal_type", "principal_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.delete_account_assignment", "name": "delete_account_assignment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "instance_arn", "target_id", "target_type", "permission_set_arn", "principal_type", "principal_id"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_account_assignment of SSOAdminBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_inline_policy_from_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.delete_inline_policy_from_permission_set", "name": "delete_inline_policy_from_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_inline_policy_from_permission_set of SSOAdminBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.delete_permission_set", "name": "delete_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_permission_set of SSOAdminBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.describe_permission_set", "name": "describe_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_permission_set of SSOAdminBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detach_customer_managed_policy_reference_from_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "customer_managed_policy_reference"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.detach_customer_managed_policy_reference_from_permission_set", "name": "detach_customer_managed_policy_reference_from_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "customer_managed_policy_reference"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detach_customer_managed_policy_reference_from_permission_set of SSOAdminBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detach_managed_policy_from_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "managed_policy_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.detach_managed_policy_from_permission_set", "name": "detach_managed_policy_from_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "managed_policy_arn"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detach_managed_policy_from_permission_set of SSOAdminBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_inline_policy_for_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.get_inline_policy_for_permission_set", "name": "get_inline_policy_for_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_inline_policy_for_permission_set of SSOAdminBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_account_assignments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "account_id", "permission_set_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.ssoadmin.models.SSOAdminBackend.list_account_assignments", "name": "list_account_assignments", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "account_id", "permission_set_arn"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_account_assignments of SSOAdminBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.ssoadmin.models.SSOAdminBackend.list_account_assignments", "name": "list_account_assignments", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "list_account_assignments_for_principal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "filter_", "instance_arn", "principal_id", "principal_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.ssoadmin.models.SSOAdminBackend.list_account_assignments_for_principal", "name": "list_account_assignments_for_principal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "filter_", "instance_arn", "principal_id", "principal_type"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_account_assignments_for_principal of SSOAdminBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.ssoadmin.models.SSOAdminBackend.list_account_assignments_for_principal", "name": "list_account_assignments_for_principal", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "list_customer_managed_policy_references_in_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.ssoadmin.models.SSOAdminBackend.list_customer_managed_policy_references_in_permission_set", "name": "list_customer_managed_policy_references_in_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_customer_managed_policy_references_in_permission_set of SSOAdminBackend", "ret_type": {".class": "Instance", "args": ["moto.ssoadmin.models.CustomerManagedPolicy"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.ssoadmin.models.SSOAdminBackend.list_customer_managed_policy_references_in_permission_set", "name": "list_customer_managed_policy_references_in_permission_set", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "list_managed_policies_in_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.ssoadmin.models.SSOAdminBackend.list_managed_policies_in_permission_set", "name": "list_managed_policies_in_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_managed_policies_in_permission_set of SSOAdminBackend", "ret_type": {".class": "Instance", "args": ["moto.ssoadmin.models.ManagedPolicy"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.ssoadmin.models.SSOAdminBackend.list_managed_policies_in_permission_set", "name": "list_managed_policies_in_permission_set", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "list_permission_sets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instance_arn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "moto.ssoadmin.models.SSOAdminBackend.list_permission_sets", "name": "list_permission_sets", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instance_arn"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_permission_sets of SSOAdminBackend", "ret_type": {".class": "Instance", "args": ["moto.ssoadmin.models.PermissionSet"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "moto.ssoadmin.models.SSOAdminBackend.list_permission_sets", "name": "list_permission_sets", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "permission_sets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.ssoadmin.models.SSOAdminBackend.permission_sets", "name": "permission_sets", "type": {".class": "Instance", "args": ["moto.ssoadmin.models.PermissionSet"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "put_inline_policy_to_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "inline_policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.put_inline_policy_to_permission_set", "name": "put_inline_policy_to_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "inline_policy"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put_inline_policy_to_permission_set of SSOAdminBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_permission_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "description", "session_duration", "relay_state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.ssoadmin.models.SSOAdminBackend.update_permission_set", "name": "update_permission_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "instance_arn", "permission_set_arn", "description", "session_duration", "relay_state"], "arg_types": ["moto.ssoadmin.models.SSOAdminBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_permission_set of SSOAdminBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.ssoadmin.models.SSOAdminBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.ssoadmin.models.SSOAdminBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ServiceQuotaExceededException": {".class": "SymbolTableNode", "cross_ref": "moto.ssoadmin.exceptions.ServiceQuotaExceededException", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.ssoadmin.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.ssoadmin.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.ssoadmin.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.ssoadmin.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.ssoadmin.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.ssoadmin.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "aws_managed_policies_data": {".class": "SymbolTableNode", "cross_ref": "moto.iam.aws_managed_policies.aws_managed_policies_data", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "paginate": {".class": "SymbolTableNode", "cross_ref": "moto.utilities.paginator.paginate", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}, "ssoadmin_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.ssoadmin.models.ssoadmin_backends", "name": "ssoadmin_backends", "type": {".class": "Instance", "args": ["moto.ssoadmin.models.SSOAdminBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "unix_time": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.unix_time", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/ssoadmin/models.py"}