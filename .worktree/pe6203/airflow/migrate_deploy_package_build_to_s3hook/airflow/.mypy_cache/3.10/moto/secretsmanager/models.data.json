{".class": "MypyFile", "_fullname": "moto.secretsmanager.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BackendDict": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BackendDict", "kind": "Gdef"}, "BaseBackend": {".class": "SymbolTableNode", "cross_ref": "moto.core.base_backend.BaseBackend", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "moto.core.common_models.BaseModel", "kind": "Gdef"}, "ClientError": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.exceptions.ClientError", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FakeSecret": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.secretsmanager.models.FakeSecret", "name": "FakeSecret", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.FakeSecret", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.secretsmanager.models", "mro": ["moto.secretsmanager.models.FakeSecret", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "account_id", "region_name", "secret_id", "secret_version", "version_id", "secret_string", "secret_binary", "description", "tags", "kms_key_id", "version_stages", "last_changed_date", "created_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.FakeSecret.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "account_id", "region_name", "secret_id", "secret_version", "version_id", "secret_string", "secret_binary", "description", "tags", "kms_key_id", "version_stages", "last_changed_date", "created_date"], "arg_types": ["moto.secretsmanager.models.FakeSecret", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FakeSecret", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_form_version_ids_to_stages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.FakeSecret._form_version_ids_to_stages", "name": "_form_version_ids_to_stages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.secretsmanager.models.FakeSecret"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_form_version_ids_to_stages of FakeSecret", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.arn", "name": "arn", "type": "builtins.str"}}, "auto_rotate_after_days": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.auto_rotate_after_days", "name": "auto_rotate_after_days", "type": "builtins.int"}}, "created_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.created_date", "name": "created_date", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "default_version_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.default_version_id", "name": "default_version_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "deleted_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.FakeSecret.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "deleted_date"], "arg_types": ["moto.secretsmanager.models.FakeSecret", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete of FakeSecret", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deleted_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.deleted_date", "name": "deleted_date", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.description", "name": "description", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "is_deleted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.FakeSecret.is_deleted", "name": "is_deleted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.secretsmanager.models.FakeSecret"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_deleted of FakeSecret", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kms_key_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.kms_key_id", "name": "kms_key_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "last_changed_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.last_changed_date", "name": "last_changed_date", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "last_rotation_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.last_rotation_date", "name": "last_rotation_date", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.name", "name": "name", "type": "builtins.str"}}, "next_rotation_date": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.next_rotation_date", "name": "next_rotation_date", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "policy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.policy", "name": "policy", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "remove_version_stages_from_old_versions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "version_stages"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.FakeSecret.remove_version_stages_from_old_versions", "name": "remove_version_stages_from_old_versions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "version_stages"], "arg_types": ["moto.secretsmanager.models.FakeSecret", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_version_stages_from_old_versions of FakeSecret", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset_default_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "secret_version", "version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.FakeSecret.reset_default_version", "name": "reset_default_version", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "secret_version", "version_id"], "arg_types": ["moto.secretsmanager.models.FakeSecret", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset_default_version of FakeSecret", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "restore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.FakeSecret.restore", "name": "restore", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.secretsmanager.models.FakeSecret"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore of FakeSecret", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rotation_enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.rotation_enabled", "name": "rotation_enabled", "type": "builtins.bool"}}, "rotation_lambda_arn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.rotation_lambda_arn", "name": "rotation_lambda_arn", "type": "builtins.str"}}, "rotation_requested": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.rotation_requested", "name": "rotation_requested", "type": "builtins.bool"}}, "secret_binary": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.secret_binary", "name": "secret_binary", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "secret_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.secret_id", "name": "secret_id", "type": "builtins.str"}}, "secret_string": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.secret_string", "name": "secret_string", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "set_default_version_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.FakeSecret.set_default_version_id", "name": "set_default_version_id", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "version_id"], "arg_types": ["moto.secretsmanager.models.FakeSecret", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_default_version_id of FakeSecret", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.tags", "name": "tags", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.FakeSecret.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["moto.secretsmanager.models.FakeSecret"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_dict of FakeSecret", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_short_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "include_version_stages", "version_id", "include_version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.FakeSecret.to_short_dict", "name": "to_short_dict", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "include_version_stages", "version_id", "include_version_id"], "arg_types": ["moto.secretsmanager.models.FakeSecret", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_short_dict of FakeSecret", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "description", "tags", "kms_key_id", "last_changed_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.FakeSecret.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "description", "tags", "kms_key_id", "last_changed_date"], "arg_types": ["moto.secretsmanager.models.FakeSecret", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update of FakeSecret", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "version_stages": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.version_stages", "name": "version_stages", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "versions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "moto.secretsmanager.models.FakeSecret.versions", "name": "versions", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.secretsmanager.models.FakeSecret.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.secretsmanager.models.FakeSecret", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidParameterException": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.exceptions.InvalidParameterException", "kind": "Gdef"}, "InvalidRequestException": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.exceptions.InvalidRequestException", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ResourceExistsException": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.exceptions.ResourceExistsException", "kind": "Gdef"}, "ResourceNotFoundException": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.exceptions.ResourceNotFoundException", "kind": "Gdef"}, "SecretHasNoValueException": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.exceptions.SecretHasNoValueException", "kind": "Gdef"}, "SecretNotFoundException": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.exceptions.SecretNotFoundException", "kind": "Gdef"}, "SecretStageVersionMismatchException": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.exceptions.SecretStageVersionMismatchException", "kind": "Gdef"}, "SecretsManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.common_models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.secretsmanager.models.SecretsManager", "name": "SecretsManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManager", "has_param_spec_type": false, "metaclass_type": "moto.core.base_backend.InstanceTrackerMeta", "metadata": {}, "module_name": "moto.secretsmanager.models", "mro": ["moto.secretsmanager.models.SecretsManager", "moto.core.common_models.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "region_name"], "arg_types": ["moto.secretsmanager.models.SecretsManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SecretsManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "region": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.SecretsManager.region", "name": "region", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.secretsmanager.models.SecretsManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.secretsmanager.models.SecretsManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SecretsManagerBackend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["moto.core.base_backend.BaseBackend"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.secretsmanager.models.SecretsManagerBackend", "name": "SecretsManagerBackend", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "moto.secretsmanager.models", "mro": ["moto.secretsmanager.models.SecretsManagerBackend", "moto.core.base_backend.BaseBackend", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "region_name", "account_id"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SecretsManagerBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "secret_id", "secret_string", "secret_binary", "description", "tags", "kms_key_id", "version_id", "version_stages"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend._add_secret", "name": "_add_secret", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "secret_id", "secret_string", "secret_binary", "description", "tags", "kms_key_id", "version_id", "version_stages"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_secret of SecretsManagerBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": ["moto.secretsmanager.models.FakeSecret", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_client_request_token_validator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "client_request_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend._client_request_token_validator", "name": "_client_request_token_validator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "client_request_token"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_client_request_token_validator of SecretsManagerBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_from_client_request_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "client_request_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend._from_client_request_token", "name": "_from_client_request_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "client_request_token"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_from_client_request_token of SecretsManagerBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_valid_identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend._is_valid_identifier", "name": "_is_valid_identifier", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "identifier"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_valid_identifier of SecretsManagerBackend", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unix_time_secs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend._unix_time_secs", "name": "_unix_time_secs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dt"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unix_time_secs of SecretsManagerBackend", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cancel_rotate_secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "secret_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.cancel_rotate_secret", "name": "cancel_rotate_secret", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "secret_id"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cancel_rotate_secret of SecretsManagerBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "secret_string", "secret_binary", "description", "tags", "kms_key_id", "client_request_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.create_secret", "name": "create_secret", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "secret_string", "secret_binary", "description", "tags", "kms_key_id", "client_request_token"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_secret of SecretsManagerBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_vpc_endpoint_service": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["service_region", "zones"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.default_vpc_endpoint_service", "name": "default_vpc_endpoint_service", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["service_region", "zones"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_vpc_endpoint_service of SecretsManagerBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.default_vpc_endpoint_service", "name": "default_vpc_endpoint_service", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["service_region", "zones"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "default_vpc_endpoint_service of SecretsManagerBackend", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "delete_resource_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "secret_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.delete_resource_policy", "name": "delete_resource_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "secret_id"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_resource_policy of SecretsManagerBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "secret_id", "recovery_window_in_days", "force_delete_without_recovery"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.delete_secret", "name": "delete_secret", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "secret_id", "recovery_window_in_days", "force_delete_without_recovery"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_secret of SecretsManagerBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "describe_secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "secret_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.describe_secret", "name": "describe_secret", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "secret_id"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "describe_secret of SecretsManagerBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_random_password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "password_length", "exclude_characters", "exclude_numbers", "exclude_punctuation", "exclude_uppercase", "exclude_lowercase", "include_space", "require_each_included_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.get_random_password", "name": "get_random_password", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "password_length", "exclude_characters", "exclude_numbers", "exclude_punctuation", "exclude_uppercase", "exclude_lowercase", "include_space", "require_each_included_type"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.int", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_random_password of SecretsManagerBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resource_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "secret_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.get_resource_policy", "name": "get_resource_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "secret_id"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_policy of SecretsManagerBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_secret_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "secret_id", "version_id", "version_stage"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.get_secret_value", "name": "get_secret_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "secret_id", "version_id", "version_stage"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_secret_value of SecretsManagerBackend", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_secret_version_ids": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "secret_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.list_secret_version_ids", "name": "list_secret_version_ids", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "secret_id"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_secret_version_ids of SecretsManagerBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_secrets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "filters", "max_results", "next_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.list_secrets", "name": "list_secrets", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "filters", "max_results", "next_token"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_secrets of SecretsManagerBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "put_resource_policy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "secret_id", "policy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.put_resource_policy", "name": "put_resource_policy", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "secret_id", "policy"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put_resource_policy of SecretsManagerBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "put_secret_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "secret_id", "secret_string", "secret_binary", "client_request_token", "version_stages"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.put_secret_value", "name": "put_secret_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "secret_id", "secret_string", "secret_binary", "client_request_token", "version_stages"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put_secret_value of SecretsManagerBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "restore_secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "secret_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.restore_secret", "name": "restore_secret", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "secret_id"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "restore_secret of SecretsManagerBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rotate_secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "secret_id", "client_request_token", "rotation_lambda_arn", "rotation_rules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.rotate_secret", "name": "rotate_secret", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "secret_id", "client_request_token", "rotation_lambda_arn", "rotation_rules"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rotate_secret of SecretsManagerBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "secrets": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.secrets", "name": "secrets", "type": "moto.secretsmanager.models.SecretsStore"}}, "tag_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "secret_id", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.tag_resource", "name": "tag_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "secret_id", "tags"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tag_resource of SecretsManagerBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "untag_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "secret_id", "tag_keys"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.untag_resource", "name": "untag_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "secret_id", "tag_keys"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "untag_resource of SecretsManagerBackend", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "secret_id", "secret_string", "secret_binary", "client_request_token", "kms_key_id", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.update_secret", "name": "update_secret", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "secret_id", "secret_string", "secret_binary", "client_request_token", "kms_key_id", "description"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_secret of SecretsManagerBackend", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_secret_version_stage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "secret_id", "version_stage", "remove_from_version_id", "move_to_version_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsManagerBackend.update_secret_version_stage", "name": "update_secret_version_stage", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "secret_id", "version_stage", "remove_from_version_id", "move_to_version_id"], "arg_types": ["moto.secretsmanager.models.SecretsManagerBackend", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_secret_version_stage of SecretsManagerBackend", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.secretsmanager.models.SecretsManagerBackend.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.secretsmanager.models.SecretsManagerBackend", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SecretsStore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", "moto.secretsmanager.models.FakeSecret"], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "moto.secretsmanager.models.SecretsStore", "name": "SecretsStore", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsStore", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "moto.secretsmanager.models", "mro": ["moto.secretsmanager.models.SecretsStore", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsStore.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["moto.secretsmanager.models.SecretsStore", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of SecretsStore", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsStore.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["moto.secretsmanager.models.SecretsStore", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SecretsStore", "ret_type": "moto.secretsmanager.models.FakeSecret", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsStore.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["moto.secretsmanager.models.SecretsStore", "builtins.str", "moto.secretsmanager.models.FakeSecret"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SecretsStore", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsStore.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["moto.secretsmanager.models.SecretsStore", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of SecretsStore", "ret_type": {".class": "UnionType", "items": ["moto.secretsmanager.models.FakeSecret", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.SecretsStore.pop", "name": "pop", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["moto.secretsmanager.models.SecretsStore", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop of SecretsStore", "ret_type": {".class": "UnionType", "items": ["moto.secretsmanager.models.FakeSecret", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "moto.secretsmanager.models.SecretsStore.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "moto.secretsmanager.models.SecretsStore", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.secretsmanager.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.secretsmanager.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.secretsmanager.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.secretsmanager.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.secretsmanager.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "moto.secretsmanager.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_filter_functions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.secretsmanager.models._filter_functions", "name": "_filter_functions", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["secret", null], "arg_types": ["moto.secretsmanager.models.FakeSecret", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_matches": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["secret", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models._matches", "name": "_matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["secret", "filters"], "arg_types": ["moto.secretsmanager.models.FakeSecret", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_matches", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "description_filter": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.list_secrets.filters.description_filter", "kind": "Gdef"}, "filter_all": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.list_secrets.filters.filter_all", "kind": "Gdef"}, "filter_keys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "moto.secretsmanager.models.filter_keys", "name": "filter_keys", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_keys", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_secret_name_from_partial_arn": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.utils.get_secret_name_from_partial_arn", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "mock_random": {".class": "SymbolTableNode", "cross_ref": "moto.moto_api._internal.mock_random", "kind": "Gdef"}, "name_filter": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.list_secrets.filters.name_filter", "kind": "Gdef"}, "random_password": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.utils.random_password", "kind": "Gdef"}, "secret_arn": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.utils.secret_arn", "kind": "Gdef"}, "secretsmanager_backends": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "moto.secretsmanager.models.secretsmanager_backends", "name": "secretsmanager_backends", "type": {".class": "Instance", "args": ["moto.secretsmanager.models.SecretsManagerBackend"], "extra_attrs": null, "type_ref": "moto.core.base_backend.BackendDict"}}}, "tag_key": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.list_secrets.filters.tag_key", "kind": "Gdef"}, "tag_value": {".class": "SymbolTableNode", "cross_ref": "moto.secretsmanager.list_secrets.filters.tag_value", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "utcfromtimestamp": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.utcfromtimestamp", "kind": "Gdef"}, "utcnow": {".class": "SymbolTableNode", "cross_ref": "moto.core.utils.utcnow", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/moto/secretsmanager/models.py"}