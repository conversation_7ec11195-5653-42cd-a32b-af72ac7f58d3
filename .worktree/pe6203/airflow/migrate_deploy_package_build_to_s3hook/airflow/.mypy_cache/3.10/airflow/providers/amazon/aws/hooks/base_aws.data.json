{".class": "MypyFile", "_fullname": "airflow.providers.amazon.aws.hooks.base_aws", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AioSession": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AioSession", "name": "AioSession", "type": {".class": "AnyType", "missing_import_name": "airflow.providers.amazon.aws.hooks.base_aws.AioSession", "source_any": null, "type_of_any": 3}}}, "AirflowException": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.AirflowException", "kind": "Gdef"}, "AirflowNotFoundException": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.AirflowNotFoundException", "kind": "Gdef"}, "AirflowProviderDeprecationWarning": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.AirflowProviderDeprecationWarning", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AwsBaseAsyncHook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.providers.amazon.aws.hooks.base_aws.AwsBaseHook"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsBaseAsyncHook", "name": "AwsBaseAsyncHook", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsBaseAsyncHook", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.providers.amazon.aws.hooks.base_aws", "mro": ["airflow.providers.amazon.aws.hooks.base_aws.AwsBaseAsyncHook", "airflow.providers.amazon.aws.hooks.base_aws.AwsBaseHook", "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "airflow.hooks.base.BaseHook", "airflow.utils.log.logging_mixin.LoggingMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsBaseAsyncHook.__init__", "name": "__init__", "type": null}}, "get_async_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsBaseAsyncHook.get_async_session", "name": "get_async_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.AwsBaseAsyncHook"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_async_session of AwsBaseAsyncHook", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.providers.amazon.aws.hooks.base_aws.AioSession", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_client_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsBaseAsyncHook.get_client_async", "name": "get_client_async", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsBaseAsyncHook.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.providers.amazon.aws.hooks.base_aws.AwsBaseAsyncHook", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AwsBaseHook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsBaseHook", "name": "AwsBaseHook", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsBaseHook", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.providers.amazon.aws.hooks.base_aws", "mro": ["airflow.providers.amazon.aws.hooks.base_aws.AwsBaseHook", "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "airflow.hooks.base.BaseHook", "airflow.utils.log.logging_mixin.LoggingMixin", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsBaseHook.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.providers.amazon.aws.hooks.base_aws.AwsBaseHook", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AwsConnectionWrapper": {".class": "SymbolTableNode", "cross_ref": "airflow.providers.amazon.aws.utils.connection_wrapper.AwsConnectionWrapper", "kind": "Gdef"}, "AwsGenericHook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.hooks.base.BaseHook"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "name": "AwsGenericHook", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.providers.amazon.aws.hooks.base_aws", "mro": ["airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "airflow.hooks.base.BaseHook", "airflow.utils.log.logging_mixin.LoggingMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "aws_conn_id", "verify", "region_name", "client_type", "resource_type", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "aws_conn_id", "verify", "region_name", "client_type", "resource_type", "config"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["botocore.config.Config", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AwsGenericHook", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_parameters_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["config", "waiter_name", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._apply_parameters_value", "name": "_apply_parameters_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["config", "waiter_name", "parameters"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_parameters_value of AwsGenericHook", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._apply_parameters_value", "name": "_apply_parameters_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["config", "waiter_name", "parameters"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_parameters_value of AwsGenericHook", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._client", "name": "_client", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_client of AwsGenericHook", "ret_type": "botocore.client.BaseClient", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._client", "name": "_client", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_client of AwsGenericHook", "ret_type": "botocore.client.BaseClient", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._config", "name": "_config", "type": {".class": "UnionType", "items": ["botocore.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_find_executor_class_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._find_executor_class_name", "name": "_find_executor_class_name", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_executor_class_name of AwsGenericHook", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._find_executor_class_name", "name": "_find_executor_class_name", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_executor_class_name of AwsGenericHook", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_find_operator_class_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["target_function_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._find_operator_class_name", "name": "_find_operator_class_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["target_function_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_operator_class_name of AwsGenericHook", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._find_operator_class_name", "name": "_find_operator_class_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["target_function_name"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_operator_class_name of AwsGenericHook", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_generate_dag_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._generate_dag_key", "name": "_generate_dag_key", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_dag_key of AwsGenericHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._generate_dag_key", "name": "_generate_dag_key", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "_generate_user_agent_extra_field": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "existing_user_agent_extra"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._generate_user_agent_extra_field", "name": "_generate_user_agent_extra_field", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "existing_user_agent_extra"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_user_agent_extra_field of AwsGenericHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_airflow_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._get_airflow_version", "name": "_get_airflow_version", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_airflow_version of AwsGenericHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._get_airflow_version", "name": "_get_airflow_version", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "_get_caller": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "target_function_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._get_caller", "name": "_get_caller", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "target_function_name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_caller of AwsGenericHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._get_caller", "name": "_get_caller", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "_get_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._get_config", "name": "_get_config", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "config"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}, {".class": "UnionType", "items": ["botocore.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_config of AwsGenericHook", "ret_type": "botocore.config.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_provider_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._get_provider_version", "name": "_get_provider_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_provider_version of AwsGenericHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._get_provider_version", "name": "_get_provider_version", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "_list_custom_waiters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._list_custom_waiters", "name": "_list_custom_waiters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_list_custom_waiters of AwsGenericHook", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_list_official_waiters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._list_official_waiters", "name": "_list_official_waiters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_list_official_waiters of AwsGenericHook", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._region_name", "name": "_region_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_resolve_service_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "is_resource_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._resolve_service_name", "name": "_resolve_service_name", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "is_resource_type"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_service_name of AwsGenericHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_verify": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook._verify", "name": "_verify", "type": {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "async_conn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.async_conn", "name": "async_conn", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.async_conn", "name": "async_conn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "async_conn of AwsGenericHook", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "aws_conn_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.aws_conn_id", "name": "aws_conn_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "client_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.client_type", "name": "client_type", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.config", "name": "config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config of AwsGenericHook", "ret_type": "botocore.config.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.config", "name": "config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config of AwsGenericHook", "ret_type": "botocore.config.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "conn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.conn", "name": "conn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conn of AwsGenericHook", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.conn", "name": "conn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conn of AwsGenericHook", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "conn_client_meta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.conn_client_meta", "name": "conn_client_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conn_client_meta of AwsGenericHook", "ret_type": "botocore.client.ClientMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.conn_client_meta", "name": "conn_client_meta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conn_client_meta of AwsGenericHook", "ret_type": "botocore.client.ClientMeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "conn_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.conn_config", "name": "conn_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conn_config of AwsGenericHook", "ret_type": "airflow.providers.amazon.aws.utils.connection_wrapper.AwsConnectionWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.conn_config", "name": "conn_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conn_config of AwsGenericHook", "ret_type": "airflow.providers.amazon.aws.utils.connection_wrapper.AwsConnectionWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "conn_name_attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.conn_name_attr", "name": "conn_name_attr", "type": "builtins.str"}}, "conn_partition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.conn_partition", "name": "conn_partition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conn_partition of AwsGenericHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.conn_partition", "name": "conn_partition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conn_partition of AwsGenericHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "conn_region_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.conn_region_name", "name": "conn_region_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conn_region_name of AwsGenericHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.conn_region_name", "name": "conn_region_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conn_region_name of AwsGenericHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "conn_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.conn_type", "name": "conn_type", "type": "builtins.str"}}, "default_conn_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.default_conn_name", "name": "default_conn_name", "type": "builtins.str"}}, "expand_role": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "role", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.expand_role", "name": "expand_role", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "role", "region_name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expand_role of AwsGenericHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_client_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "region_name", "config", "deferrable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.get_client_type", "name": "get_client_type", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "region_name", "config", "deferrable"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["botocore.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_client_type of AwsGenericHook", "ret_type": {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_conn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.get_conn", "name": "get_conn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_conn of AwsGenericHook", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "region_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.get_credentials", "name": "get_credentials", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "region_name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_credentials of AwsGenericHook", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "botocore.credentials.ReadOnlyCredentials"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resource_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "region_name", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.get_resource_type", "name": "get_resource_type", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "region_name", "config"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["botocore.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource_type of AwsGenericHook", "ret_type": {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "region_name", "deferrable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.get_session", "name": "get_session", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "region_name", "deferrable"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_session of AwsGenericHook", "ret_type": "boto3.session.Session", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_ui_field_behaviour": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.get_ui_field_behaviour", "name": "get_ui_field_behaviour", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ui_field_behaviour of AwsGenericHook", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.get_ui_field_behaviour", "name": "get_ui_field_behaviour", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_ui_field_behaviour of AwsGenericHook", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_waiter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "waiter_name", "parameters", "deferrable", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.get_waiter", "name": "get_waiter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "waiter_name", "parameters", "deferrable", "client"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}, "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_waiter of AwsGenericHook", "ret_type": "botocore.waiter.Waiter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hook_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.hook_name", "name": "hook_name", "type": "builtins.str"}}, "list_waiters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.list_waiters", "name": "list_waiters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_waiters of AwsGenericHook", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "region_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.region_name", "name": "region_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "region_name of AwsGenericHook", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.region_name", "name": "region_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "region_name of AwsGenericHook", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "resource_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.resource_type", "name": "resource_type", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["should_retry"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.retry", "name": "retry", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["should_retry"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.Exception"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "retry of AwsGenericHook", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.retry", "name": "retry", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["should_retry"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.Exception"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "retry of AwsGenericHook", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "service_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.service_config", "name": "service_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "service_config of AwsGenericHook", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.service_config", "name": "service_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "service_config of AwsGenericHook", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "service_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.service_name", "name": "service_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "service_name of AwsGenericHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.service_name", "name": "service_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "service_name of AwsGenericHook", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.test_connection", "name": "test_connection", "type": null}}, "verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of AwsGenericHook", "ret_type": {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.verify", "name": "verify", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify of AwsGenericHook", "ret_type": {".class": "UnionType", "items": ["builtins.bool", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "waiter_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.waiter_path", "name": "waiter_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "waiter_path of AwsGenericHook", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.waiter_path", "name": "waiter_path", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "waiter_path of AwsGenericHook", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "id": 1, "name": "BaseAwsConnection", "namespace": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "airflow.providers.amazon.aws.hooks.base_aws.AwsGenericHook"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["BaseAwsConnection"], "typeddict_type": null}}, "BaseAsyncSessionFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory", "name": "BaseAsyncSessionFactory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.providers.amazon.aws.hooks.base_aws", "mro": ["airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory", "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", "airflow.utils.log.logging_mixin.LoggingMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory.__init__", "name": "__init__", "type": null}}, "_basic_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory._basic_session", "name": "_basic_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_basic_session of BaseAsyncSessionFactory", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.providers.amazon.aws.hooks.base_aws.AioSession", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory._basic_session", "name": "_basic_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_basic_session of BaseAsyncSessionFactory", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.providers.amazon.aws.hooks.base_aws.AioSession", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_refresh_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory._get_refresh_credentials", "name": "_get_refresh_credentials", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_refresh_credentials of BaseAsyncSessionFactory", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_session_with_assume_role": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory._get_session_with_assume_role", "name": "_get_session_with_assume_role", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_session_with_assume_role of BaseAsyncSessionFactory", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.providers.amazon.aws.hooks.base_aws.AioSession", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "deferrable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory.create_session", "name": "create_session", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "deferrable"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_session of BaseAsyncSessionFactory", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.providers.amazon.aws.hooks.base_aws.AioSession", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_role_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory.get_role_credentials", "name": "get_role_credentials", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_role_credentials of BaseAsyncSessionFactory", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.providers.amazon.aws.hooks.base_aws.BaseAsyncSessionFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseAwsConnection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseAwsConnection", "name": "BaseAwsConnection", "upper_bound": {".class": "UnionType", "items": [{".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}, {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.resource"}], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "BaseHook": {".class": "SymbolTableNode", "cross_ref": "airflow.hooks.base.BaseHook", "kind": "Gdef"}, "BaseSessionFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.utils.log.logging_mixin.LoggingMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", "name": "BaseSessionFactory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.providers.amazon.aws.hooks.base_aws", "mro": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", "airflow.utils.log.logging_mixin.LoggingMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "conn", "region_name", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "conn", "region_name", "config"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", {".class": "UnionType", "items": ["airflow.models.connection.Connection", "airflow.providers.amazon.aws.utils.connection_wrapper.AwsConnectionWrapper", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["botocore.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseSessionFactory", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_session_kwargs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._apply_session_kwargs", "name": "_apply_session_kwargs", "type": null}}, "_assume_role": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sts_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._assume_role", "name": "_assume_role", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sts_client"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assume_role of BaseSessionFactory", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_assume_role_with_saml": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sts_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._assume_role_with_saml", "name": "_assume_role_with_saml", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sts_client"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "boto3.client"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assume_role_with_saml of BaseSessionFactory", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._config", "name": "_config", "type": {".class": "UnionType", "items": ["botocore.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_conn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._conn", "name": "_conn", "type": {".class": "UnionType", "items": ["airflow.models.connection.Connection", "airflow.providers.amazon.aws.utils.connection_wrapper.AwsConnectionWrapper", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_create_basic_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._create_basic_session", "name": "_create_basic_session", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "session_kwargs"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_basic_session of BaseSessionFactory", "ret_type": "boto3.session.Session", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_session_with_assume_role": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "session_kwargs", "deferrable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._create_session_with_assume_role", "name": "_create_session_with_assume_role", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "session_kwargs", "deferrable"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_session_with_assume_role of BaseSessionFactory", "ret_type": "boto3.session.Session", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fetch_saml_assertion_using_http_spegno_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "saml_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._fetch_saml_assertion_using_http_spegno_auth", "name": "_fetch_saml_assertion_using_http_spegno_auth", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "saml_config"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fetch_saml_assertion_using_http_spegno_auth of BaseSessionFactory", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_file_token_loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._get_file_token_loader", "name": "_get_file_token_loader", "type": null}}, "_get_google_identity_token_loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._get_google_identity_token_loader", "name": "_get_google_identity_token_loader", "type": null}}, "_get_idp_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "saml_config", "auth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._get_idp_response", "name": "_get_idp_response", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "saml_config", "auth"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": "airflow.providers.amazon.aws.hooks.base_aws.requests", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_idp_response of BaseSessionFactory", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.providers.amazon.aws.hooks.base_aws.requests", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_web_identity_credential_fetcher": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._get_web_identity_credential_fetcher", "name": "_get_web_identity_credential_fetcher", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_web_identity_credential_fetcher of BaseSessionFactory", "ret_type": "botocore.credentials.AssumeRoleWithWebIdentityCredentialFetcher", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_refresh_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._refresh_credentials", "name": "_refresh_credentials", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_refresh_credentials of BaseSessionFactory", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_region_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._region_name", "name": "_region_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_strip_invalid_session_name_characters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "role_session_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory._strip_invalid_session_name_characters", "name": "_strip_invalid_session_name_characters", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "role_session_name"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_strip_invalid_session_name_characters of BaseSessionFactory", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "basic_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.basic_session", "name": "basic_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "basic_session of BaseSessionFactory", "ret_type": "boto3.session.Session", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.basic_session", "name": "basic_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "basic_session of BaseSessionFactory", "ret_type": "boto3.session.Session", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.config", "name": "config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config of BaseSessionFactory", "ret_type": {".class": "UnionType", "items": ["botocore.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.config", "name": "config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config of BaseSessionFactory", "ret_type": {".class": "UnionType", "items": ["botocore.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "conn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.conn", "name": "conn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conn of BaseSessionFactory", "ret_type": "airflow.providers.amazon.aws.utils.connection_wrapper.AwsConnectionWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.conn", "name": "conn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conn of BaseSessionFactory", "ret_type": "airflow.providers.amazon.aws.utils.connection_wrapper.AwsConnectionWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "deferrable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.create_session", "name": "create_session", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "deferrable"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_session of BaseSessionFactory", "ret_type": "boto3.session.Session", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extra_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.extra_config", "name": "extra_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extra_config of BaseSessionFactory", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.extra_config", "name": "extra_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extra_config of BaseSessionFactory", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_async_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.get_async_session", "name": "get_async_session", "type": null}}, "region_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.region_name", "name": "region_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "region_name of BaseSessionFactory", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.region_name", "name": "region_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "region_name of BaseSessionFactory", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "role_arn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.role_arn", "name": "role_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "role_arn of BaseSessionFactory", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.role_arn", "name": "role_arn", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "role_arn of BaseSessionFactory", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClientMeta": {".class": "SymbolTableNode", "cross_ref": "botocore.client.ClientMeta", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "botocore.config.Config", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "airflow.models.connection.Connection", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "LoggingMixin": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.log.logging_mixin.LoggingMixin", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "ProvidersManager": {".class": "SymbolTableNode", "cross_ref": "airflow.providers_manager.ProvidersManager", "kind": "Gdef"}, "ReadOnlyCredentials": {".class": "SymbolTableNode", "cross_ref": "botocore.credentials.ReadOnlyCredentials", "kind": "Gdef"}, "SessionFactory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.SessionFactory", "name": "SessionFactory", "type": {".class": "TypeType", "item": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "Waiter": {".class": "SymbolTableNode", "cross_ref": "botocore.waiter.Waiter", "kind": "Gdef"}, "WaiterModel": {".class": "SymbolTableNode", "cross_ref": "botocore.waiter.WaiterModel", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_parse_s3_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["config_file_name", "config_format", "profile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws._parse_s3_config", "name": "_parse_s3_config", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["config_file_name", "config_format", "profile"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_s3_config", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aiobotocore": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.aiobotocore", "name": "aiobotocore", "type": {".class": "AnyType", "missing_import_name": "airflow.providers.amazon.aws.hooks.base_aws.aiobotocore", "source_any": null, "type_of_any": 3}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "boto3": {".class": "SymbolTableNode", "cross_ref": "boto3", "kind": "Gdef"}, "botocore": {".class": "SymbolTableNode", "cross_ref": "botocore", "kind": "Gdef"}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef"}, "conf": {".class": "SymbolTableNode", "cross_ref": "airflow.configuration.conf", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "deepcopy": {".class": "SymbolTableNode", "cross_ref": "copy.deepcopy", "kind": "Gdef"}, "exactly_one": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.helpers.exactly_one", "kind": "Gdef"}, "generate_uuid": {".class": "SymbolTableNode", "cross_ref": "airflow.providers.amazon.aws.utils.identifiers.generate_uuid", "kind": "Gdef"}, "get_session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.get_session", "name": "get_session", "type": {".class": "AnyType", "missing_import_name": "airflow.providers.amazon.aws.hooks.base_aws.get_session", "source_any": null, "type_of_any": 3}}}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "jinja2": {".class": "SymbolTableNode", "cross_ref": "jinja2", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "mask_secret": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.log.secrets_masker.mask_secret", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.requests", "name": "requests", "type": {".class": "AnyType", "missing_import_name": "airflow.providers.amazon.aws.hooks.base_aws.requests", "source_any": null, "type_of_any": 3}}}, "resolve_session_factory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.resolve_session_factory", "name": "resolve_session_factory", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_session_factory", "ret_type": {".class": "TypeType", "item": "airflow.providers.amazon.aws.hooks.base_aws.BaseSessionFactory"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "return_on_error": {".class": "SymbolTableNode", "cross_ref": "airflow.providers.amazon.aws.utils.suppress.return_on_error", "kind": "Gdef"}, "slugify": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.slugify", "name": "slugify", "type": {".class": "AnyType", "missing_import_name": "airflow.providers.amazon.aws.hooks.base_aws.slugify", "source_any": null, "type_of_any": 3}}}, "tenacity": {".class": "SymbolTableNode", "cross_ref": "tenacity", "kind": "Gdef"}, "tzlocal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.providers.amazon.aws.hooks.base_aws.tzlocal", "name": "tzlocal", "type": {".class": "AnyType", "missing_import_name": "airflow.providers.amazon.aws.hooks.base_aws.tzlocal", "source_any": null, "type_of_any": 3}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/providers/amazon/aws/hooks/base_aws.py"}