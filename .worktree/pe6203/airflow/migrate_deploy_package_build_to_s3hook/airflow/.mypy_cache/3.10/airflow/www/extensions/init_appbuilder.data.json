{".class": "MypyFile", "_fullname": "airflow.www.extensions.init_appbuilder", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AirflowAppBuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder", "name": "AirflowAppBuilder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.extensions.init_appbuilder", "mro": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "app", "session", "menu", "indexview", "base_template", "static_folder", "static_url_path", "update_perms", "auth_rate_limited", "auth_rate_limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "app", "session", "menu", "indexview", "base_template", "static_folder", "static_url_path", "update_perms", "auth_rate_limited", "auth_rate_limit"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.Session", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AirflowAppBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_addon_views": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder._add_addon_views", "name": "_add_addon_views", "type": null}}, "_add_admin_views": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder._add_admin_views", "name": "_add_admin_views", "type": null}}, "_add_global_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder._add_global_filters", "name": "_add_global_filters", "type": null}}, "_add_global_static": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder._add_global_static", "name": "_add_global_static", "type": null}}, "_add_menu_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "update_perms"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder._add_menu_permissions", "name": "_add_menu_permissions", "type": null}}, "_add_permission": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "baseview", "update_perms"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder._add_permission", "name": "_add_permission", "type": null}}, "_add_permissions_menu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "update_perms"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder._add_permissions_menu", "name": "_add_permissions_menu", "type": null}}, "_addon_managers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder._addon_managers", "name": "_addon_managers", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_check_and_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "baseview"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder._check_and_init", "name": "_check_and_init", "type": null}}, "_init_extension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder._init_extension", "name": "_init_extension", "type": null}}, "_process_inner_views": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder._process_inner_views", "name": "_process_inner_views", "type": null}}, "_swap_url_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder._swap_url_filter", "name": "_swap_url_filter", "type": null}}, "_view_exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "view"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder._view_exists", "name": "_view_exists", "type": null}}, "add_limits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "baseview"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.add_limits", "name": "add_limits", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "baseview"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_limits of AirflowAppBuilder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_link": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "name", "href", "icon", "label", "category", "category_icon", "category_label", "baseview", "cond"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.add_link", "name": "add_link", "type": null}}, "add_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "update_perms"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.add_permissions", "name": "add_permissions", "type": null}}, "add_separator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "category", "cond"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.add_separator", "name": "add_separator", "type": null}}, "add_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "baseview", "name", "href", "icon", "label", "category", "category_icon", "category_label", "menu_cond"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.add_view", "name": "add_view", "type": null}}, "add_view_no_menu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "baseview", "endpoint", "static_folder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.add_view_no_menu", "name": "add_view_no_menu", "type": null}}, "addon_managers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.addon_managers", "name": "addon_managers", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "app": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.app", "name": "app", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "app_icon": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.app_icon", "name": "app_icon", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.app_icon", "name": "app_icon", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "app_icon of AirflowAppBuilder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "app_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.app_name", "name": "app_name", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.app_name", "name": "app_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "app_name of AirflowAppBuilder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "app_theme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.app_theme", "name": "app_theme", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.app_theme", "name": "app_theme", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "app_theme of AirflowAppBuilder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "auth_rate_limit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.auth_rate_limit", "name": "auth_rate_limit", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "auth_rate_limited": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.auth_rate_limited", "name": "auth_rate_limited", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "base_template": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.base_template", "name": "base_template", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "baseviews": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.baseviews", "name": "baseviews", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.BaseView", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.Session", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "bm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.bm", "name": "bm", "type": {".class": "NoneType"}}}, "get_app": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.get_app", "name": "get_app", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.get_app", "name": "get_app", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_app of AirflowAppBuilder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.get_session", "name": "get_session", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.get_session", "name": "get_session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_session of AirflowAppBuilder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_url_for_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.get_url_for_index", "name": "get_url_for_index", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.get_url_for_index", "name": "get_url_for_index", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_url_for_index of AirflowAppBuilder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_url_for_locale": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lang"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.get_url_for_locale", "name": "get_url_for_locale", "type": null}}, "get_url_for_login": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.get_url_for_login", "name": "get_url_for_login", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.get_url_for_login", "name": "get_url_for_login", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_url_for_login of AirflowAppBuilder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_url_for_login_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "next_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.get_url_for_login_with", "name": "get_url_for_login_with", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "next_url"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_url_for_login_with of AirflowAppBuilder", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "indexview": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.indexview", "name": "indexview", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "init_app": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "app", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.init_app", "name": "init_app", "type": null}}, "languages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.languages", "name": "languages", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.languages", "name": "languages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "languages of AirflowAppBuilder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "menu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.menu", "name": "menu", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.post_init", "name": "post_init", "type": null}}, "register_blueprint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "baseview", "endpoint", "static_folder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.register_blueprint", "name": "register_blueprint", "type": null}}, "security_cleanup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.security_cleanup", "name": "security_cleanup", "type": null}}, "security_converge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "dry"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.security_converge", "name": "security_converge", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "dry"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "security_converge of AirflowAppBuilder", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.session", "name": "session", "type": {".class": "NoneType"}}}, "sm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.sm", "name": "sm", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.BaseSecurityManager", "source_any": null, "type_of_any": 3}}}, "static_folder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.static_folder", "name": "static_folder", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "static_url_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.static_url_path", "name": "static_url_path", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "template_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.template_filters", "name": "template_filters", "type": {".class": "NoneType"}}}, "update_perms": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.update_perms", "name": "update_perms", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.version", "name": "version", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of AirflowAppBuilder", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BabelManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.BabelManager", "name": "BabelManager", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.BabelManager", "source_any": null, "type_of_any": 3}}}, "BaseSecurityManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.BaseSecurityManager", "name": "BaseSecurityManager", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.BaseSecurityManager", "source_any": null, "type_of_any": 3}}}, "BaseView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.BaseView", "name": "BaseView", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.BaseView", "source_any": null, "type_of_any": 3}}}, "Blueprint": {".class": "SymbolTableNode", "cross_ref": "flask.blueprints.Blueprint", "kind": "Gdef"}, "Flask": {".class": "SymbolTableNode", "cross_ref": "flask.app.Flask", "kind": "Gdef"}, "IndexView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.IndexView", "name": "IndexView", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.IndexView", "source_any": null, "type_of_any": 3}}}, "LOGMSG_ERR_FAB_ADDON_IMPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.LOGMSG_ERR_FAB_ADDON_IMPORT", "name": "LOGMSG_ERR_FAB_ADDON_IMPORT", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.LOGMSG_ERR_FAB_ADDON_IMPORT", "source_any": null, "type_of_any": 3}}}, "LOGMSG_ERR_FAB_ADDON_PROCESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.LOGMSG_ERR_FAB_ADDON_PROCESS", "name": "LOGMSG_ERR_FAB_ADDON_PROCESS", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.LOGMSG_ERR_FAB_ADDON_PROCESS", "source_any": null, "type_of_any": 3}}}, "LOGMSG_ERR_FAB_ADD_PERMISSION_MENU": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.LOGMSG_ERR_FAB_ADD_PERMISSION_MENU", "name": "LOGMSG_ERR_FAB_ADD_PERMISSION_MENU", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.LOGMSG_ERR_FAB_ADD_PERMISSION_MENU", "source_any": null, "type_of_any": 3}}}, "LOGMSG_ERR_FAB_ADD_PERMISSION_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.LOGMSG_ERR_FAB_ADD_PERMISSION_VIEW", "name": "LOGMSG_ERR_FAB_ADD_PERMISSION_VIEW", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.LOGMSG_ERR_FAB_ADD_PERMISSION_VIEW", "source_any": null, "type_of_any": 3}}}, "LOGMSG_INF_FAB_ADDON_ADDED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.LOGMSG_INF_FAB_ADDON_ADDED", "name": "LOGMSG_INF_FAB_ADDON_ADDED", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.LOGMSG_INF_FAB_ADDON_ADDED", "source_any": null, "type_of_any": 3}}}, "LOGMSG_INF_FAB_ADD_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.LOGMSG_INF_FAB_ADD_VIEW", "name": "LOGMSG_INF_FAB_ADD_VIEW", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.LOGMSG_INF_FAB_ADD_VIEW", "source_any": null, "type_of_any": 3}}}, "LOGMSG_WAR_FAB_VIEW_EXISTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.LOGMSG_WAR_FAB_VIEW_EXISTS", "name": "LOGMSG_WAR_FAB_VIEW_EXISTS", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.LOGMSG_WAR_FAB_VIEW_EXISTS", "source_any": null, "type_of_any": 3}}}, "Menu": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.Menu", "name": "<PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.Menu", "source_any": null, "type_of_any": 3}}}, "Session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.Session", "name": "Session", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.Session", "source_any": null, "type_of_any": 3}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TemplateFilters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.TemplateFilters", "name": "TemplateFilters", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.TemplateFilters", "source_any": null, "type_of_any": 3}}}, "UtilView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.UtilView", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.UtilView", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.extensions.init_appbuilder.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.extensions.init_appbuilder.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.extensions.init_appbuilder.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.extensions.init_appbuilder.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.extensions.init_appbuilder.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.extensions.init_appbuilder.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.extensions.init_appbuilder.__version__", "name": "__version__", "type": {".class": "AnyType", "missing_import_name": "airflow.www.extensions.init_appbuilder.__version__", "source_any": null, "type_of_any": 3}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "conf": {".class": "SymbolTableNode", "cross_ref": "airflow.configuration.conf", "kind": "Gdef"}, "current_app": {".class": "SymbolTableNode", "cross_ref": "flask.globals.current_app", "kind": "Gdef"}, "dynamic_class_import": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["class_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.dynamic_class_import", "name": "dynamic_class_import", "type": null}}, "get_auth_manager": {".class": "SymbolTableNode", "cross_ref": "airflow.www.extensions.init_auth_manager.get_auth_manager", "kind": "Gdef"}, "init_appbuilder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["app"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_appbuilder.init_appbuilder", "name": "init_appbuilder", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["app"], "arg_types": ["flask.app.Flask"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_appbuilder", "ret_type": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_auth_manager": {".class": "SymbolTableNode", "cross_ref": "airflow.www.extensions.init_auth_manager.init_auth_manager", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "airflow.www.extensions.init_appbuilder.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "reduce": {".class": "SymbolTableNode", "cross_ref": "functools.reduce", "kind": "Gdef"}, "settings": {".class": "SymbolTableNode", "cross_ref": "airflow.settings", "kind": "Gdef"}, "url_for": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.url_for", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/www/extensions/init_appbuilder.py"}