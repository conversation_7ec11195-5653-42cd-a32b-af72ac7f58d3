{".class": "MypyFile", "_fullname": "airflow.utils.log.task_context_logger", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "FileTaskHandler": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.log.file_task_handler.FileTaskHandler", "kind": "Gdef"}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TaskContextLogger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger", "name": "TaskContextLogger", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.log.task_context_logger", "mro": ["airflow.utils.log.task_context_logger.TaskContextLogger", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "component_name", "call_site_logger"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "component_name", "call_site_logger"], "arg_types": ["airflow.utils.log.task_context_logger.TaskContextLogger", "builtins.str", {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TaskContextLogger", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_task_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger._get_task_handler", "name": "_get_task_handler", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_task_handler of TaskContextLogger", "ret_type": {".class": "UnionType", "items": ["airflow.utils.log.file_task_handler.FileTaskHandler", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger._get_task_handler", "name": "_get_task_handler", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_task_handler of TaskContextLogger", "ret_type": {".class": "UnionType", "items": ["airflow.utils.log.file_task_handler.FileTaskHandler", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 3], "arg_names": ["self", "level", "msg", "args", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger._log", "name": "_log", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 3], "arg_names": ["self", "level", "msg", "args", "ti"], "arg_types": ["airflow.utils.log.task_context_logger.TaskContextLogger", "builtins.int", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "airflow.models.taskinstance.TaskInstance"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_log of TaskContextLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_should_enable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger._should_enable", "name": "_should_enable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.task_context_logger.TaskContextLogger"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_enable of TaskContextLogger", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_site_logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.call_site_logger", "name": "call_site_logger", "type": {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "component_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.component_name", "name": "component_name", "type": "builtins.str"}}, "critical": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.critical", "name": "critical", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "arg_types": ["airflow.utils.log.task_context_logger.TaskContextLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "airflow.models.taskinstance.TaskInstance"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "critical of TaskContextLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.debug", "name": "debug", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "arg_types": ["airflow.utils.log.task_context_logger.TaskContextLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "airflow.models.taskinstance.TaskInstance"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debug of TaskContextLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enabled": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.enabled", "name": "enabled", "type": "builtins.bool"}}, "error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.error", "name": "error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "arg_types": ["airflow.utils.log.task_context_logger.TaskContextLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "airflow.models.taskinstance.TaskInstance"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "error of TaskContextLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fatal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.fatal", "name": "fatal", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "arg_types": ["airflow.utils.log.task_context_logger.TaskContextLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "airflow.models.taskinstance.TaskInstance"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fatal of <PERSON>C<PERSON>xt<PERSON>og<PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "arg_types": ["airflow.utils.log.task_context_logger.TaskContextLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "airflow.models.taskinstance.TaskInstance"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of TaskContextLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "notset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.notset", "name": "notset", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "arg_types": ["airflow.utils.log.task_context_logger.TaskContextLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "airflow.models.taskinstance.TaskInstance"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "notset of TaskContextLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "task_handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.task_handler", "name": "task_handler", "type": {".class": "UnionType", "items": ["airflow.utils.log.file_task_handler.FileTaskHandler", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "warn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.warn", "name": "warn", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "arg_types": ["airflow.utils.log.task_context_logger.TaskContextLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "airflow.models.taskinstance.TaskInstance"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warn of TaskContextLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.warning", "name": "warning", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 3], "arg_names": ["self", "msg", "args", "ti"], "arg_types": ["airflow.utils.log.task_context_logger.TaskContextLogger", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "airflow.models.taskinstance.TaskInstance"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "warning of TaskContextLogger", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.task_context_logger.TaskContextLogger.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.log.task_context_logger.TaskContextLogger", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TaskInstance": {".class": "SymbolTableNode", "cross_ref": "airflow.models.taskinstance.TaskInstance", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.task_context_logger.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.task_context_logger.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.task_context_logger.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.task_context_logger.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.task_context_logger.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.task_context_logger.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "conf": {".class": "SymbolTableNode", "cross_ref": "airflow.configuration.conf", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy.copy", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "airflow.utils.log.task_context_logger.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "suppress": {".class": "SymbolTableNode", "cross_ref": "contextlib.suppress", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/log/task_context_logger.py"}