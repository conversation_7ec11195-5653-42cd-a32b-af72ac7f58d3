{".class": "MypyFile", "_fullname": "airflow.utils.timeout", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AirflowTaskTimeout": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.AirflowTaskTimeout", "kind": "Gdef"}, "ContextManager": {".class": "SymbolTableNode", "cross_ref": "typing.ContextManager", "kind": "Gdef"}, "IS_WINDOWS": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.platform.IS_WINDOWS", "kind": "Gdef"}, "LoggingMixin": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.log.logging_mixin.LoggingMixin", "kind": "Gdef"}, "TimeoutPosix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.ContextManager"}, "airflow.utils.log.logging_mixin.LoggingMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.timeout.TimeoutPosix", "name": "TimeoutPosix", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.utils.timeout.TimeoutPosix", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "airflow.utils.timeout", "mro": ["airflow.utils.timeout.TimeoutPosix", "typing.ContextManager", "contextlib.AbstractContextManager", "abc.ABC", "airflow.utils.log.logging_mixin.LoggingMixin", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.timeout.TimeoutPosix.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.timeout.TimeoutPosix.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "seconds", "error_message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.timeout.TimeoutPosix.__init__", "name": "__init__", "type": null}}, "error_message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.timeout.TimeoutPosix.error_message", "name": "error_message", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "handle_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "signum", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.timeout.TimeoutPosix.handle_timeout", "name": "handle_timeout", "type": null}}, "seconds": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.timeout.TimeoutPosix.seconds", "name": "seconds", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.timeout.TimeoutPosix.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.timeout.TimeoutPosix", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeoutWindows": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.ContextManager"}, "airflow.utils.log.logging_mixin.LoggingMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.timeout.TimeoutWindows", "name": "TimeoutWindows", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.utils.timeout.TimeoutWindows", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "airflow.utils.timeout", "mro": ["airflow.utils.timeout.TimeoutWindows", "typing.ContextManager", "contextlib.AbstractContextManager", "abc.ABC", "airflow.utils.log.logging_mixin.LoggingMixin", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.timeout.TimeoutWindows.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.timeout.TimeoutWindows.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "seconds", "error_message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.timeout.TimeoutWindows.__init__", "name": "__init__", "type": null}}, "_timer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "airflow.utils.timeout.TimeoutWindows._timer", "name": "_timer", "type": {".class": "UnionType", "items": ["threading.Timer", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "error_message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.timeout.TimeoutWindows.error_message", "name": "error_message", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "handle_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.timeout.TimeoutWindows.handle_timeout", "name": "handle_timeout", "type": null}}, "seconds": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.timeout.TimeoutWindows.seconds", "name": "seconds", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.timeout.TimeoutWindows.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.timeout.TimeoutWindows", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Timer": {".class": "SymbolTableNode", "cross_ref": "threading.Timer", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.timeout.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.timeout.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.timeout.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.timeout.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.timeout.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.timeout.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_timeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "airflow.utils.timeout._timeout", "line": 29, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.ContextManager"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "signal": {".class": "SymbolTableNode", "cross_ref": "signal", "kind": "Gdef"}, "timeout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "airflow.utils.timeout.timeout", "name": "timeout", "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "airflow.utils.timeout.TimeoutWindows"}, {".class": "TypeType", "item": "airflow.utils.timeout.TimeoutPosix"}], "uses_pep604_syntax": false}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/timeout.py"}