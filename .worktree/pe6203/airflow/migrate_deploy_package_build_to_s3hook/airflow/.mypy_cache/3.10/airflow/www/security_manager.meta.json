{"data_mtime": 1756971053, "dep_lines": [28, 29, 37, 79, 80, 69, 71, 42, 70, 72, 25, 40, 41, 17, 19, 20, 21, 23, 24, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 77, 26], "dep_prios": [5, 5, 5, 25, 25, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 5], "dependencies": ["airflow.auth.managers.fab.security_manager.constants", "airflow.auth.managers.models.resource_details", "airflow.auth.managers.utils.fab", "airflow.auth.managers.fab.models", "airflow.auth.managers.models.base_user", "airflow.utils.log.logging_mixin", "airflow.www.extensions.init_auth_manager", "airflow.security.permissions", "airflow.utils.session", "airflow.www.utils", "flask_limiter.util", "airflow.exceptions", "airflow.models", "__future__", "json", "functools", "typing", "flask", "flask_limiter", "builtins", "_frozen_importlib", "abc", "airflow.auth", "airflow.auth.managers", "airflow.auth.managers.base_auth_manager", "airflow.auth.managers.fab", "airflow.auth.managers.models", "airflow.utils", "airflow.utils.log", "airflow.www.extensions", "flask.ctx", "flask.globals", "flask_limiter.extension"], "hash": "ec00d715bc0b08f70740f524823e5d5c3ee3c996", "id": "airflow.www.security_manager", "ignore_all": true, "interface_hash": "66ad78d20a3f3b4419707e964ca05e4b1b11e07f", "mtime": 1743589731, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/www/security_manager.py", "plugin_data": null, "size": 14977, "suppressed": ["sqlalchemy.orm", "sqlalchemy"], "version_id": "1.15.0"}