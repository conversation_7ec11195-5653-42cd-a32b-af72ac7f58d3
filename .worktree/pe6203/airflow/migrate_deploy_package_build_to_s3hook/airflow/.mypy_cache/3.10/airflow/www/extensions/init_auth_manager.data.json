{".class": "MypyFile", "_fullname": "airflow.www.extensions.init_auth_manager", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AirflowAppBuilder": {".class": "SymbolTableNode", "cross_ref": "airflow.www.extensions.init_appbuilder.AirflowAppBuilder", "kind": "Gdef"}, "AirflowConfigException": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.AirflowConfigException", "kind": "Gdef"}, "BaseAuthManager": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.base_auth_manager.BaseAuthManager", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.extensions.init_auth_manager.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.extensions.init_auth_manager.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.extensions.init_auth_manager.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.extensions.init_auth_manager.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.extensions.init_auth_manager.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.extensions.init_auth_manager.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "auth_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "airflow.www.extensions.init_auth_manager.auth_manager", "name": "auth_manager", "type": {".class": "UnionType", "items": ["airflow.auth.managers.base_auth_manager.BaseAuthManager", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "conf": {".class": "SymbolTableNode", "cross_ref": "airflow.configuration.conf", "kind": "Gdef"}, "get_auth_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_auth_manager.get_auth_manager", "name": "get_auth_manager", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_auth_manager", "ret_type": "airflow.auth.managers.base_auth_manager.BaseAuthManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_auth_manager_cls": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_auth_manager.get_auth_manager_cls", "name": "get_auth_manager_cls", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_auth_manager_cls", "ret_type": {".class": "TypeType", "item": "airflow.auth.managers.base_auth_manager.BaseAuthManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "init_auth_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["appbuilder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.extensions.init_auth_manager.init_auth_manager", "name": "init_auth_manager", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["appbuilder"], "arg_types": ["airflow.www.extensions.init_appbuilder.AirflowAppBuilder"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "init_auth_manager", "ret_type": "airflow.auth.managers.base_auth_manager.BaseAuthManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/www/extensions/init_auth_manager.py"}