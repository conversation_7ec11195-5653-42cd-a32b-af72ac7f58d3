{".class": "MypyFile", "_fullname": "airflow.utils.process_utils", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AirflowException": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.AirflowException", "kind": "Gdef"}, "DEFAULT_TIME_TO_WAIT_AFTER_SIGTERM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "airflow.utils.process_utils.DEFAULT_TIME_TO_WAIT_AFTER_SIGTERM", "name": "DEFAULT_TIME_TO_WAIT_AFTER_SIGTERM", "type": "builtins.int"}}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "IS_WINDOWS": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.platform.IS_WINDOWS", "kind": "Gdef"}, "PIDLockFile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.process_utils.PIDLockFile", "name": "PIDLockFile", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.process_utils.PIDLockFile", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.process_utils.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.process_utils.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.process_utils.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.process_utils.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.process_utils.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.process_utils.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "check_if_pidfile_process_is_running": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["pid_file", "process_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.process_utils.check_if_pidfile_process_is_running", "name": "check_if_pidfile_process_is_running", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["pid_file", "process_name"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_if_pidfile_process_is_running", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "conf": {".class": "SymbolTableNode", "cross_ref": "airflow.configuration.conf", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "errno": {".class": "SymbolTableNode", "cross_ref": "errno", "kind": "Gdef"}, "execute_in_subprocess": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["cmd", "cwd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.process_utils.execute_in_subprocess", "name": "execute_in_subprocess", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["cmd", "cwd"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute_in_subprocess", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute_in_subprocess_with_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["cmd", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.process_utils.execute_in_subprocess_with_kwargs", "name": "execute_in_subprocess_with_kwargs", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cmd", "kwargs"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute_in_subprocess_with_kwargs", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "execute_interactive": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["cmd", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.process_utils.execute_interactive", "name": "execute_interactive", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["cmd", "kwargs"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute_interactive", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "kill_child_processes_by_pids": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["pids_to_kill", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.process_utils.kill_child_processes_by_pids", "name": "kill_child_processes_by_pids", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["pids_to_kill", "timeout"], "arg_types": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "kill_child_processes_by_pids", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "airflow.utils.process_utils.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "patch_environ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["new_env_variables"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.utils.process_utils.patch_environ", "name": "patch_environ", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["new_env_variables"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "patch_environ", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "airflow.utils.process_utils.patch_environ", "name": "patch_environ", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["new_env_variables"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "patch_environ", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "psutil": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.process_utils.psutil", "name": "psutil", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.process_utils.psutil", "source_any": null, "type_of_any": 3}}}, "pty": {".class": "SymbolTableNode", "cross_ref": "pty", "kind": "Gdef"}, "reap_process_group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["process_group_id", "logger", "sig", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.process_utils.reap_process_group", "name": "reap_process_group", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["process_group_id", "logger", "sig", "timeout"], "arg_types": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "signal.Signals", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reap_process_group", "ret_type": {".class": "Instance", "args": ["builtins.int", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "select": {".class": "SymbolTableNode", "cross_ref": "select", "kind": "Gdef"}, "set_new_process_group": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.process_utils.set_new_process_group", "name": "set_new_process_group", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_new_process_group", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "shlex": {".class": "SymbolTableNode", "cross_ref": "shlex", "kind": "Gdef"}, "signal": {".class": "SymbolTableNode", "cross_ref": "signal", "kind": "Gdef"}, "subprocess": {".class": "SymbolTableNode", "cross_ref": "subprocess", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "termios": {".class": "SymbolTableNode", "cross_ref": "termios", "kind": "Gdef"}, "tty": {".class": "SymbolTableNode", "cross_ref": "tty", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/process_utils.py"}