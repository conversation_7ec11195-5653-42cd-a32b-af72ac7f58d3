{"data_mtime": **********, "dep_lines": [54, 55, 53, 43, 49, 56, 39, 48, 50, 52, 59, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 46], "dep_prios": [5, 5, 5, 25, 5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["airflow.providers.amazon.aws.hooks.base_aws", "airflow.providers.amazon.aws.utils.tags", "airflow.providers.amazon.aws.exceptions", "airflow.utils.types", "boto3.s3.transfer", "airflow.utils.helpers", "urllib.parse", "asgiref.sync", "botocore.exceptions", "airflow.exceptions", "mypy_boto3_s3.service_resource", "__future__", "asyncio", "fnmatch", "gzip", "logging", "os", "re", "shutil", "time", "warnings", "contextlib", "copy", "datetime", "functools", "inspect", "io", "pathlib", "tempfile", "typing", "uuid", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "airflow.hooks", "airflow.hooks.base", "airflow.utils", "airflow.utils.log", "airflow.utils.log.logging_mixin", "boto3", "boto3.resources", "boto3.resources.base", "boto3.s3", "botocore", "botocore.config", "mypy_boto3_s3", "s3transfer", "s3transfer.manager", "types"], "hash": "d9f1f29c0ca4f6b6cab0924b755e6ef437fc7673", "id": "airflow.providers.amazon.aws.hooks.s3", "ignore_all": true, "interface_hash": "4ebdd692817153cc2020324eb9c709ce43bfb84d", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/providers/amazon/aws/hooks/s3.py", "plugin_data": null, "size": 58869, "suppressed": ["aiobotocore.client"], "version_id": "1.15.0"}