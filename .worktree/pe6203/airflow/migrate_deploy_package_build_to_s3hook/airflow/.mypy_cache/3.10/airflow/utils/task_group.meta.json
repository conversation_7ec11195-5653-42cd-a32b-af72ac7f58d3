{"data_mtime": 1756971053, "dep_lines": [29, 36, 37, 38, 43, 44, 45, 46, 47, 49, 458, 483, 583, 30, 19, 21, 22, 23, 24, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 41, 27], "dep_prios": [5, 5, 5, 5, 20, 25, 20, 25, 25, 25, 20, 20, 20, 5, 5, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25, 10], "dependencies": ["airflow.compat.functools", "airflow.models.taskmixin", "airflow.serialization.enums", "airflow.utils.helpers", "airflow.models.abstractoperator", "airflow.models.baseoperator", "airflow.models.dag", "airflow.models.expandinput", "airflow.models.operator", "airflow.utils.edgemodifier", "airflow.serialization.serialized_objects", "airflow.operators.subdag", "airflow.models.xcom_arg", "airflow.exceptions", "__future__", "copy", "functools", "operator", "weakref", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "airflow.compat", "airflow.models", "airflow.models.mappedoperator", "airflow.serialization", "airflow.template", "airflow.template.templater", "airflow.utils.log", "airflow.utils.log.logging_mixin", "airflow.utils.mixins", "enum", "types", "typing_extensions"], "hash": "0c0ad594a333571db3feeea0a9448483ef192f6e", "id": "airflow.utils.task_group", "ignore_all": true, "interface_hash": "0e64c92bf52c7781ab2c5c9b0fc0c1b31bed4a3b", "mtime": 1743589731, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/task_group.py", "plugin_data": null, "size": 29920, "suppressed": ["sqlalchemy.orm", "re2"], "version_id": "1.15.0"}