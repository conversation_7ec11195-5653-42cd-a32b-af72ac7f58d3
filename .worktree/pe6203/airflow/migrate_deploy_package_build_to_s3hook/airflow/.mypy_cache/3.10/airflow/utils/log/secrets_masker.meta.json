{"data_mtime": 1756971053, "dep_lines": [44, 20, 43, 47, 49, 306, 18, 20, 21, 22, 23, 24, 25, 43, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 41], "dep_prios": [5, 10, 10, 25, 25, 20, 5, 20, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["airflow.compat.functools", "collections.abc", "airflow.settings", "kubernetes.client", "airflow.typing_compat", "airflow.configuration", "__future__", "collections", "logging", "sys", "enum", "functools", "typing", "airflow", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "airflow.compat", "kubernetes", "kubernetes.client.models", "kubernetes.client.models.v1_env_var", "re", "types", "typing_extensions"], "hash": "8b2b6b5fcdb71aa3f9c537afa18345b6dd448bc5", "id": "airflow.utils.log.secrets_masker", "ignore_all": true, "interface_hash": "24bfd925598224aadf93797b0c149da0979e486a", "mtime": 1743589731, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/log/secrets_masker.py", "plugin_data": null, "size": 14985, "suppressed": ["re2"], "version_id": "1.15.0"}