{".class": "MypyFile", "_fullname": "airflow.utils.log.logging_mixin", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ANSI_ESCAPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "airflow.utils.log.logging_mixin.ANSI_ESCAPE", "name": "ANSI_ESCAPE", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.log.logging_mixin.re2", "source_any": {".class": "AnyType", "missing_import_name": "airflow.utils.log.logging_mixin.re2", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ExternalLoggingMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_external_log_url", 1], ["log_name", 1], ["supports_external_link", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.log.logging_mixin.ExternalLoggingMixin", "name": "ExternalLoggingMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "airflow.utils.log.logging_mixin.ExternalLoggingMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.log.logging_mixin", "mro": ["airflow.utils.log.logging_mixin.ExternalLoggingMixin", "builtins.object"], "names": {".class": "SymbolTable", "get_external_log_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_instance", "try_number"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "airflow.utils.log.logging_mixin.ExternalLoggingMixin.get_external_log_url", "name": "get_external_log_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_instance", "try_number"], "arg_types": ["airflow.utils.log.logging_mixin.ExternalLoggingMixin", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_external_log_url of ExternalLoggingMixin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.logging_mixin.ExternalLoggingMixin.get_external_log_url", "name": "get_external_log_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "task_instance", "try_number"], "arg_types": ["airflow.utils.log.logging_mixin.ExternalLoggingMixin", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_external_log_url of ExternalLoggingMixin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "log_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "airflow.utils.log.logging_mixin.ExternalLoggingMixin.log_name", "name": "log_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.logging_mixin.ExternalLoggingMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log_name of ExternalLoggingMixin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.logging_mixin.ExternalLoggingMixin.log_name", "name": "log_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.logging_mixin.ExternalLoggingMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log_name of ExternalLoggingMixin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "supports_external_link": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "airflow.utils.log.logging_mixin.ExternalLoggingMixin.supports_external_link", "name": "supports_external_link", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.logging_mixin.ExternalLoggingMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_external_link of ExternalLoggingMixin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.logging_mixin.ExternalLoggingMixin.supports_external_link", "name": "supports_external_link", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.logging_mixin.ExternalLoggingMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_external_link of ExternalLoggingMixin", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin.ExternalLoggingMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.log.logging_mixin.ExternalLoggingMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Handler": {".class": "SymbolTableNode", "cross_ref": "logging.Handler", "kind": "Gdef"}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "IOBase": {".class": "SymbolTableNode", "cross_ref": "io.IOBase", "kind": "Gdef"}, "Logger": {".class": "SymbolTableNode", "cross_ref": "logging.Logger", "kind": "Gdef"}, "LoggingMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.log.logging_mixin.LoggingMixin", "name": "LoggingMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.log.logging_mixin", "mro": ["airflow.utils.log.logging_mixin.LoggingMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin.__init__", "name": "__init__", "type": null}}, "_create_logger_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["logged_class", "log_config_logger_name", "class_logger_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin._create_logger_name", "name": "_create_logger_name", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["logged_class", "log_config_logger_name", "class_logger_name"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin._T", "id": -1, "name": "_T", "namespace": "airflow.utils.log.logging_mixin.LoggingMixin._create_logger_name", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_logger_name of LoggingMixin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin._T", "id": -1, "name": "_T", "namespace": "airflow.utils.log.logging_mixin.LoggingMixin._create_logger_name", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin._create_logger_name", "name": "_create_logger_name", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["logged_class", "log_config_logger_name", "class_logger_name"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin._T", "id": -1, "name": "_T", "namespace": "airflow.utils.log.logging_mixin.LoggingMixin._create_logger_name", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_logger_name of LoggingMixin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin._T", "id": -1, "name": "_T", "namespace": "airflow.utils.log.logging_mixin.LoggingMixin._create_logger_name", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "_get_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "obj", "clazz"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin._get_log", "name": "_get_log", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "obj", "clazz"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.log.logging_mixin.LoggingMixin"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin._T", "id": -1, "name": "_T", "namespace": "airflow.utils.log.logging_mixin.LoggingMixin._get_log", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_log of LoggingMixin", "ret_type": "logging.Logger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin._T", "id": -1, "name": "_T", "namespace": "airflow.utils.log.logging_mixin.LoggingMixin._get_log", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin._get_log", "name": "_get_log", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "obj", "clazz"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.log.logging_mixin.LoggingMixin"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin._T", "id": -1, "name": "_T", "namespace": "airflow.utils.log.logging_mixin.LoggingMixin._get_log", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_log of LoggingMixin", "ret_type": "logging.Logger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin._T", "id": -1, "name": "_T", "namespace": "airflow.utils.log.logging_mixin.LoggingMixin._get_log", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin._log", "name": "_log", "type": {".class": "UnionType", "items": ["logging.Logger", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_log_config_logger_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin._log_config_logger_name", "name": "_log_config_logger_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_logger_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin._logger_name", "name": "_logger_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_set_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin._set_context", "name": "_set_context", "type": null}}, "log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin.log", "name": "log", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.logging_mixin.LoggingMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log of LoggingMixin", "ret_type": "logging.Logger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin.log", "name": "log", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.logging_mixin.LoggingMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log of LoggingMixin", "ret_type": "logging.Logger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "logger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin.logger", "name": "logger", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.log.logging_mixin.LoggingMixin"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logger of LoggingMixin", "ret_type": "logging.Logger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.logging_mixin.LoggingMixin.logger", "name": "logger", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.log.logging_mixin.LoggingMixin"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "logger of LoggingMixin", "ret_type": "logging.Logger", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin.LoggingMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.log.logging_mixin.LoggingMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RedirectStdHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "logging.StreamHandler"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.log.logging_mixin.RedirectStdHandler", "name": "RedirectStdHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.RedirectStdHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.log.logging_mixin", "mro": ["airflow.utils.log.logging_mixin.RedirectStdHandler", "logging.StreamHandler", "logging.Handler", "<PERSON>.Filterer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.RedirectStdHandler.__init__", "name": "__init__", "type": null}}, "_orig_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.log.logging_mixin.RedirectStdHandler._orig_stream", "name": "_orig_stream", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_use_stderr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.log.logging_mixin.RedirectStdHandler._use_stderr", "name": "_use_stderr", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.utils.log.logging_mixin.RedirectStdHandler.stream", "name": "stream", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.logging_mixin.RedirectStdHandler.stream", "name": "stream", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.logging_mixin.RedirectStdHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream of RedirectStdHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin.RedirectStdHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.log.logging_mixin.RedirectStdHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SetContextPropagate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.log.logging_mixin.SetContextPropagate", "name": "SetContextPropagate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "airflow.utils.log.logging_mixin.SetContextPropagate", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "airflow.utils.log.logging_mixin", "mro": ["airflow.utils.log.logging_mixin.SetContextPropagate", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "DISABLE_PROPAGATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.log.logging_mixin.SetContextPropagate.DISABLE_PROPAGATE", "name": "DISABLE_PROPAGATE", "type": "builtins.object"}}, "MAINTAIN_PROPAGATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.log.logging_mixin.SetContextPropagate.MAINTAIN_PROPAGATE", "name": "MAINTAIN_PROPAGATE", "type": "builtins.object"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin.SetContextPropagate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.log.logging_mixin.SetContextPropagate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamHandler": {".class": "SymbolTableNode", "cross_ref": "logging.StreamHandler", "kind": "Gdef"}, "StreamLogWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["io.IOBase", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.IO"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter", "name": "StreamLogWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "airflow.utils.log.logging_mixin", "mro": ["airflow.utils.log.logging_mixin.StreamLogWriter", "io.IOBase", "_io._IOBase", "typing.IO", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "logger", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter.__init__", "name": "__init__", "type": null}}, "_buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter._buffer", "name": "_buffer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_propagate_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter._propagate_log", "name": "_propagate_log", "type": null}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter.close", "name": "close", "type": null}}, "closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter.closed", "name": "closed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter.closed", "name": "closed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.logging_mixin.StreamLogWriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "closed of StreamLogWriter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter.encoding", "name": "encoding", "type": {".class": "NoneType"}}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter.flush", "name": "flush", "type": null}}, "isatty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter.isatty", "name": "isatty", "type": null}}, "level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter.level", "name": "level", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter.logger", "name": "logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter.write", "name": "write", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin.StreamLogWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.log.logging_mixin.StreamLogWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.logging_mixin._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.logging_mixin.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.logging_mixin.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.logging_mixin.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.__getattr__", "name": "__getattr__", "type": null}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.logging_mixin.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.logging_mixin.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.logging_mixin.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abc": {".class": "SymbolTableNode", "cross_ref": "abc", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "re2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.logging_mixin.re2", "name": "re2", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.log.logging_mixin.re2", "source_any": null, "type_of_any": 3}}}, "remove_escape_codes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.remove_escape_codes", "name": "remove_escape_codes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_escape_codes", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["logger", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.logging_mixin.set_context", "name": "set_context", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/log/logging_mixin.py"}