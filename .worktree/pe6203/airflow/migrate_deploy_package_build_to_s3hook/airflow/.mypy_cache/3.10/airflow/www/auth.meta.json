{"data_mtime": 1756971054, "dep_lines": [33, 48, 44, 47, 43, 55, 56, 41, 42, 54, 17, 19, 20, 21, 23, 25, 1, 1, 1, 1, 1, 1, 1, 26, 27], "dep_prios": [5, 25, 5, 25, 5, 25, 25, 5, 5, 25, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["airflow.auth.managers.models.resource_details", "airflow.auth.managers.models.batch_apis", "airflow.www.extensions.init_auth_manager", "airflow.auth.managers.base_auth_manager", "airflow.utils.net", "airflow.models.connection", "airflow.models.xcom", "airflow.configuration", "airflow.exceptions", "airflow.models", "__future__", "functools", "logging", "warnings", "typing", "flask", "builtins", "_frozen_importlib", "abc", "airflow.auth", "airflow.auth.managers", "airflow.auth.managers.models", "enum"], "hash": "d8e5b841c63a43e32d97753eff8d7eebfea211b3", "id": "airflow.www.auth", "ignore_all": true, "interface_hash": "07540797dc4d3f9c2e908dd233682f19bdcfe4ab", "mtime": 1743589731, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/www/auth.py", "plugin_data": null, "size": 13065, "suppressed": ["flask_appbuilder._compat", "flask_appbuilder.const"], "version_id": "1.15.0"}