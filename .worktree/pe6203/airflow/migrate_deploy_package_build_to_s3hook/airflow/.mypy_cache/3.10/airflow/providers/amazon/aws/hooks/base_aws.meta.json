{"data_mtime": **********, "dep_lines": [56, 57, 58, 900, 61, 62, 55, 60, 70, 40, 44, 45, 49, 50, 59, 67, 68, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 41, 43, 559, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 411, 317, 409, 46, 154, 212, 316, 42, 47, 337, 338, 1003], "dep_prios": [5, 5, 5, 20, 5, 5, 5, 5, 25, 10, 5, 5, 5, 5, 5, 25, 20, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20, 20, 5, 5, 10, 20, 10, 5, 20, 20, 20], "dependencies": ["airflow.providers.amazon.aws.utils.connection_wrapper", "airflow.providers.amazon.aws.utils.identifiers", "airflow.providers.amazon.aws.utils.suppress", "airflow.providers.amazon.aws.waiters.base_waiter", "airflow.utils.log.logging_mixin", "airflow.utils.log.secrets_masker", "airflow.hooks.base", "airflow.utils.helpers", "airflow.models.connection", "botocore.session", "botocore.config", "botocore.waiter", "airflow.configuration", "airflow.exceptions", "airflow.providers_manager", "botocore.client", "botocore.credentials", "__future__", "datetime", "inspect", "json", "logging", "os", "warnings", "copy", "functools", "pathlib", "typing", "boto3", "botocore", "jinja2", "tenacity", "airflow", "builtins", "_frozen_importlib", "_typeshed", "abc", "airflow.hooks", "airflow.models", "airflow.providers.amazon.aws.utils", "airflow.utils", "airflow.utils.log", "boto3.session", "types"], "hash": "8cc61b42fbcfbfa4e5b080f0ec74a11057322a1d", "id": "airflow.providers.amazon.aws.hooks.base_aws", "ignore_all": true, "interface_hash": "438cb88da78adc190dffab0dd7a29eaa7cb00a68", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/providers/amazon/aws/hooks/base_aws.py", "plugin_data": null, "size": 48826, "suppressed": ["airflow.providers.google.common.utils.id_token_credentials", "requests.packages.urllib3.util.retry", "google.auth.transport", "dateutil.tz", "aiobotocore.session", "aiobotocore.credentials", "requests.adapters", "requests", "slugify", "requests_gssapi", "lxml", "aiobotocore"], "version_id": "1.15.0"}