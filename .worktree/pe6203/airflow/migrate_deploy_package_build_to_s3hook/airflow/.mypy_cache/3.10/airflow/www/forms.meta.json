{"data_mtime": **********, "dep_lines": [39, 42, 43, 44, 45, 40, 41, 42, 18, 20, 21, 22, 23, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 26, 32, 36, 37, 33, 34, 35], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 20, 5, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5, 5, 5], "dependencies": ["airflow.compat.functools", "airflow.utils.timezone", "airflow.utils.types", "airflow.www.validators", "airflow.www.widgets", "airflow.configuration", "airflow.providers_manager", "airflow.utils", "__future__", "datetime", "json", "operator", "typing", "pendulum", "builtins", "_frozen_importlib", "abc", "airflow.compat", "enum", "json.decoder", "pendulum.date", "pendulum.datetime", "pendulum.duration", "pendulum.mixins", "pendulum.mixins.default", "pendulum.parser", "pendulum.time", "typing_extensions"], "hash": "20948bef8025348d23455e2d0bcc0f791faaabe5", "id": "airflow.www.forms", "ignore_all": true, "interface_hash": "0bb3021cb510dae7fa6317b6b48b66aae8d3105f", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/www/forms.py", "plugin_data": null, "size": 9702, "suppressed": ["flask_appbuilder.fieldwidgets", "flask_appbuilder.forms", "wtforms.fields", "wtforms.validators", "flask_babel", "flask_wtf", "wtforms"], "version_id": "1.15.0"}