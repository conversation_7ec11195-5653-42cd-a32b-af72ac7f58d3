{".class": "MypyFile", "_fullname": "airflow.www.security_manager", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ACTION_CAN_ACCESS_MENU": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.ACTION_CAN_ACCESS_MENU", "kind": "Gdef"}, "ACTION_CAN_READ": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.ACTION_CAN_READ", "kind": "Gdef"}, "AccessView": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.models.resource_details.AccessView", "kind": "Gdef"}, "Action": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.fab.models.Action", "kind": "Gdef"}, "AirflowException": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.AirflowException", "kind": "Gdef"}, "AirflowSecurityManagerV2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.utils.log.logging_mixin.LoggingMixin"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2", "name": "AirflowSecurityManagerV2", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.security_manager", "mro": ["airflow.www.security_manager.AirflowSecurityManagerV2", "airflow.utils.log.logging_mixin.LoggingMixin", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "appbuilder"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "appbuilder"], "arg_types": ["airflow.www.security_manager.AirflowSecurityManagerV2", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AirflowSecurityManagerV2", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_auth_manager_is_authorized_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2._auth_manager_is_authorized_map", "name": "_auth_manager_is_authorized_map", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.security_manager.AirflowSecurityManagerV2", {".class": "AnyType", "missing_import_name": "airflow.www.security_manager.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_auth_manager_is_authorized_map of AirflowSecurityManagerV2", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["airflow.auth.managers.models.base_user.BaseUser", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2._auth_manager_is_authorized_map", "name": "_auth_manager_is_authorized_map", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.security_manager.AirflowSecurityManagerV2", {".class": "AnyType", "missing_import_name": "airflow.www.security_manager.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_auth_manager_is_authorized_map of AirflowSecurityManagerV2", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["airflow.auth.managers.models.base_user.BaseUser", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_auth_manager_is_authorized_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "fab_resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2._get_auth_manager_is_authorized_method", "name": "_get_auth_manager_is_authorized_method", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "fab_resource_name"], "arg_types": ["airflow.www.security_manager.AirflowSecurityManagerV2", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_auth_manager_is_authorized_method of AirflowSecurityManagerV2", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_authorized_category_menu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "category"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2._is_authorized_category_menu", "name": "_is_authorized_category_menu", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "category"], "arg_types": ["airflow.www.security_manager.AirflowSecurityManagerV2", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_authorized_category_menu of AirflowSecurityManagerV2", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_limit_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "baseview"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.add_limit_view", "name": "add_limit_view", "type": null}}, "add_permissions_menu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.add_permissions_menu", "name": "add_permissions_menu", "type": null}}, "add_permissions_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "base_action_names", "resource_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.add_permissions_view", "name": "add_permissions_view", "type": null}}, "appbuilder": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.appbuilder", "name": "appbuilder", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "before_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.before_request", "name": "before_request", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.before_request", "name": "before_request", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_request of AirflowSecurityManagerV2", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_admin_standalone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.create_admin_standalone", "name": "create_admin_standalone", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.security_manager.AirflowSecurityManagerV2"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_admin_standalone of AirflowSecurityManagerV2", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_limiter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.create_limiter", "name": "create_limiter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.security_manager.AirflowSecurityManagerV2"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_limiter of AirflowSecurityManagerV2", "ret_type": "flask_limiter.extension.Limiter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_action": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.get_action", "name": "get_action", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["airflow.www.security_manager.AirflowSecurityManagerV2", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_action of AirflowSecurityManagerV2", "ret_type": "airflow.auth.managers.fab.models.Action", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resource": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.get_resource", "name": "get_resource", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["airflow.www.security_manager.AirflowSecurityManagerV2", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resource of AirflowSecurityManagerV2", "ret_type": "airflow.auth.managers.fab.models.Resource", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_access": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "action_name", "resource_name", "user", "resource_pk"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.has_access", "name": "has_access", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "action_name", "resource_name", "user", "resource_pk"], "arg_types": ["airflow.www.security_manager.AirflowSecurityManagerV2", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_access of AirflowSecurityManagerV2", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "limiter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.limiter", "name": "limiter", "type": "flask_limiter.extension.Limiter"}}, "register_views": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.register_views", "name": "register_views", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.security_manager.AirflowSecurityManagerV2.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.security_manager.AirflowSecurityManagerV2", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseUser": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.models.base_user.BaseUser", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "airflow.models.connection.Connection", "kind": "Gdef"}, "ConnectionDetails": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.models.resource_details.ConnectionDetails", "kind": "Gdef"}, "CustomSQLAInterface": {".class": "SymbolTableNode", "cross_ref": "airflow.www.utils.CustomSQLAInterface", "kind": "Gdef"}, "DagAccessEntity": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.models.resource_details.DagAccessEntity", "kind": "Gdef"}, "DagDetails": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.models.resource_details.DagDetails", "kind": "Gdef"}, "DagRun": {".class": "SymbolTableNode", "cross_ref": "airflow.models.dagrun.DagRun", "kind": "Gdef"}, "EXISTING_ROLES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "airflow.www.security_manager.EXISTING_ROLES", "name": "EXISTING_ROLES", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "FAB_EXISTING_ROLES": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.fab.security_manager.constants.EXISTING_ROLES", "kind": "Gdef"}, "Limiter": {".class": "SymbolTableNode", "cross_ref": "flask_limiter.extension.Limiter", "kind": "Gdef"}, "LoggingMixin": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.log.logging_mixin.LoggingMixin", "kind": "Gdef"}, "NEW_SESSION": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.session.NEW_SESSION", "kind": "Gdef"}, "Pool": {".class": "SymbolTableNode", "cross_ref": "airflow.models.pool.Pool", "kind": "Gdef"}, "PoolDetails": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.models.resource_details.PoolDetails", "kind": "Gdef"}, "RESOURCE_ADMIN_MENU": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_ADMIN_MENU", "kind": "Gdef"}, "RESOURCE_AUDIT_LOG": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_AUDIT_LOG", "kind": "Gdef"}, "RESOURCE_BROWSE_MENU": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_BROWSE_MENU", "kind": "Gdef"}, "RESOURCE_CLUSTER_ACTIVITY": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_CLUSTER_ACTIVITY", "kind": "Gdef"}, "RESOURCE_CONFIG": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_CONFIG", "kind": "Gdef"}, "RESOURCE_CONNECTION": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_CONNECTION", "kind": "Gdef"}, "RESOURCE_DAG": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_DAG", "kind": "Gdef"}, "RESOURCE_DAG_CODE": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_DAG_CODE", "kind": "Gdef"}, "RESOURCE_DAG_DEPENDENCIES": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_DAG_DEPENDENCIES", "kind": "Gdef"}, "RESOURCE_DAG_RUN": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_DAG_RUN", "kind": "Gdef"}, "RESOURCE_DATASET": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_DATASET", "kind": "Gdef"}, "RESOURCE_DOCS": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_DOCS", "kind": "Gdef"}, "RESOURCE_DOCS_MENU": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_DOCS_MENU", "kind": "Gdef"}, "RESOURCE_JOB": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_JOB", "kind": "Gdef"}, "RESOURCE_PLUGIN": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_PLUGIN", "kind": "Gdef"}, "RESOURCE_POOL": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_POOL", "kind": "Gdef"}, "RESOURCE_PROVIDER": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_PROVIDER", "kind": "Gdef"}, "RESOURCE_SLA_MISS": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_SLA_MISS", "kind": "Gdef"}, "RESOURCE_TASK_INSTANCE": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_TASK_INSTANCE", "kind": "Gdef"}, "RESOURCE_TASK_RESCHEDULE": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_TASK_RESCHEDULE", "kind": "Gdef"}, "RESOURCE_TRIGGER": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_TRIGGER", "kind": "Gdef"}, "RESOURCE_VARIABLE": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_VARIABLE", "kind": "Gdef"}, "RESOURCE_XCOM": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions.RESOURCE_XCOM", "kind": "Gdef"}, "Resource": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.fab.models.Resource", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.security_manager.Session", "name": "Session", "type": {".class": "AnyType", "missing_import_name": "airflow.www.security_manager.Session", "source_any": null, "type_of_any": 3}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TaskInstance": {".class": "SymbolTableNode", "cross_ref": "airflow.models.taskinstance.TaskInstance", "kind": "Gdef"}, "Variable": {".class": "SymbolTableNode", "cross_ref": "airflow.models.variable.Variable", "kind": "Gdef"}, "VariableDetails": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.models.resource_details.VariableDetails", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.security_manager.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.security_manager.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.security_manager.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.security_manager.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.security_manager.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.security_manager.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef"}, "g": {".class": "SymbolTableNode", "cross_ref": "flask.globals.g", "kind": "Gdef"}, "get_auth_manager": {".class": "SymbolTableNode", "cross_ref": "airflow.www.extensions.init_auth_manager.get_auth_manager", "kind": "Gdef"}, "get_method_from_fab_action_map": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.utils.fab.get_method_from_fab_action_map", "kind": "Gdef"}, "get_remote_address": {".class": "SymbolTableNode", "cross_ref": "flask_limiter.util.get_remote_address", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "provide_session": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.session.provide_session", "kind": "Gdef"}, "select": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.security_manager.select", "name": "select", "type": {".class": "AnyType", "missing_import_name": "airflow.www.security_manager.select", "source_any": null, "type_of_any": 3}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/www/security_manager.py"}