{".class": "MypyFile", "_fullname": "airflow.utils.setup_teardown", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractOperator": {".class": "SymbolTableNode", "cross_ref": "airflow.models.abstractoperator.AbstractOperator", "kind": "Gdef"}, "AirflowException": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.AirflowException", "kind": "Gdef"}, "BaseSetupTeardownContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext", "name": "BaseSetupTeardownContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.setup_teardown", "mro": ["airflow.utils.setup_teardown.BaseSetupTeardownContext", "builtins.object"], "names": {".class": "SymbolTable", "_context_managed_setup_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._context_managed_setup_task", "name": "_context_managed_setup_task", "type": {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}}}, "_context_managed_teardown_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._context_managed_teardown_task", "name": "_context_managed_teardown_task", "type": {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}}}, "_previous_context_managed_setup_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._previous_context_managed_setup_task", "name": "_previous_context_managed_setup_task", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_previous_context_managed_teardown_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._previous_context_managed_teardown_task", "name": "_previous_context_managed_teardown_task", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_previous_setup_upstream_of_teardown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._previous_setup_upstream_of_teardown", "name": "_previous_setup_upstream_of_teardown", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_previous_teardown_downstream_of_setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._previous_teardown_downstream_of_setup", "name": "_previous_teardown_downstream_of_setup", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_push_tasks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "operator", "setup"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._push_tasks", "name": "_push_tasks", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "operator", "setup"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_push_tasks of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._push_tasks", "name": "_push_tasks", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "operator", "setup"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_push_tasks of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_set_dependency": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["task", "receiving_task", "upstream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._set_dependency", "name": "_set_dependency", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._set_dependency", "name": "_set_dependency", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["task", "receiving_task", "upstream"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_dependency of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_setup_upstream_of_teardown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._setup_upstream_of_teardown", "name": "_setup_upstream_of_teardown", "type": {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}}}, "_teardown_downstream_of_setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._teardown_downstream_of_setup", "name": "_teardown_downstream_of_setup", "type": {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}}}, "_update_setup_upstream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "operator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._update_setup_upstream", "name": "_update_setup_upstream", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "operator"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_setup_upstream of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._update_setup_upstream", "name": "_update_setup_upstream", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "operator"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_setup_upstream of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_update_teardown_downstream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "operator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._update_teardown_downstream", "name": "_update_teardown_downstream", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "operator"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_teardown_downstream of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext._update_teardown_downstream", "name": "_update_teardown_downstream", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "operator"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_teardown_downstream of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.active", "name": "active", "type": "builtins.bool"}}, "context_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.context_map", "name": "context_map", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "TupleType", "implicit": false, "items": ["airflow.models.abstractoperator.AbstractOperator"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.error", "name": "error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "error of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.error", "name": "error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "message"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "error of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pop_context_managed_setup_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.pop_context_managed_setup_task", "name": "pop_context_managed_setup_task", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_context_managed_setup_task of BaseSetupTeardownContext", "ret_type": {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.pop_context_managed_setup_task", "name": "pop_context_managed_setup_task", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_context_managed_setup_task of BaseSetupTeardownContext", "ret_type": {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pop_context_managed_teardown_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.pop_context_managed_teardown_task", "name": "pop_context_managed_teardown_task", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_context_managed_teardown_task of BaseSetupTeardownContext", "ret_type": {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.pop_context_managed_teardown_task", "name": "pop_context_managed_teardown_task", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_context_managed_teardown_task of BaseSetupTeardownContext", "ret_type": {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pop_setup_upstream_of_teardown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.pop_setup_upstream_of_teardown", "name": "pop_setup_upstream_of_teardown", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_setup_upstream_of_teardown of BaseSetupTeardownContext", "ret_type": {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.pop_setup_upstream_of_teardown", "name": "pop_setup_upstream_of_teardown", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_setup_upstream_of_teardown of BaseSetupTeardownContext", "ret_type": {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "pop_teardown_downstream_of_setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.pop_teardown_downstream_of_setup", "name": "pop_teardown_downstream_of_setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_teardown_downstream_of_setup of BaseSetupTeardownContext", "ret_type": {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.pop_teardown_downstream_of_setup", "name": "pop_teardown_downstream_of_setup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_teardown_downstream_of_setup of BaseSetupTeardownContext", "ret_type": {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "push_context_managed_setup_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "task"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.push_context_managed_setup_task", "name": "push_context_managed_setup_task", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "task"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_context_managed_setup_task of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.push_context_managed_setup_task", "name": "push_context_managed_setup_task", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "task"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_context_managed_setup_task of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "push_context_managed_teardown_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "task"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.push_context_managed_teardown_task", "name": "push_context_managed_teardown_task", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "task"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_context_managed_teardown_task of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.push_context_managed_teardown_task", "name": "push_context_managed_teardown_task", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "task"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_context_managed_teardown_task of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "push_setup_teardown_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "operator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.push_setup_teardown_task", "name": "push_setup_teardown_task", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "operator"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_setup_teardown_task of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.push_setup_teardown_task", "name": "push_setup_teardown_task", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "operator"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_setup_teardown_task of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_dependency": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "receiving_task", "new_task", "upstream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.set_dependency", "name": "set_dependency", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "receiving_task", "new_task", "upstream"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_dependency of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.set_dependency", "name": "set_dependency", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["cls", "receiving_task", "new_task", "upstream"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", {".class": "Instance", "args": ["airflow.models.abstractoperator.AbstractOperator"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_dependency of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_setup_task_as_roots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "roots"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.set_setup_task_as_roots", "name": "set_setup_task_as_roots", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.set_setup_task_as_roots", "name": "set_setup_task_as_roots", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "roots"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_setup_task_as_roots of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_setup_teardown_relationships": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.set_setup_teardown_relationships", "name": "set_setup_teardown_relationships", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.set_setup_teardown_relationships", "name": "set_setup_teardown_relationships", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_setup_teardown_relationships of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_teardown_task_as_leaves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "leaves"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.set_teardown_task_as_leaves", "name": "set_teardown_task_as_leaves", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.set_teardown_task_as_leaves", "name": "set_teardown_task_as_leaves", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "leaves"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_teardown_task_as_leaves of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "set_work_task_roots_and_leaves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.set_work_task_roots_and_leaves", "name": "set_work_task_roots_and_leaves", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.set_work_task_roots_and_leaves", "name": "set_work_task_roots_and_leaves", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_work_task_roots_and_leaves of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update_context_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "task"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.update_context_map", "name": "update_context_map", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "task"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, "airflow.models.taskmixin.DependencyMixin"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_context_map of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.update_context_map", "name": "update_context_map", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "task"], "arg_types": [{".class": "TypeType", "item": "airflow.utils.setup_teardown.BaseSetupTeardownContext"}, "airflow.models.taskmixin.DependencyMixin"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "update_context_map of BaseSetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.setup_teardown.BaseSetupTeardownContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.setup_teardown.BaseSetupTeardownContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DependencyMixin": {".class": "SymbolTableNode", "cross_ref": "airflow.models.taskmixin.DependencyMixin", "kind": "Gdef"}, "PlainXComArg": {".class": "SymbolTableNode", "cross_ref": "airflow.models.xcom_arg.PlainXComArg", "kind": "Gdef"}, "SetupTeardownContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.utils.setup_teardown.BaseSetupTeardownContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.setup_teardown.SetupTeardownContext", "name": "SetupTeardownContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.utils.setup_teardown.SetupTeardownContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.setup_teardown", "mro": ["airflow.utils.setup_teardown.SetupTeardownContext", "airflow.utils.setup_teardown.BaseSetupTeardownContext", "builtins.object"], "names": {".class": "SymbolTable", "add_task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["task"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.utils.setup_teardown.SetupTeardownContext.add_task", "name": "add_task", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["task"], "arg_types": [{".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", "airflow.models.xcom_arg.PlainXComArg"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_task of SetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.setup_teardown.SetupTeardownContext.add_task", "name": "add_task", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["task"], "arg_types": [{".class": "UnionType", "items": ["airflow.models.abstractoperator.AbstractOperator", "airflow.models.xcom_arg.PlainXComArg"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_task of SetupTeardownContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.setup_teardown.SetupTeardownContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.setup_teardown.SetupTeardownContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.setup_teardown.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.setup_teardown.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.setup_teardown.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.setup_teardown.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.setup_teardown.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.setup_teardown.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/setup_teardown.py"}