{"data_mtime": 1756971053, "dep_lines": [28, 29, 22, 25, 19, 21, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 10, 5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["airflow.models.operator", "airflow.utils.context", "multiprocessing.context", "airflow.configuration", "__future__", "multiprocessing", "typing", "builtins", "_frozen_importlib", "abc", "airflow.models", "airflow.models.abstractoperator", "airflow.models.baseoperator", "airflow.models.dag", "airflow.models.dagrun", "airflow.models.dataset", "airflow.models.mappedoperator", "airflow.models.param", "airflow.models.taskinstance", "airflow.models.taskmixin", "airflow.serialization", "airflow.serialization.pydantic", "airflow.serialization.pydantic.dag_run", "airflow.serialization.pydantic.dataset", "airflow.serialization.pydantic.taskinstance", "airflow.template", "airflow.template.templater", "airflow.utils.log", "airflow.utils.log.logging_mixin", "configparser", "datetime", "pendulum", "pendulum.date", "pendulum.datetime", "pendulum.mixins", "pendulum.mixins.default", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "4007487c15e44646b5c92606bd6a6cb5c62b6b6c", "id": "airflow.utils.mixins", "ignore_all": true, "interface_hash": "3a286edb70c1b8f03ca8497a7b0a1c564a5a2642", "mtime": 1743589731, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/mixins.py", "plugin_data": null, "size": 2424, "suppressed": [], "version_id": "1.15.0"}