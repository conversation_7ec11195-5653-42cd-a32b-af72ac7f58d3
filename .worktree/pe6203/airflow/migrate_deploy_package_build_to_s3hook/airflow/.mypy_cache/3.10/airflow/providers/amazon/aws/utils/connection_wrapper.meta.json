{"data_mtime": **********, "dep_lines": [30, 31, 32, 33, 36, 27, 29, 17, 19, 20, 21, 22, 23, 24, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["airflow.providers.amazon.aws.utils", "airflow.utils.log.logging_mixin", "airflow.utils.log.secrets_masker", "airflow.utils.types", "airflow.models.connection", "botocore.config", "airflow.exceptions", "__future__", "json", "warnings", "copy", "dataclasses", "functools", "typing", "botocore", "builtins", "_frozen_importlib", "_typeshed", "abc", "airflow.models", "airflow.utils", "airflow.utils.log", "enum", "logging", "types"], "hash": "6ecfdc87cb912b53279dc9729343202cd3b73e14", "id": "airflow.providers.amazon.aws.utils.connection_wrapper", "ignore_all": true, "interface_hash": "c67141b84cb1390db17842e72b6ef38c9a074b04", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/providers/amazon/aws/utils/connection_wrapper.py", "plugin_data": null, "size": 22616, "suppressed": [], "version_id": "1.15.0"}