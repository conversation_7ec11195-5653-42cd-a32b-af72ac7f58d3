{"data_mtime": 1756971054, "dep_lines": [43, 242, 41, 42, 19, 21, 22, 23, 25, 41, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 27, 48, 28, 37, 38, 39, 49, 240, 26], "dep_prios": [5, 20, 10, 5, 5, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 25, 5, 5, 5, 5, 25, 20, 5], "dependencies": ["airflow.www.extensions.init_auth_manager", "airflow.www.views", "airflow.settings", "airflow.configuration", "__future__", "logging", "functools", "typing", "flask", "airflow", "builtins", "_frozen_importlib", "abc", "airflow.auth", "airflow.auth.managers", "airflow.auth.managers.base_auth_manager", "airflow.utils", "airflow.utils.log", "airflow.utils.log.logging_mixin", "configparser", "flask.app", "flask.scaffold"], "hash": "d100307cf851fab763ef7955e476c45398a155b9", "id": "airflow.www.extensions.init_appbuilder", "ignore_all": true, "interface_hash": "d75c0f802a1cfab2f93e9371f33a92245e37c5fb", "mtime": 1743589731, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/www/extensions/init_appbuilder.py", "plugin_data": null, "size": 24258, "suppressed": ["flask_appbuilder.babel.manager", "flask_appbuilder.security.manager", "flask_appbuilder.const", "flask_appbuilder.filters", "flask_appbuilder.menu", "flask_appbuilder.views", "sqlalchemy.orm", "flask_appbuilder.security", "flask_appbuilder"], "version_id": "1.15.0"}