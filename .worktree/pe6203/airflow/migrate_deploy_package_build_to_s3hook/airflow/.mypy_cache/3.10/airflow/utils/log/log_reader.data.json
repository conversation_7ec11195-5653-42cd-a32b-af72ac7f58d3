{".class": "MypyFile", "_fullname": "airflow.utils.log.log_reader", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ExternalLoggingMixin": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.log.logging_mixin.ExternalLoggingMixin", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "NEW_SESSION": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.session.NEW_SESSION", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.log_reader.Session", "name": "Session", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.log.log_reader.Session", "source_any": null, "type_of_any": 3}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TaskInstance": {".class": "SymbolTableNode", "cross_ref": "airflow.models.taskinstance.TaskInstance", "kind": "Gdef"}, "TaskInstanceState": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.state.TaskInstanceState", "kind": "Gdef"}, "TaskLogReader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.log.log_reader.TaskLogReader", "name": "TaskLogReader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.utils.log.log_reader.TaskLogReader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.log.log_reader", "mro": ["airflow.utils.log.log_reader.TaskLogReader", "builtins.object"], "names": {".class": "SymbolTable", "STREAM_LOOP_SLEEP_SECONDS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.log.log_reader.TaskLogReader.STREAM_LOOP_SLEEP_SECONDS", "name": "STREAM_LOOP_SLEEP_SECONDS", "type": "builtins.int"}}, "log_handler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.utils.log.log_reader.TaskLogReader.log_handler", "name": "log_handler", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.log_reader.TaskLogReader.log_handler", "name": "log_handler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.log_reader.TaskLogReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log_handler of TaskLogReader", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "read_log_chunks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ti", "try_number", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.log_reader.TaskLogReader.read_log_chunks", "name": "read_log_chunks", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ti", "try_number", "metadata"], "arg_types": ["airflow.utils.log.log_reader.TaskLogReader", "airflow.models.taskinstance.TaskInstance", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_log_chunks of TaskLogReader", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_log_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ti", "try_number", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.log_reader.TaskLogReader.read_log_stream", "name": "read_log_stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "ti", "try_number", "metadata"], "arg_types": ["airflow.utils.log.log_reader.TaskLogReader", "airflow.models.taskinstance.TaskInstance", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_log_stream of TaskLogReader", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_log_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "ti", "try_number", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.utils.log.log_reader.TaskLogReader.render_log_filename", "name": "render_log_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "ti", "try_number", "session"], "arg_types": ["airflow.utils.log.log_reader.TaskLogReader", "airflow.models.taskinstance.TaskInstance", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": "airflow.utils.log.log_reader.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_log_filename of TaskLogReader", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.log_reader.TaskLogReader.render_log_filename", "name": "render_log_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "ti", "try_number", "session"], "arg_types": ["airflow.utils.log.log_reader.TaskLogReader", "airflow.models.taskinstance.TaskInstance", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": "airflow.utils.log.log_reader.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_log_filename of TaskLogReader", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "supports_external_link": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.utils.log.log_reader.TaskLogReader.supports_external_link", "name": "supports_external_link", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.log_reader.TaskLogReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_external_link of TaskLogReader", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.log_reader.TaskLogReader.supports_external_link", "name": "supports_external_link", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.log_reader.TaskLogReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_external_link of TaskLogReader", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "supports_read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.utils.log.log_reader.TaskLogReader.supports_read", "name": "supports_read", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.log_reader.TaskLogReader.supports_read", "name": "supports_read", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.log_reader.TaskLogReader"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_read of TaskLogReader", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.log_reader.TaskLogReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.log.log_reader.TaskLogReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.log_reader.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.log_reader.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.log_reader.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.log_reader.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.log_reader.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.log_reader.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef"}, "conf": {".class": "SymbolTableNode", "cross_ref": "airflow.configuration.conf", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "provide_session": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.session.provide_session", "kind": "Gdef"}, "render_log_filename": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.helpers.render_log_filename", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/log/log_reader.py"}