{".class": "MypyFile", "_fullname": "airflow.utils.mixins", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Context": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.context.Context", "kind": "Gdef"}, "MultiprocessingStartMethodMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.mixins.MultiprocessingStartMethodMixin", "name": "MultiprocessingStartMethodMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.utils.mixins.MultiprocessingStartMethodMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.mixins", "mro": ["airflow.utils.mixins.MultiprocessingStartMethodMixin", "builtins.object"], "names": {".class": "SymbolTable", "_get_multiprocessing_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.mixins.MultiprocessingStartMethodMixin._get_multiprocessing_context", "name": "_get_multiprocessing_context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.mixins.MultiprocessingStartMethodMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_multiprocessing_context of MultiprocessingStartMethodMixin", "ret_type": "multiprocessing.context.DefaultContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_multiprocessing_start_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.mixins.MultiprocessingStartMethodMixin._get_multiprocessing_start_method", "name": "_get_multiprocessing_start_method", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.mixins.MultiprocessingStartMethodMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_multiprocessing_start_method of MultiprocessingStartMethodMixin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.mixins.MultiprocessingStartMethodMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.mixins.MultiprocessingStartMethodMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Operator": {".class": "SymbolTableNode", "cross_ref": "airflow.models.operator.Operator", "kind": "Gdef"}, "ResolveMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.mixins.ResolveMixin", "name": "ResolveMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.utils.mixins.ResolveMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.mixins", "mro": ["airflow.utils.mixins.ResolveMixin", "builtins.object"], "names": {".class": "SymbolTable", "iter_references": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.mixins.ResolveMixin.iter_references", "name": "iter_references", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.mixins.ResolveMixin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_references of ResolveMixin", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "airflow.models.operator.Operator"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "resolve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.mixins.ResolveMixin.resolve", "name": "resolve", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["airflow.utils.mixins.ResolveMixin", {".class": "TypeAliasType", "args": [], "type_ref": "airflow.utils.context.Context"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve of ResolveMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.mixins.ResolveMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.mixins.ResolveMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.mixins.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.mixins.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.mixins.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.mixins.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.mixins.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.mixins.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "conf": {".class": "SymbolTableNode", "cross_ref": "airflow.configuration.conf", "kind": "Gdef"}, "multiprocessing": {".class": "SymbolTableNode", "cross_ref": "multiprocessing", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/mixins.py"}