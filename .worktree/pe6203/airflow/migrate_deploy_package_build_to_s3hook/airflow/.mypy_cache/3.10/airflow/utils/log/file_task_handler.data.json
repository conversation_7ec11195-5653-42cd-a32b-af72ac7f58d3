{".class": "MypyFile", "_fullname": "airflow.utils.log.file_task_handler", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AirflowException": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.AirflowException", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Context": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.context.Context", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "ExecutorLoader": {".class": "SymbolTableNode", "cross_ref": "airflow.executors.executor_loader.ExecutorLoader", "kind": "Gdef"}, "FileTaskHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["logging.Handler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler", "name": "FileTaskHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.log.file_task_handler", "mro": ["airflow.utils.log.file_task_handler.FileTaskHandler", "logging.Handler", "<PERSON>.Filterer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "base_log_folder", "filename_template"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "base_log_folder", "filename_template"], "arg_types": ["airflow.utils.log.file_task_handler.FileTaskHandler", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FileTaskHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_executor_get_task_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._executor_get_task_log", "name": "_executor_get_task_log", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.file_task_handler.FileTaskHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_executor_get_task_log of FileTaskHandler", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["airflow.models.taskinstance.TaskInstance", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._executor_get_task_log", "name": "_executor_get_task_log", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.file_task_handler.FileTaskHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_executor_get_task_log of FileTaskHandler", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["airflow.models.taskinstance.TaskInstance", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_get_log_retrieval_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "ti", "log_relative_path", "log_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._get_log_retrieval_url", "name": "_get_log_retrieval_url", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "ti", "log_relative_path", "log_type"], "arg_types": ["airflow.utils.log.file_task_handler.FileTaskHandler", "airflow.models.taskinstance.TaskInstance", "builtins.str", {".class": "UnionType", "items": ["airflow.utils.log.file_task_handler.LogType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_log_retrieval_url of FileTaskHandler", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_pod_namespace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ti"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._get_pod_namespace", "name": "_get_pod_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ti"], "arg_types": ["airflow.models.taskinstance.TaskInstance"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_pod_namespace of FileTaskHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._get_pod_namespace", "name": "_get_pod_namespace", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ti"], "arg_types": ["airflow.models.taskinstance.TaskInstance"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_pod_namespace of FileTaskHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_init_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "ti", "identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._init_file", "name": "_init_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "ti", "identifier"], "arg_types": ["airflow.utils.log.file_task_handler.FileTaskHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_file of FileTaskHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepare_log_folder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "directory"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._prepare_log_folder", "name": "_prepare_log_folder", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "directory"], "arg_types": ["airflow.utils.log.file_task_handler.FileTaskHandler", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_log_folder of FileTaskHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "ti", "try_number", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._read", "name": "_read", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "ti", "try_number", "metadata"], "arg_types": ["airflow.utils.log.file_task_handler.FileTaskHandler", "airflow.models.taskinstance.TaskInstance", "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read of FileTaskHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_read_from_local": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["worker_log_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._read_from_local", "name": "_read_from_local", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["worker_log_path"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_from_local of FileTaskHandler", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._read_from_local", "name": "_read_from_local", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["worker_log_path"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_from_local of FileTaskHandler", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_read_from_logs_server": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ti", "worker_log_rel_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._read_from_logs_server", "name": "_read_from_logs_server", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ti", "worker_log_rel_path"], "arg_types": ["airflow.utils.log.file_task_handler.FileTaskHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_from_logs_server of FileTaskHandler", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_read_grouped_logs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._read_grouped_logs", "name": "_read_grouped_logs", "type": null}}, "_read_remote_logs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "ti", "try_number", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._read_remote_logs", "name": "_read_remote_logs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "ti", "try_number", "metadata"], "arg_types": ["airflow.utils.log.file_task_handler.FileTaskHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_remote_logs of FileTaskHandler", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ti", "try_number"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler._render_filename", "name": "_render_filename", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ti", "try_number"], "arg_types": ["airflow.utils.log.file_task_handler.FileTaskHandler", {".class": "UnionType", "items": ["airflow.models.taskinstance.TaskInstance", {".class": "TypeAliasType", "args": [], "type_ref": "airflow.models.taskinstancekey.TaskInstanceKey"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_filename of FileTaskHandler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_triggerer_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["full_path", "job_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.add_triggerer_suffix", "name": "add_triggerer_suffix", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.add_triggerer_suffix", "name": "add_triggerer_suffix", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["full_path", "job_id"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_triggerer_suffix of FileTaskHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.close", "name": "close", "type": null}}, "ctx_task_deferred": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.ctx_task_deferred", "name": "ctx_task_deferred", "type": "builtins.bool"}}, "emit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.emit", "name": "emit", "type": null}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.flush", "name": "flush", "type": null}}, "handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.handler", "name": "handler", "type": {".class": "UnionType", "items": ["logging.Handler", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "inherits_from_empty_operator_log_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.inherits_from_empty_operator_log_message", "name": "inherits_from_empty_operator_log_message", "type": "builtins.str"}}, "local_base": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.local_base", "name": "local_base", "type": "builtins.str"}}, "maintain_propagate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.maintain_propagate", "name": "maintain_propagate", "type": "builtins.bool"}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "task_instance", "try_number", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.read", "name": "read", "type": null}}, "set_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "ti", "identifier"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.set_context", "name": "set_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "ti", "identifier"], "arg_types": ["airflow.utils.log.file_task_handler.FileTaskHandler", "airflow.models.taskinstance.TaskInstance", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_context of FileTaskHandler", "ret_type": {".class": "UnionType", "items": [{".class": "NoneType"}, "airflow.utils.log.logging_mixin.SetContextPropagate"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_task_context_logging": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.supports_task_context_logging", "name": "supports_task_context_logging", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.file_task_handler.FileTaskHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_task_context_logging of FileTaskHandler", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.supports_task_context_logging", "name": "supports_task_context_logging", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.utils.log.file_task_handler.FileTaskHandler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_task_context_logging of FileTaskHandler", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "trigger_should_wrap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.trigger_should_wrap", "name": "trigger_should_wrap", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.file_task_handler.FileTaskHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.log.file_task_handler.FileTaskHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LogType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str", "enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.log.file_task_handler.LogType", "name": "LogType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "airflow.utils.log.file_task_handler.LogType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "airflow.utils.log.file_task_handler", "mro": ["airflow.utils.log.file_task_handler.LogType", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "TRIGGER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.log.file_task_handler.LogType.TRIGGER", "name": "TRIGGER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "trigger"}, "type_ref": "builtins.str"}}}, "WORKER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.log.file_task_handler.LogType.WORKER", "name": "WORKER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "worker"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.log.file_task_handler.LogType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.log.file_task_handler.LogType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NonCachingFileHandler": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.log.non_caching_file_handler.NonCachingFileHandler", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "RemovedInAirflow3Warning": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.RemovedInAirflow3Warning", "kind": "Gdef"}, "SetContextPropagate": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.log.logging_mixin.SetContextPropagate", "kind": "Gdef"}, "State": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.state.State", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TaskInstance": {".class": "SymbolTableNode", "cross_ref": "airflow.models.taskinstance.TaskInstance", "kind": "Gdef"}, "TaskInstanceKey": {".class": "SymbolTableNode", "cross_ref": "airflow.models.taskinstancekey.TaskInstanceKey", "kind": "Gdef"}, "TaskInstanceState": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.state.TaskInstanceState", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.file_task_handler.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.file_task_handler.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.file_task_handler.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.file_task_handler.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.file_task_handler.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.log.file_task_handler.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_ensure_ti": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["ti", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler._ensure_ti", "name": "_ensure_ti", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["ti", "session"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "airflow.models.taskinstancekey.TaskInstanceKey"}, "airflow.models.taskinstance.TaskInstance"], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ensure_ti", "ret_type": "airflow.models.taskinstance.TaskInstance", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fetch_logs_from_service": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["url", "log_relative_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler._fetch_logs_from_service", "name": "_fetch_logs_from_service", "type": null}}, "_interleave_logs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["logs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler._interleave_logs", "name": "_interleave_logs", "type": null}}, "_parse_timestamp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "airflow.utils.log.file_task_handler._parse_timestamp", "name": "_parse_timestamp", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_parse_timestamps_in_log_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler._parse_timestamps_in_log_file", "name": "_parse_timestamps_in_log_file", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lines"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_timestamps_in_log_file", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_task_deferred_context_var": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.log.file_task_handler._set_task_deferred_context_var", "name": "_set_task_deferred_context_var", "type": null}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef"}, "conf": {".class": "SymbolTableNode", "cross_ref": "airflow.configuration.conf", "kind": "Gdef"}, "create_session": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.session.create_session", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "airflow.utils.log.file_task_handler.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_template_string": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.helpers.parse_template_string", "kind": "Gdef"}, "pendulum": {".class": "SymbolTableNode", "cross_ref": "pendulum", "kind": "Gdef"}, "render_template_to_string": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.helpers.render_template_to_string", "kind": "Gdef"}, "suppress": {".class": "SymbolTableNode", "cross_ref": "contextlib.suppress", "kind": "Gdef"}, "urljoin": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urljoin", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/log/file_task_handler.py"}