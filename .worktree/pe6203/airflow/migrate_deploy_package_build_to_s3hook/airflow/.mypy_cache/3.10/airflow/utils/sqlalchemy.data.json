{".class": "MypyFile", "_fullname": "airflow.utils.sqlalchemy", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.ColumnElement", "name": "ColumnElement", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}}}, "ColumnOperators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.ColumnOperators", "name": "ColumnOperators", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}}}, "CommitProhibitorGuard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.sqlalchemy.CommitProhibitorGuard", "name": "CommitProhibitorGuard", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.CommitProhibitorGuard", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.sqlalchemy", "mro": ["airflow.utils.sqlalchemy.CommitProhibitorGuard", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.CommitProhibitorGuard.__enter__", "name": "__enter__", "type": null}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.CommitProhibitorGuard.__exit__", "name": "__exit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.CommitProhibitorGuard.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "session"], "arg_types": ["airflow.utils.sqlalchemy.CommitProhibitorGuard", {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CommitProhibitorGuard", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.CommitProhibitorGuard._validate_commit", "name": "_validate_commit", "type": null}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.CommitProhibitorGuard.commit", "name": "commit", "type": null}}, "expected_commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.sqlalchemy.CommitProhibitorGuard.expected_commit", "name": "expected_commit", "type": "builtins.bool"}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.utils.sqlalchemy.CommitProhibitorGuard.session", "name": "session", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.sqlalchemy.CommitProhibitorGuard.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.sqlalchemy.CommitProhibitorGuard", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Encoding": {".class": "SymbolTableNode", "cross_ref": "airflow.serialization.enums.Encoding", "kind": "Gdef"}, "ExecutorConfigType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.sqlalchemy.ExecutorConfigType", "name": "ExecutorConfigType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.utils.sqlalchemy.ExecutorConfigType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.sqlalchemy", "mro": ["airflow.utils.sqlalchemy.ExecutorConfigType", "builtins.object"], "names": {".class": "SymbolTable", "bind_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.ExecutorConfigType.bind_processor", "name": "bind_processor", "type": null}}, "cache_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.sqlalchemy.ExecutorConfigType.cache_ok", "name": "cache_ok", "type": "builtins.bool"}}, "compare_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "x", "y"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.ExecutorConfigType.compare_values", "name": "compare_values", "type": null}}, "result_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "coltype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.ExecutorConfigType.result_processor", "name": "result_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.sqlalchemy.ExecutorConfigType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.sqlalchemy.ExecutorConfigType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExtendedJSON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.sqlalchemy.ExtendedJSON", "name": "ExtendedJSON", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.utils.sqlalchemy.ExtendedJSON", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.sqlalchemy", "mro": ["airflow.utils.sqlalchemy.ExtendedJSON", "builtins.object"], "names": {".class": "SymbolTable", "cache_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.sqlalchemy.ExtendedJSON.cache_ok", "name": "cache_ok", "type": "builtins.bool"}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.sqlalchemy.ExtendedJSON.impl", "name": "impl", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Text", "source_any": null, "type_of_any": 3}}}, "load_dialect_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.ExtendedJSON.load_dialect_impl", "name": "load_dialect_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "arg_types": ["airflow.utils.sqlalchemy.ExtendedJSON", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_dialect_impl of ExtendedJSON", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.TypeEngine", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_bind_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.ExtendedJSON.process_bind_param", "name": "process_bind_param", "type": null}}, "process_result_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.ExtendedJSON.process_result_value", "name": "process_result_value", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.sqlalchemy.ExtendedJSON.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.sqlalchemy.ExtendedJSON", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "Interval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.sqlalchemy.Interval", "name": "Interval", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.utils.sqlalchemy.Interval", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.sqlalchemy", "mro": ["airflow.utils.sqlalchemy.Interval", "builtins.object"], "names": {".class": "SymbolTable", "attr_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.sqlalchemy.Interval.attr_keys", "name": "attr_keys", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.relativedelta", "source_any": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.relativedelta", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "cache_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.sqlalchemy.Interval.cache_ok", "name": "cache_ok", "type": "builtins.bool"}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.sqlalchemy.Interval.impl", "name": "impl", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Text", "source_any": null, "type_of_any": 3}}}, "process_bind_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.Interval.process_bind_param", "name": "process_bind_param", "type": null}}, "process_result_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.Interval.process_result_value", "name": "process_result_value", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.sqlalchemy.Interval.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.sqlalchemy.Interval", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "JSON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.JSON", "name": "JSON", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.JSON", "source_any": null, "type_of_any": 3}}}, "OperationalError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.OperationalError", "name": "OperationalError", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.OperationalError", "source_any": null, "type_of_any": 3}}}, "PickleType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.PickleType", "name": "PickleType", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.PickleType", "source_any": null, "type_of_any": 3}}}, "Query": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.Query", "name": "Query", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Query", "source_any": null, "type_of_any": 3}}}, "Select": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.Select", "name": "Select", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Select", "source_any": null, "type_of_any": 3}}}, "Session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.Session", "name": "Session", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}}}, "TIMESTAMP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.TIMESTAMP", "name": "TIMESTAMP", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.TIMESTAMP", "source_any": null, "type_of_any": 3}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.Text", "name": "Text", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Text", "source_any": null, "type_of_any": 3}}}, "TypeDecorator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.TypeDecorator", "name": "TypeDecorator", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.TypeDecorator", "source_any": null, "type_of_any": 3}}}, "TypeEngine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.TypeEngine", "name": "TypeEngine", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.TypeEngine", "source_any": null, "type_of_any": 3}}}, "USE_ROW_LEVEL_LOCKING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "airflow.utils.sqlalchemy.USE_ROW_LEVEL_LOCKING", "name": "USE_ROW_LEVEL_LOCKING", "type": "builtins.bool"}}, "UnicodeText": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.UnicodeText", "name": "UnicodeText", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.UnicodeText", "source_any": null, "type_of_any": 3}}}, "UtcDateTime": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.utils.sqlalchemy.UtcDateTime", "name": "UtcDateTime", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.utils.sqlalchemy.UtcDateTime", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.utils.sqlalchemy", "mro": ["airflow.utils.sqlalchemy.UtcDateTime", "builtins.object"], "names": {".class": "SymbolTable", "cache_ok": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.sqlalchemy.UtcDateTime.cache_ok", "name": "cache_ok", "type": "builtins.bool"}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.utils.sqlalchemy.UtcDateTime.impl", "name": "impl", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.TIMESTAMP", "source_any": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.TIMESTAMP", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "load_dialect_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.UtcDateTime.load_dialect_impl", "name": "load_dialect_impl", "type": null}}, "process_bind_param": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.UtcDateTime.process_bind_param", "name": "process_bind_param", "type": null}}, "process_result_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.UtcDateTime.process_result_value", "name": "process_result_value", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.utils.sqlalchemy.UtcDateTime.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.utils.sqlalchemy.UtcDateTime", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "V1Pod": {".class": "SymbolTableNode", "cross_ref": "kubernetes.client.models.v1_pod.V1Pod", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.sqlalchemy.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.sqlalchemy.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.sqlalchemy.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.sqlalchemy.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.sqlalchemy.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.utils.sqlalchemy.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "and_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.and_", "name": "and_", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.and_", "source_any": null, "type_of_any": 3}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "conf": {".class": "SymbolTableNode", "cross_ref": "airflow.configuration.conf", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "ensure_pod_is_valid_after_unpickling": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pod"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.ensure_pod_is_valid_after_unpickling", "name": "ensure_pod_is_valid_after_unpickling", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pod"], "arg_types": ["kubernetes.client.models.v1_pod.V1Pod"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ensure_pod_is_valid_after_unpickling", "ret_type": {".class": "UnionType", "items": ["kubernetes.client.models.v1_pod.V1Pod", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "event": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.event", "name": "event", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.event", "source_any": null, "type_of_any": 3}}}, "false": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.false", "name": "false", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.false", "source_any": null, "type_of_any": 3}}}, "is_lock_not_available_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.is_lock_not_available_error", "name": "is_lock_not_available_error", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["error"], "arg_types": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.OperationalError", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_lock_not_available_error", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "lock_rows": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["query", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.utils.sqlalchemy.lock_rows", "name": "lock_rows", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["query", "session"], "arg_types": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Query", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lock_rows", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.lock_rows", "name": "lock_rows", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["query", "session"], "arg_types": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Query", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lock_rows", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "airflow.utils.sqlalchemy.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "make_naive": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.timezone.make_naive", "kind": "Gdef"}, "mssql": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.mssql", "name": "mssql", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.mssql", "source_any": null, "type_of_any": 3}}}, "mysql": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.mysql", "name": "mysql", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.mysql", "source_any": null, "type_of_any": 3}}}, "nowait": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.nowait", "name": "nowait", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nowait", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nulls_first": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["col", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.nulls_first", "name": "nulls_first", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["col", "session"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nulls_first", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nullsfirst": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.nullsfirst", "name": "nullsfirst", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.nullsfirst", "source_any": null, "type_of_any": 3}}}, "or_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.or_", "name": "or_", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.or_", "source_any": null, "type_of_any": 3}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "prohibit_commit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.prohibit_commit", "name": "prohibit_commit", "type": null}}, "relativedelta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.relativedelta", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.relativedelta", "source_any": null, "type_of_any": 3}}}, "sanitize_for_serialization": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.sanitize_for_serialization", "name": "sanitize_for_serialization", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": ["kubernetes.client.models.v1_pod.V1Pod"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sanitize_for_serialization", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "settings": {".class": "SymbolTableNode", "cross_ref": "airflow.settings", "kind": "Gdef"}, "skip_locked": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.skip_locked", "name": "skip_locked", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["session"], "arg_types": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skip_locked", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "true": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.true", "name": "true", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.true", "source_any": null, "type_of_any": 3}}}, "tuple_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.tuple_", "name": "tuple_", "type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.tuple_", "source_any": null, "type_of_any": 3}}}, "tuple_in_condition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.tuple_in_condition", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["columns", "collection", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "airflow.utils.sqlalchemy.tuple_in_condition", "name": "tuple_in_condition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["columns", "collection", "session"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Select", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["columns", "collection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "airflow.utils.sqlalchemy.tuple_in_condition", "name": "tuple_in_condition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["columns", "collection"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.tuple_in_condition", "name": "tuple_in_condition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["columns", "collection"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["columns", "collection", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "airflow.utils.sqlalchemy.tuple_in_condition", "name": "tuple_in_condition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["columns", "collection", "session"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Select", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.tuple_in_condition", "name": "tuple_in_condition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["columns", "collection", "session"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Select", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["columns", "collection"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["columns", "collection", "session"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Select", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "tuple_not_in_condition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.tuple_not_in_condition", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["columns", "collection", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "airflow.utils.sqlalchemy.tuple_not_in_condition", "name": "tuple_not_in_condition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["columns", "collection", "session"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Select", "source_any": null, "type_of_any": 3}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_not_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["columns", "collection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "airflow.utils.sqlalchemy.tuple_not_in_condition", "name": "tuple_not_in_condition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["columns", "collection"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_not_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.tuple_not_in_condition", "name": "tuple_not_in_condition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["columns", "collection"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_not_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["columns", "collection", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "airflow.utils.sqlalchemy.tuple_not_in_condition", "name": "tuple_not_in_condition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["columns", "collection", "session"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Select", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_not_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "airflow.utils.sqlalchemy.tuple_not_in_condition", "name": "tuple_not_in_condition", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["columns", "collection", "session"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Select", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_not_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["columns", "collection"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_not_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["columns", "collection", "session"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnElement", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Select", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_not_in_condition", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.ColumnOperators", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "utc": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.timezone.utc", "kind": "Gdef"}, "with_row_locks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["query", "session", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.utils.sqlalchemy.with_row_locks", "name": "with_row_locks", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["query", "session", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Query", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Session", "source_any": null, "type_of_any": 3}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_row_locks", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.utils.sqlalchemy.Query", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/sqlalchemy.py"}