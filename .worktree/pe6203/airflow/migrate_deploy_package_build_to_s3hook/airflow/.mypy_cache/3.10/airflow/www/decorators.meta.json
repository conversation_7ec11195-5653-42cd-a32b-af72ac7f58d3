{"data_mtime": 1756971054, "dep_lines": [33, 35, 30, 33, 34, 32, 18, 20, 21, 22, 23, 24, 25, 26, 28, 29, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 10, 5, 5, 30, 30], "dependencies": ["airflow.utils.log.secrets_masker", "airflow.www.extensions.init_auth_manager", "pendulum.parsing.exceptions", "airflow.utils.log", "airflow.utils.session", "airflow.models", "__future__", "functools", "gzip", "itertools", "json", "logging", "io", "typing", "pendulum", "flask", "builtins", "_frozen_importlib", "abc"], "hash": "59272d4771ffb54f46f81888138d8252eced8dad", "id": "airflow.www.decorators", "ignore_all": true, "interface_hash": "e4525b77e59ba6580b65f2ec72bbea7167f5cc0a", "mtime": 1743589731, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/www/decorators.py", "plugin_data": null, "size": 6119, "suppressed": [], "version_id": "1.15.0"}