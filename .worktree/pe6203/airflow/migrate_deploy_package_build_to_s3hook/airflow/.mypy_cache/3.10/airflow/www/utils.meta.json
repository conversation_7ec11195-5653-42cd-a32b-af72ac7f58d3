{"data_mtime": 1756971053, "dep_lines": [53, 65, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 54, 55, 24, 27, 41, 42, 43, 47, 59, 18, 20, 21, 22, 23, 26, 34, 35, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 31, 32, 29, 30, 39, 61, 63, 28, 37, 60, 62, 33, 36, 38], "dep_prios": [5, 25, 10, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 25, 5, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5, 25, 25, 5, 5, 25, 25, 5, 5, 5], "dependencies": ["airflow.www.extensions.init_auth_manager", "airflow.www.extensions.init_appbuilder", "airflow.models.errors", "airflow.models.dagrun", "airflow.models.dagwarning", "airflow.models.taskinstance", "airflow.utils.timezone", "airflow.utils.code_utils", "airflow.utils.helpers", "airflow.utils.json", "airflow.utils.sqlalchemy", "airflow.utils.state", "airflow.www.forms", "airflow.www.widgets", "urllib.parse", "flask.helpers", "airflow.configuration", "airflow.exceptions", "airflow.models", "airflow.utils", "pendulum.datetime", "__future__", "json", "textwrap", "time", "typing", "flask", "markdown_it", "markupsafe", "builtins", "_frozen_importlib", "abc", "airflow.models.abstractoperator", "airflow.models.baseoperator", "airflow.models.mappedoperator", "airflow.models.taskmixin", "airflow.template", "airflow.template.templater", "airflow.utils.log", "airflow.utils.log.logging_mixin", "airflow.www.extensions", "datetime", "enum", "json.encoder", "pendulum", "pendulum.date", "pendulum.mixins", "pendulum.mixins.default", "typing_extensions"], "hash": "80414efe6df9f973ef0ffbc2477b73da66ace70c", "id": "airflow.www.utils", "ignore_all": true, "interface_hash": "a5c30addf49b27578634c4007670c75316ef7cb2", "mtime": 1743589731, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/www/utils.py", "plugin_data": null, "size": 33225, "suppressed": ["flask_appbuilder.models.sqla.filters", "flask_appbuilder.models.sqla.interface", "flask_appbuilder.models.filters", "flask_appbuilder.models.sqla", "sqlalchemy.ext.associationproxy", "sqlalchemy.orm.session", "sqlalchemy.sql.operators", "flask_appbuilder.forms", "pygments.formatters", "pygments.lexer", "sqlalchemy.sql", "flask_babel", "pygments", "sqlalchemy"], "version_id": "1.15.0"}