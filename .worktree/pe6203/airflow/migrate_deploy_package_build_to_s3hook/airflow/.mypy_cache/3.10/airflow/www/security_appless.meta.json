{"data_mtime": 1756971053, "dep_lines": [21, 17, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 24], "dep_prios": [5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 25], "dependencies": ["airflow.auth.managers.fab.security_manager.override", "__future__", "typing", "builtins", "_frozen_importlib", "abc", "airflow.auth", "airflow.auth.managers", "airflow.auth.managers.fab", "airflow.auth.managers.fab.security_manager", "airflow.utils", "airflow.utils.log", "airflow.utils.log.logging_mixin", "airflow.www.security_manager"], "hash": "eb42ba2ae1ccbcafc322be41f54d8eab26456242", "id": "airflow.www.security_appless", "ignore_all": true, "interface_hash": "b996f87ff5bdc245e28fcf4e8ae3283a05d3787d", "mtime": 1743589731, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/www/security_appless.py", "plugin_data": null, "size": 1640, "suppressed": ["flask_session"], "version_id": "1.15.0"}