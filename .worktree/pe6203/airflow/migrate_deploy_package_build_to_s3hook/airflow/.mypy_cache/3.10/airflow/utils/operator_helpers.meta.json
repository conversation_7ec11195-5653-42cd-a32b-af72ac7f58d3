{"data_mtime": 1756971053, "dep_lines": [24, 23, 18, 20, 21, 23, 154, 155, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 20, 20, 20, 5, 30, 30], "dependencies": ["airflow.utils.context", "airflow.settings", "__future__", "datetime", "typing", "airflow", "inspect", "itertools", "builtins", "_frozen_importlib", "abc"], "hash": "f8283be35237314234cd7fafea11686c77826e16", "id": "airflow.utils.operator_helpers", "ignore_all": true, "interface_hash": "89ca7b0fe5b026128ec4a882e9b20679d86c5fd7", "mtime": 1743589731, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/operator_helpers.py", "plugin_data": null, "size": 8580, "suppressed": [], "version_id": "1.15.0"}