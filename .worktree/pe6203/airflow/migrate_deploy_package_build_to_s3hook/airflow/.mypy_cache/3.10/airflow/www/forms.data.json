{".class": "MypyFile", "_fullname": "airflow.www.forms", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AirflowDateTimePickerROWidget": {".class": "SymbolTableNode", "cross_ref": "airflow.www.widgets.AirflowDateTimePickerROWidget", "kind": "Gdef"}, "AirflowDateTimePickerWidget": {".class": "SymbolTableNode", "cross_ref": "airflow.www.widgets.AirflowDateTimePickerWidget", "kind": "Gdef"}, "BS3PasswordFieldWidget": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.BS3PasswordFieldWidget", "name": "BS3PasswordFieldWidget", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.BS3PasswordFieldWidget", "source_any": null, "type_of_any": 3}}}, "BS3TextAreaFieldWidget": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.BS3TextAreaFieldWidget", "name": "BS3TextAreaFieldWidget", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.BS3TextAreaFieldWidget", "source_any": null, "type_of_any": 3}}}, "BS3TextAreaROWidget": {".class": "SymbolTableNode", "cross_ref": "airflow.www.widgets.BS3TextAreaROWidget", "kind": "Gdef"}, "BS3TextFieldROWidget": {".class": "SymbolTableNode", "cross_ref": "airflow.www.widgets.BS3TextFieldROWidget", "kind": "Gdef"}, "BS3TextFieldWidget": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.BS3TextFieldWidget", "name": "BS3TextFieldWidget", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.BS3TextFieldWidget", "source_any": null, "type_of_any": 3}}}, "DagRunEditForm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.forms.DagRunEditForm", "name": "DagRunEditForm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.forms.DagRunEditForm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.forms", "mro": ["airflow.www.forms.DagRunEditForm", "builtins.object"], "names": {".class": "SymbolTable", "conf": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.DagRunEditForm.conf", "name": "conf", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.TextAreaField", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.forms.TextAreaField", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dag_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.DagRunEditForm.dag_id", "name": "dag_id", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.StringField", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.forms.StringField", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "end_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.DagRunEditForm.end_date", "name": "end_date", "type": "airflow.www.forms.DateTimeWithTimezoneField"}}, "execution_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.DagRunEditForm.execution_date", "name": "execution_date", "type": "airflow.www.forms.DateTimeWithTimezoneField"}}, "note": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.DagRunEditForm.note", "name": "note", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.TextAreaField", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.forms.TextAreaField", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "populate_obj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.forms.DagRunEditForm.populate_obj", "name": "populate_obj", "type": null}}, "run_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.DagRunEditForm.run_id", "name": "run_id", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.StringField", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.forms.StringField", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "start_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.DagRunEditForm.start_date", "name": "start_date", "type": "airflow.www.forms.DateTimeWithTimezoneField"}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.DagRunEditForm.state", "name": "state", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.StringField", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.forms.StringField", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.forms.DagRunEditForm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.forms.DagRunEditForm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DagRunType": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.types.DagRunType", "kind": "Gdef"}, "DateTimeForm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.forms.DateTimeForm", "name": "DateTimeForm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.forms.DateTimeForm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.forms", "mro": ["airflow.www.forms.DateTimeForm", "builtins.object"], "names": {".class": "SymbolTable", "execution_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.DateTimeForm.execution_date", "name": "execution_date", "type": "airflow.www.forms.DateTimeWithTimezoneField"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.forms.DateTimeForm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.forms.DateTimeForm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DateTimeWithNumRunsForm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.forms.DateTimeWithNumRunsForm", "name": "DateTimeWithNumRunsForm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.forms.DateTimeWithNumRunsForm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.forms", "mro": ["airflow.www.forms.DateTimeWithNumRunsForm", "builtins.object"], "names": {".class": "SymbolTable", "base_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.DateTimeWithNumRunsForm.base_date", "name": "base_date", "type": "airflow.www.forms.DateTimeWithTimezoneField"}}, "num_runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.DateTimeWithNumRunsForm.num_runs", "name": "num_runs", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.SelectField", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.forms.SelectField", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.forms.DateTimeWithNumRunsForm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.forms.DateTimeWithNumRunsForm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DateTimeWithTimezoneField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.forms.DateTimeWithTimezoneField", "name": "DateTimeWithTimezoneField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.forms.DateTimeWithTimezoneField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.forms", "mro": ["airflow.www.forms.DateTimeWithTimezoneField", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "label", "validators", "datetime_format", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.forms.DateTimeWithTimezoneField.__init__", "name": "__init__", "type": null}}, "_get_default_timezone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.forms.DateTimeWithTimezoneField._get_default_timezone", "name": "_get_default_timezone", "type": null}}, "_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.forms.DateTimeWithTimezoneField._value", "name": "_value", "type": null}}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.www.forms.DateTimeWithTimezoneField.data", "name": "data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "airflow.www.forms.DateTimeWithTimezoneField.format", "name": "format", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "process_formdata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "valuelist"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.forms.DateTimeWithTimezoneField.process_formdata", "name": "process_formdata", "type": null}}, "widget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.DateTimeWithTimezoneField.widget", "name": "widget", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.widgets", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.forms.widgets", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.forms.DateTimeWithTimezoneField.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.forms.DateTimeWithTimezoneField", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DynamicForm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.DynamicForm", "name": "DynamicForm", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.DynamicForm", "source_any": null, "type_of_any": 3}}}, "Field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.Field", "name": "Field", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.Field", "source_any": null, "type_of_any": 3}}}, "FlaskForm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.FlaskForm", "name": "FlaskForm", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.FlaskForm", "source_any": null, "type_of_any": 3}}}, "InputRequired": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.InputRequired", "name": "InputRequired", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.InputRequired", "source_any": null, "type_of_any": 3}}}, "IntegerField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.IntegerField", "name": "IntegerField", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.IntegerField", "source_any": null, "type_of_any": 3}}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.Optional", "name": "Optional", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.Optional", "source_any": null, "type_of_any": 3}}}, "PasswordField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.PasswordField", "name": "PasswordField", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.PasswordField", "source_any": null, "type_of_any": 3}}}, "ProvidersManager": {".class": "SymbolTableNode", "cross_ref": "airflow.providers_manager.ProvidersManager", "kind": "Gdef"}, "ReadOnly": {".class": "SymbolTableNode", "cross_ref": "airflow.www.validators.ReadOnly", "kind": "Gdef"}, "Select2Widget": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.Select2Widget", "name": "Select2Widget", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.Select2Widget", "source_any": null, "type_of_any": 3}}}, "SelectField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.SelectField", "name": "SelectField", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.SelectField", "source_any": null, "type_of_any": 3}}}, "StringField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.StringField", "name": "StringField", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.StringField", "source_any": null, "type_of_any": 3}}}, "TaskInstanceEditForm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.forms.TaskInstanceEditForm", "name": "TaskInstanceEditForm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.forms.TaskInstanceEditForm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.forms", "mro": ["airflow.www.forms.TaskInstanceEditForm", "builtins.object"], "names": {".class": "SymbolTable", "dag_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.TaskInstanceEditForm.dag_id", "name": "dag_id", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.StringField", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.forms.StringField", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "end_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.TaskInstanceEditForm.end_date", "name": "end_date", "type": "airflow.www.forms.DateTimeWithTimezoneField"}}, "execution_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.TaskInstanceEditForm.execution_date", "name": "execution_date", "type": "airflow.www.forms.DateTimeWithTimezoneField"}}, "note": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.TaskInstanceEditForm.note", "name": "note", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.TextAreaField", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.forms.TextAreaField", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "populate_obj": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.forms.TaskInstanceEditForm.populate_obj", "name": "populate_obj", "type": null}}, "start_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.TaskInstanceEditForm.start_date", "name": "start_date", "type": "airflow.www.forms.DateTimeWithTimezoneField"}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.TaskInstanceEditForm.state", "name": "state", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.SelectField", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.forms.SelectField", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "task_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.forms.TaskInstanceEditForm.task_id", "name": "task_id", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.StringField", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.forms.StringField", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.forms.TaskInstanceEditForm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.forms.TaskInstanceEditForm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextAreaField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.TextAreaField", "name": "TextAreaField", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.TextAreaField", "source_any": null, "type_of_any": 3}}}, "ValidKey": {".class": "SymbolTableNode", "cross_ref": "airflow.www.validators.ValidKey", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.forms.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.forms.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.forms.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.forms.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.forms.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.forms.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cache": {".class": "SymbolTableNode", "cross_ref": "airflow.compat.functools.cache", "kind": "Gdef"}, "conf": {".class": "SymbolTableNode", "cross_ref": "airflow.configuration.conf", "kind": "Gdef"}, "create_connection_form_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.forms.create_connection_form_class", "name": "create_connection_form_class", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_connection_form_class", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": "airflow.www.forms.DynamicForm", "source_any": null, "type_of_any": 3}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "airflow.www.forms.create_connection_form_class", "name": "create_connection_form_class", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_connection_form_class", "ret_type": {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": "airflow.www.forms.DynamicForm", "source_any": null, "type_of_any": 3}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "lazy_gettext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.lazy_gettext", "name": "lazy_gettext", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.lazy_gettext", "source_any": null, "type_of_any": 3}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "pendulum": {".class": "SymbolTableNode", "cross_ref": "pendulum", "kind": "Gdef"}, "timezone": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.timezone", "kind": "Gdef"}, "widgets": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.forms.widgets", "name": "widgets", "type": {".class": "AnyType", "missing_import_name": "airflow.www.forms.widgets", "source_any": null, "type_of_any": 3}}}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/www/forms.py"}