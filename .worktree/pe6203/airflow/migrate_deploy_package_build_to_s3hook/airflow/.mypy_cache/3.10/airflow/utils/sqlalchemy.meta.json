{"data_mtime": 1756971053, "dep_lines": [39, 35, 36, 122, 33, 34, 18, 20, 21, 22, 23, 24, 25, 33, 1, 1, 1, 1, 1, 1, 1, 43, 29, 30, 31, 40, 41, 27, 28], "dep_prios": [25, 5, 5, 20, 10, 5, 5, 10, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 25, 5, 5, 5, 25, 25, 5, 5], "dependencies": ["kubernetes.client.models.v1_pod", "airflow.serialization.enums", "airflow.utils.timezone", "airflow.serialization.serialized_objects", "airflow.settings", "airflow.configuration", "__future__", "contextlib", "copy", "datetime", "json", "logging", "typing", "airflow", "builtins", "_frozen_importlib", "abc", "configparser", "kubernetes", "kubernetes.client", "kubernetes.client.models"], "hash": "4c4b8062d52718d3ab35c53795d4aba72b2f94f5", "id": "airflow.utils.sqlalchemy", "ignore_all": true, "interface_hash": "260e6ff95a11ca014fc9a894596e9b12134adb7c", "mtime": 1743589731, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/sqlalchemy.py", "plugin_data": null, "size": 20522, "suppressed": ["sqlalchemy.sql.expression", "sqlalchemy.dialects", "sqlalchemy.sql", "sqlalchemy.types", "sqlalchemy.exc", "sqlalchemy.orm", "dateutil", "sqlalchemy"], "version_id": "1.15.0"}