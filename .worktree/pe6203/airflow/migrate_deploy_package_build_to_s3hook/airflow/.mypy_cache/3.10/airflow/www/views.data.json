{".class": "MypyFile", "_fullname": "airflow.www.views", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AIRFLOW_CONFIG": {".class": "SymbolTableNode", "cross_ref": "airflow.configuration.AIRFLOW_CONFIG", "kind": "Gdef"}, "AccessView": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.models.resource_details.AccessView", "kind": "Gdef"}, "Airflow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowBaseView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.Airflow", "name": "Airflow", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.Airflow", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.Airflow", "airflow.www.views.AirflowBaseView", "builtins.object"], "names": {".class": "SymbolTable", "_clear_dag_tis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 3, 5, 5, 5, 5, 3], "arg_names": ["self", "dag", "start_date", "end_date", "origin", "task_ids", "recursive", "confirmed", "only_failed", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.Airflow._clear_dag_tis", "name": "_clear_dag_tis", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 3, 5, 5, 5, 5, 3], "arg_names": ["self", "dag", "start_date", "end_date", "origin", "task_ids", "recursive", "confirmed", "only_failed", "session"], "arg_types": ["airflow.www.views.Airflow", "airflow.models.dag.DAG", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Collection"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.bool", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear_dag_tis of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mark_dagrun_state_as_failed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dag_id", "dag_run_id", "confirmed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.Airflow._mark_dagrun_state_as_failed", "name": "_mark_dagrun_state_as_failed", "type": null}}, "_mark_dagrun_state_as_queued": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "dag_id", "dag_run_id", "confirmed", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow._mark_dagrun_state_as_queued", "name": "_mark_dagrun_state_as_queued", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "dag_id", "dag_run_id", "confirmed", "session"], "arg_types": ["airflow.www.views.Airflow", "builtins.str", "builtins.str", "builtins.bool", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mark_dagrun_state_as_queued of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow._mark_dagrun_state_as_queued", "name": "_mark_dagrun_state_as_queued", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "dag_id", "dag_run_id", "confirmed", "session"], "arg_types": ["airflow.www.views.Airflow", "builtins.str", "builtins.str", "builtins.bool", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mark_dagrun_state_as_queued of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_mark_dagrun_state_as_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "dag_id", "dag_run_id", "confirmed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.Airflow._mark_dagrun_state_as_success", "name": "_mark_dagrun_state_as_success", "type": null}}, "_mark_task_group_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "dag_id", "run_id", "group_id", "origin", "upstream", "downstream", "future", "past", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.Airflow._mark_task_group_state", "name": "_mark_task_group_state", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "dag_id", "run_id", "group_id", "origin", "upstream", "downstream", "future", "past", "state"], "arg_types": ["airflow.www.views.Airflow", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "airflow.utils.state.TaskInstanceState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mark_task_group_state of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mark_task_instance_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "dag_id", "run_id", "task_id", "map_indexes", "origin", "upstream", "downstream", "future", "past", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.Airflow._mark_task_instance_state", "name": "_mark_task_instance_state", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "dag_id", "run_id", "task_id", "map_indexes", "origin", "upstream", "downstream", "future", "past", "state"], "arg_types": ["airflow.www.views.Airflow", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "airflow.utils.state.TaskInstanceState"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mark_task_instance_state of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "audit_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.audit_log", "name": "audit_log", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "arg_types": ["airflow.www.views.Airflow", "builtins.str", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "audit_log of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.audit_log", "name": "audit_log", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "blocked": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.blocked", "name": "blocked", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blocked of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.blocked", "name": "blocked", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "calendar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.calendar", "name": "calendar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "arg_types": ["airflow.www.views.Airflow", "builtins.str", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calendar of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.calendar", "name": "calendar", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.clear", "name": "clear", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "cluster_activity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.cluster_activity", "name": "cluster_activity", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.cluster_activity", "name": "cluster_activity", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.code", "name": "code", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "code of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.code", "name": "code", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "confirm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.confirm", "name": "confirm", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.confirm", "name": "confirm", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "dag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dag_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.dag", "name": "dag", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.dag", "name": "dag", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "dag_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.dag_details", "name": "dag_details", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dag_details of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.dag_details", "name": "dag_details", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "dag_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.dag_stats", "name": "dag_stats", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dag_stats of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.dag_stats", "name": "dag_stats", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "dagrun_clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.dagrun_clear", "name": "dagrun_clear", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dagrun_clear of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.dagrun_clear", "name": "dagrun_clear", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "dagrun_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.dagrun_details", "name": "dagrun_details", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.dagrun_details", "name": "dagrun_details", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "dagrun_failed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.dagrun_failed", "name": "dagrun_failed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.dagrun_failed", "name": "dagrun_failed", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "dagrun_queued": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.dagrun_queued", "name": "dagrun_queued", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.dagrun_queued", "name": "dagrun_queued", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "dagrun_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.dagrun_success", "name": "dagrun_success", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.dagrun_success", "name": "dagrun_success", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "dataset_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.dataset_dependencies", "name": "dataset_dependencies", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.dataset_dependencies", "name": "dataset_dependencies", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "datasets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.datasets", "name": "datasets", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.datasets", "name": "datasets", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "datasets_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.datasets_summary", "name": "datasets_summary", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.datasets_summary", "name": "datasets_summary", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.delete", "name": "delete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.delete", "name": "delete", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "duration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.duration", "name": "duration", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "arg_types": ["airflow.www.views.Airflow", "builtins.str", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "duration of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.duration", "name": "duration", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "extra_links": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.extra_links", "name": "extra_links", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extra_links of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.extra_links", "name": "extra_links", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "failed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.failed", "name": "failed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.failed", "name": "failed", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "gantt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.gantt", "name": "gantt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "arg_types": ["airflow.www.views.Airflow", "builtins.str", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "gantt of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.gantt", "name": "gantt", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "get_logs_with_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.get_logs_with_metadata", "name": "get_logs_with_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_logs_with_metadata of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.get_logs_with_metadata", "name": "get_logs_with_metadata", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.graph", "name": "graph", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "arg_types": ["airflow.www.views.Airflow", "builtins.str", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "graph of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.graph", "name": "graph", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "graph_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.graph_data", "name": "graph_data", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "graph_data of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.graph_data", "name": "graph_data", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "grid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.grid", "name": "grid", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "arg_types": ["airflow.www.views.Airflow", "builtins.str", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "grid of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.grid", "name": "grid", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "grid_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.grid_data", "name": "grid_data", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.grid_data", "name": "grid_data", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "health": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.health", "name": "health", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.health", "name": "health", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "historical_metrics_data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.historical_metrics_data", "name": "historical_metrics_data", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.historical_metrics_data", "name": "historical_metrics_data", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.index", "name": "index", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.index", "name": "index", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "landing_times": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.landing_times", "name": "landing_times", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "arg_types": ["airflow.www.views.Airflow", "builtins.str", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "landing_times of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.landing_times", "name": "landing_times", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "last_dagruns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.last_dagruns", "name": "last_dagruns", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "last_dagruns of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.last_dagruns", "name": "last_dagruns", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "legacy_audit_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.legacy_audit_log", "name": "legacy_audit_log", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.legacy_audit_log", "name": "legacy_audit_log", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "legacy_calendar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.legacy_calendar", "name": "legacy_calendar", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.legacy_calendar", "name": "legacy_calendar", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "legacy_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.legacy_code", "name": "legacy_code", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.legacy_code", "name": "legacy_code", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "legacy_dag_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.legacy_dag_details", "name": "legacy_dag_details", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.legacy_dag_details", "name": "legacy_dag_details", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "legacy_duration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.legacy_duration", "name": "legacy_duration", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.legacy_duration", "name": "legacy_duration", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "legacy_gantt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.legacy_gantt", "name": "legacy_gantt", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.legacy_gantt", "name": "legacy_gantt", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "legacy_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.legacy_graph", "name": "legacy_graph", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.legacy_graph", "name": "legacy_graph", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "legacy_landing_times": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.legacy_landing_times", "name": "legacy_landing_times", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.legacy_landing_times", "name": "legacy_landing_times", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "legacy_tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.legacy_tree", "name": "legacy_tree", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.legacy_tree", "name": "legacy_tree", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "legacy_tries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.legacy_tries", "name": "legacy_tries", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.legacy_tries", "name": "legacy_tries", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.log", "name": "log", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "log of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.log", "name": "log", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "next_run_datasets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dag_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.next_run_datasets", "name": "next_run_datasets", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.next_run_datasets", "name": "next_run_datasets", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "next_run_datasets_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.next_run_datasets_summary", "name": "next_run_datasets_summary", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "next_run_datasets_summary of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.next_run_datasets_summary", "name": "next_run_datasets_summary", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "paused": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.paused", "name": "paused", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.paused", "name": "paused", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "redirect_to_external_log": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.redirect_to_external_log", "name": "redirect_to_external_log", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "redirect_to_external_log of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.redirect_to_external_log", "name": "redirect_to_external_log", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "rendered_k8s": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.rendered_k8s", "name": "rendered_k8s", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rendered_k8s of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.rendered_k8s", "name": "rendered_k8s", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "rendered_templates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.rendered_templates", "name": "rendered_templates", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.rendered_templates", "name": "rendered_templates", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "robots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.robots", "name": "robots", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.robots", "name": "robots", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.success", "name": "success", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.success", "name": "success", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "task": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.task", "name": "task", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "task of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.task", "name": "task", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "task_instances": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.task_instances", "name": "task_instances", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.task_instances", "name": "task_instances", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "task_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.task_stats", "name": "task_stats", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "task_stats of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.task_stats", "name": "task_stats", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "tries": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.tries", "name": "tries", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "arg_types": ["airflow.www.views.Airflow", "builtins.str", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tries of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.tries", "name": "tries", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "trigger": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.trigger", "name": "trigger", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dag_id", "session"], "arg_types": ["airflow.www.views.Airflow", "builtins.str", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trigger of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.trigger", "name": "trigger", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "xcom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.Airflow.xcom", "name": "xcom", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.Airflow", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xcom of Airflow", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Airflow.xcom", "name": "xcom", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.Airflow.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.Airflow", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AirflowBaseView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.AirflowBaseView", "name": "AirflowBaseView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.AirflowBaseView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.AirflowBaseView", "builtins.object"], "names": {".class": "SymbolTable", "_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.AirflowBaseView._", "name": "_", "type": "airflow.executors.executor_loader.ConnectorSource"}}, "executor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.AirflowBaseView.executor", "name": "executor", "type": {".class": "TypeType", "item": "airflow.executors.base_executor.BaseExecutor"}}}, "extra_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.AirflowBaseView.extra_args", "name": "extra_args", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "line_chart_attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.AirflowBaseView.line_chart_attr", "name": "line_chart_attr", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "macros": {".class": "SymbolTableNode", "cross_ref": "airflow.macros", "kind": "<PERSON><PERSON><PERSON>"}, "render_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.AirflowBaseView.render_template", "name": "render_template", "type": null}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.AirflowBaseView.route_base", "name": "route_base", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.AirflowBaseView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.AirflowBaseView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AirflowConfigException": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.AirflowConfigException", "kind": "Gdef"}, "AirflowException": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.AirflowException", "kind": "Gdef"}, "AirflowModelListWidget": {".class": "SymbolTableNode", "cross_ref": "airflow.www.widgets.AirflowModelListWidget", "kind": "Gdef"}, "AirflowModelView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.AirflowModelView", "name": "AirflowModelView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.AirflowModelView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.AirflowModelView", "builtins.object"], "names": {".class": "SymbolTable", "CustomSQLAInterface": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.AirflowModelView.CustomSQLAInterface", "name": "CustomSQLAInterface", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["obj", "session"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "airflow.www.utils.Session", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": ["airflow.www.utils.CustomSQLAInterface"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "airflow.www.utils.CustomSQLAInterface", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getattribute__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.AirflowModelView.__getattribute__", "name": "__getattribute__", "type": null}}, "action_post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.AirflowModelView.action_post", "name": "action_post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.AirflowModelView.action_post", "name": "action_post", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.AirflowModelView.delete", "name": "delete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.AirflowModelView.delete", "name": "delete", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "edit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.AirflowModelView.edit", "name": "edit", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.AirflowModelView.edit", "name": "edit", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "list_widget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.AirflowModelView.list_widget", "name": "list_widget", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "airflow.www.widgets.AirflowModelListWidget", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "page_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.AirflowModelView.page_size", "name": "page_size", "type": "builtins.int"}}, "show": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.AirflowModelView.show", "name": "show", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.AirflowModelView.show", "name": "show", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.AirflowModelView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.AirflowModelView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AirflowNotFoundException": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.AirflowNotFoundException", "kind": "Gdef"}, "AirflowVariableShowWidget": {".class": "SymbolTableNode", "cross_ref": "airflow.www.widgets.AirflowVariableShowWidget", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutocompleteView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowBaseView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.AutocompleteView", "name": "AutocompleteView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.AutocompleteView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.AutocompleteView", "airflow.www.views.AirflowBaseView", "builtins.object"], "names": {".class": "SymbolTable", "autocomplete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.AutocompleteView.autocomplete", "name": "autocomplete", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "session"], "arg_types": ["airflow.www.views.AutocompleteView", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autocomplete of AutocompleteView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.AutocompleteView.autocomplete", "name": "autocomplete", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autocomplete of AutocompleteView", "ret_type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.AutocompleteView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.AutocompleteView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseFilter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.BaseFilter", "name": "BaseFilter", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.BaseFilter", "source_any": null, "type_of_any": 3}}}, "BaseHook": {".class": "SymbolTableNode", "cross_ref": "airflow.hooks.base.BaseHook", "kind": "Gdef"}, "BaseView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.BaseView", "name": "BaseView", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.BaseView", "source_any": null, "type_of_any": 3}}}, "BooleanField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.BooleanField", "name": "BooleanField", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.BooleanField", "source_any": null, "type_of_any": 3}}}, "Collection": {".class": "SymbolTableNode", "cross_ref": "typing.Collection", "kind": "Gdef"}, "ConfigurationView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowBaseView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.ConfigurationView", "name": "ConfigurationView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.ConfigurationView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.ConfigurationView", "airflow.www.views.AirflowBaseView", "builtins.object"], "names": {".class": "SymbolTable", "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConfigurationView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConfigurationView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "conf": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.ConfigurationView.conf", "name": "conf", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.ConfigurationView.conf", "name": "conf", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "default_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConfigurationView.default_view", "name": "default_view", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.ConfigurationView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.ConfigurationView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Connection": {".class": "SymbolTableNode", "cross_ref": "airflow.models.connection.Connection", "kind": "Gdef"}, "ConnectionFormProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.ConnectionFormProxy", "name": "ConnectionFormProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "airflow.www.views.ConnectionFormProxy", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.ConnectionFormProxy", "builtins.object"], "names": {".class": "SymbolTable", "refresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "airflow.www.views.ConnectionFormProxy.refresh", "name": "refresh", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "airflow.www.views.ConnectionFormProxy.refresh", "name": "refresh", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["obj"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refresh of ConnectionFormProxy", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.ConnectionFormProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.ConnectionFormProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionFormWidget": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.ConnectionFormWidget", "name": "ConnectionFormWidget", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.ConnectionFormWidget", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.ConnectionFormWidget", "builtins.object"], "names": {".class": "SymbolTable", "field_behaviours": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.views.ConnectionFormWidget.field_behaviours", "name": "field_behaviours", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.views.ConnectionFormWidget"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_behaviours of ConnectionFormWidget", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "airflow.www.views.ConnectionFormWidget.field_behaviours", "name": "field_behaviours", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.views.ConnectionFormWidget"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_behaviours of ConnectionFormWidget", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "testable_connection_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.views.ConnectionFormWidget.testable_connection_types", "name": "testable_connection_types", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.views.ConnectionFormWidget"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testable_connection_types of ConnectionFormWidget", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "airflow.www.views.ConnectionFormWidget.testable_connection_types", "name": "testable_connection_types", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.views.ConnectionFormWidget"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "testable_connection_types of ConnectionFormWidget", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.ConnectionFormWidget.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.ConnectionFormWidget", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionModelView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowModelView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.ConnectionModelView", "name": "ConnectionModelView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.ConnectionModelView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.ConnectionModelView", "airflow.www.views.AirflowModelView", "builtins.object"], "names": {".class": "SymbolTable", "_add_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView._add_columns", "name": "_add_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_edit_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView._edit_columns", "name": "_edit_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_iter_extra_field_names_and_sensitivity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.ConnectionModelView._iter_extra_field_names_and_sensitivity", "name": "_iter_extra_field_names_and_sensitivity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.views.ConnectionModelView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_iter_extra_field_names_and_sensitivity of ConnectionModelView", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "action_muldelete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connections"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.ConnectionModelView.action_muldelete", "name": "action_muldelete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.ConnectionModelView.action_muldelete", "name": "action_muldelete", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_mulduplicate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "connections", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.ConnectionModelView.action_mulduplicate", "name": "action_mulduplicate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "connections", "session"], "arg_types": ["airflow.www.views.ConnectionModelView", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "action_mulduplicate of ConnectionModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.ConnectionModelView.action_mulduplicate", "name": "action_mulduplicate", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "add_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.views.ConnectionModelView.add_columns", "name": "add_columns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.views.ConnectionModelView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_columns of ConnectionModelView", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.www.views.ConnectionModelView.add_columns", "name": "add_columns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.views.ConnectionModelView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_columns of ConnectionModelView", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "add_form": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView.add_form", "name": "add_form", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["airflow.www.views.ConnectionFormProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "airflow.www.views.ConnectionFormProxy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView.add_template", "name": "add_template", "type": "builtins.str"}}, "add_widget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView.add_widget", "name": "add_widget", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "airflow.www.views.ConnectionFormWidget", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView.base_order", "name": "base_order", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "datamodel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView.datamodel", "name": "datamodel", "type": "airflow.www.utils.CustomSQLAInterface"}}, "edit_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "airflow.www.views.ConnectionModelView.edit_columns", "name": "edit_columns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.views.ConnectionModelView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "edit_columns of ConnectionModelView", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "airflow.www.views.ConnectionModelView.edit_columns", "name": "edit_columns", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.views.ConnectionModelView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "edit_columns of ConnectionModelView", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "edit_form": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView.edit_form", "name": "edit_form", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["airflow.www.views.ConnectionFormProxy"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "airflow.www.views.ConnectionFormProxy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "edit_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView.edit_template", "name": "edit_template", "type": "builtins.str"}}, "edit_widget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView.edit_widget", "name": "edit_widget", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "airflow.www.views.ConnectionFormWidget", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView.list_columns", "name": "list_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "method_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView.method_permission_name", "name": "method_permission_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "prefill_form": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "form", "pk"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.ConnectionModelView.prefill_form", "name": "prefill_form", "type": null}}, "process_form": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "form", "is_created"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.ConnectionModelView.process_form", "name": "process_form", "type": null}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ConnectionModelView.route_base", "name": "route_base", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.ConnectionModelView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.ConnectionModelView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CronMixin": {".class": "SymbolTableNode", "cross_ref": "airflow.timetables._cron.CronMixin", "kind": "Gdef"}, "DAG": {".class": "SymbolTableNode", "cross_ref": "airflow.models.dag.DAG", "kind": "Gdef"}, "DagAccessEntity": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.models.resource_details.DagAccessEntity", "kind": "Gdef"}, "DagDependenciesView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowBaseView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.DagDependenciesView", "name": "DagDependenciesView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.DagDependenciesView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.DagDependenciesView", "airflow.www.views.AirflowBaseView", "builtins.object"], "names": {".class": "SymbolTable", "_calculate_graph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.DagDependenciesView._calculate_graph", "name": "_calculate_graph", "type": null}}, "edges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.www.views.DagDependenciesView.edges", "name": "edges", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "last_refresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagDependenciesView.last_refresh", "name": "last_refresh", "type": "datetime.datetime"}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.DagDependenciesView.list", "name": "list", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.DagDependenciesView.list", "name": "list", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "nodes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "airflow.www.views.DagDependenciesView.nodes", "name": "nodes", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "refresh_interval": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagDependenciesView.refresh_interval", "name": "refresh_interval", "type": "datetime.<PERSON><PERSON><PERSON>"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.DagDependenciesView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.DagDependenciesView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DagDetails": {".class": "SymbolTableNode", "cross_ref": "airflow.auth.managers.models.resource_details.DagDetails", "kind": "Gdef"}, "DagFilter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.DagFilter", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.DagFilter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.DagFilter", "builtins.object"], "names": {".class": "SymbolTable", "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "query", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.DagFilter.apply", "name": "apply", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.DagFilter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.DagFilter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DagModel": {".class": "SymbolTableNode", "cross_ref": "airflow.models.dag.DagModel", "kind": "Gdef"}, "DagRun": {".class": "SymbolTableNode", "cross_ref": "airflow.models.dagrun.DagRun", "kind": "Gdef"}, "DagRunEditForm": {".class": "SymbolTableNode", "cross_ref": "airflow.www.forms.DagRunEditForm", "kind": "Gdef"}, "DagRunModelView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowModelView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.DagRunModelView", "name": "DagRunModelView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.DagRunModelView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.DagRunModelView", "airflow.www.views.AirflowModelView", "builtins.object"], "names": {".class": "SymbolTable", "_set_dag_runs_to_active_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "drs", "state", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.DagRunModelView._set_dag_runs_to_active_state", "name": "_set_dag_runs_to_active_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "drs", "state", "session"], "arg_types": ["airflow.www.views.DagRunModelView", {".class": "Instance", "args": ["airflow.models.dagrun.DagRun"], "extra_attrs": null, "type_ref": "builtins.list"}, "airflow.utils.state.DagRunState", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_dag_runs_to_active_state of DagRunModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.DagRunModelView._set_dag_runs_to_active_state", "name": "_set_dag_runs_to_active_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "drs", "state", "session"], "arg_types": ["airflow.www.views.DagRunModelView", {".class": "Instance", "args": ["airflow.models.dagrun.DagRun"], "extra_attrs": null, "type_ref": "builtins.list"}, "airflow.utils.state.DagRunState", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_dag_runs_to_active_state of DagRunModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "action_clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "drs", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.DagRunModelView.action_clear", "name": "action_clear", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "drs", "session"], "arg_types": ["airflow.www.views.DagRunModelView", {".class": "Instance", "args": ["airflow.models.dagrun.DagRun"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "action_clear of DagRunModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.DagRunModelView.action_clear", "name": "action_clear", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_muldelete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.DagRunModelView.action_muldelete", "name": "action_muldelete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "items"], "arg_types": ["airflow.www.views.DagRunModelView", {".class": "Instance", "args": ["airflow.models.dagrun.DagRun"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "action_muldelete of DagRunModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.DagRunModelView.action_muldelete", "name": "action_muldelete", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_set_failed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "drs", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.DagRunModelView.action_set_failed", "name": "action_set_failed", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "drs", "session"], "arg_types": ["airflow.www.views.DagRunModelView", {".class": "Instance", "args": ["airflow.models.dagrun.DagRun"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "action_set_failed of DagRunModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.DagRunModelView.action_set_failed", "name": "action_set_failed", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_set_queued": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "drs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.DagRunModelView.action_set_queued", "name": "action_set_queued", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "drs"], "arg_types": ["airflow.www.views.DagRunModelView", {".class": "Instance", "args": ["airflow.models.dagrun.DagRun"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "action_set_queued of DagRunModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.DagRunModelView.action_set_queued", "name": "action_set_queued", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_set_running": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "drs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.DagRunModelView.action_set_running", "name": "action_set_running", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "drs"], "arg_types": ["airflow.www.views.DagRunModelView", {".class": "Instance", "args": ["airflow.models.dagrun.DagRun"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "action_set_running of DagRunModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.DagRunModelView.action_set_running", "name": "action_set_running", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_set_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "drs", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.DagRunModelView.action_set_success", "name": "action_set_success", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "drs", "session"], "arg_types": ["airflow.www.views.DagRunModelView", {".class": "Instance", "args": ["airflow.models.dagrun.DagRun"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "action_set_success of DagRunModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.DagRunModelView.action_set_success", "name": "action_set_success", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "base_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.base_filters", "name": "base_filters", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "base_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.base_order", "name": "base_order", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "datamodel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.datamodel", "name": "datamodel", "type": "airflow.www.utils.CustomSQLAInterface"}}, "duration_f": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.DagRunModelView.duration_f", "name": "duration_f", "type": null}}, "edit_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.edit_columns", "name": "edit_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "edit_form": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.edit_form", "name": "edit_form", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "airflow.www.forms.DagRunEditForm", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatters_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.formatters_columns", "name": "formatters_columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "label_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.label_columns", "name": "label_columns", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.list_columns", "name": "list_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "method_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.method_permission_name", "name": "method_permission_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "order_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.order_columns", "name": "order_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.route_base", "name": "route_base", "type": "builtins.str"}}, "search_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DagRunModelView.search_columns", "name": "search_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.DagRunModelView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.DagRunModelView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DagRunState": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.state.DagRunState", "kind": "Gdef"}, "DagRunType": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.types.DagRunType", "kind": "Gdef"}, "DagScheduleDatasetReference": {".class": "SymbolTableNode", "cross_ref": "airflow.models.dataset.DagScheduleDatasetReference", "kind": "Gdef"}, "DagTag": {".class": "SymbolTableNode", "cross_ref": "airflow.models.dag.DagTag", "kind": "Gdef"}, "DataInterval": {".class": "SymbolTableNode", "cross_ref": "airflow.timetables.base.DataInterval", "kind": "Gdef"}, "Dataset": {".class": "SymbolTableNode", "cross_ref": "airflow.datasets.Dataset", "kind": "Gdef"}, "DatasetDagRunQueue": {".class": "SymbolTableNode", "cross_ref": "airflow.models.dataset.DatasetDagRunQueue", "kind": "Gdef"}, "DatasetEvent": {".class": "SymbolTableNode", "cross_ref": "airflow.models.dataset.DatasetEvent", "kind": "Gdef"}, "DatasetModel": {".class": "SymbolTableNode", "cross_ref": "airflow.models.dataset.DatasetModel", "kind": "Gdef"}, "Date": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Date", "name": "Date", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.Date", "source_any": null, "type_of_any": 3}}}, "DateTime": {".class": "SymbolTableNode", "cross_ref": "pendulum.datetime.DateTime", "kind": "Gdef"}, "DateTimeForm": {".class": "SymbolTableNode", "cross_ref": "airflow.www.forms.DateTimeForm", "kind": "Gdef"}, "DateTimeWithNumRunsForm": {".class": "SymbolTableNode", "cross_ref": "airflow.www.forms.DateTimeWithNumRunsForm", "kind": "Gdef"}, "DepContext": {".class": "SymbolTableNode", "cross_ref": "airflow.ti_deps.dep_context.DepContext", "kind": "Gdef"}, "DevView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.DevView", "name": "DevView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.DevView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.DevView", "builtins.object"], "names": {".class": "SymbolTable", "coverage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.DevView.coverage", "name": "coverage", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.DevView.coverage", "name": "coverage", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DevView.route_base", "name": "route_base", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.DevView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.DevView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DocsView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.DocsView", "name": "DocsView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.DocsView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.DocsView", "builtins.object"], "names": {".class": "SymbolTable", "home": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.DocsView.home", "name": "home", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.DocsView.home", "name": "home", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.DocsView.route_base", "name": "route_base", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.DocsView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.DocsView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExecutorLoader": {".class": "SymbolTableNode", "cross_ref": "airflow.executors.executor_loader.ExecutorLoader", "kind": "Gdef"}, "FILTER_STATUS_COOKIE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.FILTER_STATUS_COOKIE", "name": "FILTER_STATUS_COOKIE", "type": "builtins.str"}}, "FILTER_TAGS_COOKIE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.FILTER_TAGS_COOKIE", "name": "FILTER_TAGS_COOKIE", "type": "builtins.str"}}, "FLAMSG_ERR_SEC_ACCESS_DENIED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.FLAMSG_ERR_SEC_ACCESS_DENIED", "name": "FLAMSG_ERR_SEC_ACCESS_DENIED", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.FLAMSG_ERR_SEC_ACCESS_DENIED", "source_any": null, "type_of_any": 3}}}, "FormWidget": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.FormWidget", "name": "FormWidget", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.FormWidget", "source_any": null, "type_of_any": 3}}}, "IntegrityError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.IntegrityError", "name": "IntegrityError", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.IntegrityError", "source_any": null, "type_of_any": 3}}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "JSONDecodeError": {".class": "SymbolTableNode", "cross_ref": "json.decoder.JSONDecodeError", "kind": "Gdef"}, "Job": {".class": "SymbolTableNode", "cross_ref": "airflow.jobs.job.Job", "kind": "Gdef"}, "JobModelView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowModelView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.JobModelView", "name": "JobModelView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.JobModelView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.JobModelView", "airflow.www.views.AirflowModelView", "builtins.object"], "names": {".class": "SymbolTable", "base_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.JobModelView.base_filters", "name": "base_filters", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "base_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.JobModelView.base_order", "name": "base_order", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.JobModelView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.JobModelView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "datamodel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.JobModelView.datamodel", "name": "datamodel", "type": "airflow.www.utils.CustomSQLAInterface"}}, "formatters_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.JobModelView.formatters_columns", "name": "formatters_columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.JobModelView.list_columns", "name": "list_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "method_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.JobModelView.method_permission_name", "name": "method_permission_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.JobModelView.route_base", "name": "route_base", "type": "builtins.str"}}, "search_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.JobModelView.search_columns", "name": "search_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.JobModelView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.JobModelView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LINECHART_X_AXIS_TICKFORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.LINECHART_X_AXIS_TICKFORMAT", "name": "LINECHART_X_AXIS_TICKFORMAT", "type": "builtins.str"}}, "Log": {".class": "SymbolTableNode", "cross_ref": "airflow.models.log.Log", "kind": "Gdef"}, "LogModelView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowModelView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.LogModelView", "name": "LogModelView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.LogModelView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.LogModelView", "airflow.www.views.AirflowModelView", "builtins.object"], "names": {".class": "SymbolTable", "base_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.LogModelView.base_filters", "name": "base_filters", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "base_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.LogModelView.base_order", "name": "base_order", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.LogModelView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.LogModelView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "datamodel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.LogModelView.datamodel", "name": "datamodel", "type": "airflow.www.utils.CustomSQLAInterface"}}, "formatters_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.LogModelView.formatters_columns", "name": "formatters_columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "label_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.LogModelView.label_columns", "name": "label_columns", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.LogModelView.list_columns", "name": "list_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "method_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.LogModelView.method_permission_name", "name": "method_permission_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.LogModelView.route_base", "name": "route_base", "type": "builtins.str"}}, "search_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.LogModelView.search_columns", "name": "search_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.LogModelView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.LogModelView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "Markup": {".class": "SymbolTableNode", "cross_ref": "markupsafe.Markup", "kind": "Gdef"}, "ModelView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.ModelView", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.ModelView", "source_any": null, "type_of_any": 3}}}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "NEW_SESSION": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.session.NEW_SESSION", "kind": "Gdef"}, "Operator": {".class": "SymbolTableNode", "cross_ref": "airflow.models.operator.Operator", "kind": "Gdef"}, "PAGE_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PAGE_SIZE", "name": "PAGE_SIZE", "type": "builtins.int"}}, "PLUGINS_ATTRIBUTES_TO_DUMP": {".class": "SymbolTableNode", "cross_ref": "airflow.plugins_manager.PLUGINS_ATTRIBUTES_TO_DUMP", "kind": "Gdef"}, "ParamValidationError": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.ParamValidationError", "kind": "Gdef"}, "ParserError": {".class": "SymbolTableNode", "cross_ref": "pendulum.parsing.exceptions.ParserError", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "PluginView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowBaseView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.PluginView", "name": "P<PERSON>in<PERSON>iew", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.PluginView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.PluginView", "airflow.www.views.AirflowBaseView", "builtins.object"], "names": {".class": "SymbolTable", "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PluginView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PluginView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "default_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PluginView.default_view", "name": "default_view", "type": "builtins.str"}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.PluginView.list", "name": "list", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.PluginView.list", "name": "list", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "method_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PluginView.method_permission_name", "name": "method_permission_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "plugins_attributes_to_dump": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PluginView.plugins_attributes_to_dump", "name": "plugins_attributes_to_dump", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.PluginView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.PluginView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PoolModelView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowModelView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.PoolModelView", "name": "PoolModelView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.PoolModelView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.PoolModelView", "airflow.www.views.AirflowModelView", "builtins.object"], "names": {".class": "SymbolTable", "action_muldelete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.PoolModelView.action_muldelete", "name": "action_muldelete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.PoolModelView.action_muldelete", "name": "action_muldelete", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "add_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.add_columns", "name": "add_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "add_form_extra_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.add_form_extra_fields", "name": "add_form_extra_fields", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "airflow.www.views.BooleanField", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.BooleanField", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "base_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.base_order", "name": "base_order", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "datamodel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.datamodel", "name": "datamodel", "type": "airflow.www.utils.CustomSQLAInterface"}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.PoolModelView.delete", "name": "delete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.PoolModelView.delete", "name": "delete", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "edit_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.edit_columns", "name": "edit_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "edit_form_extra_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.edit_form_extra_fields", "name": "edit_form_extra_fields", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": "airflow.www.views.BooleanField", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.BooleanField", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "fdeferred_slots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.PoolModelView.fdeferred_slots", "name": "fdeferred_slots", "type": null}}, "formatters_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.formatters_columns", "name": "formatters_columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.views.PoolModelView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "fqueued_slots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.PoolModelView.fqueued_slots", "name": "fqueued_slots", "type": null}}, "frunning_slots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.PoolModelView.frunning_slots", "name": "frunning_slots", "type": null}}, "fscheduled_slots": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.PoolModelView.fscheduled_slots", "name": "fscheduled_slots", "type": null}}, "include_deferred_field": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.include_deferred_field", "name": "include_deferred_field", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.BooleanField", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.BooleanField", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "list_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.list_columns", "name": "list_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "list_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.list_template", "name": "list_template", "type": "builtins.str"}}, "method_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.method_permission_name", "name": "method_permission_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "pool_link": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.PoolModelView.pool_link", "name": "pool_link", "type": null}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.route_base", "name": "route_base", "type": "builtins.str"}}, "validators_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.PoolModelView.validators_columns", "name": "validators_columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.www.views.validators", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.validators", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.PoolModelView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.PoolModelView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProviderView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowBaseView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.ProviderView", "name": "Provide<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.ProviderView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.ProviderView", "airflow.www.views.AirflowBaseView", "builtins.object"], "names": {".class": "SymbolTable", "_clean_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.ProviderView._clean_description", "name": "_clean_description", "type": null}}, "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ProviderView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ProviderView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "default_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ProviderView.default_view", "name": "default_view", "type": "builtins.str"}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.ProviderView.list", "name": "list", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.ProviderView.list", "name": "list", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "method_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.ProviderView.method_permission_name", "name": "method_permission_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.ProviderView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.ProviderView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProvidersManager": {".class": "SymbolTableNode", "cross_ref": "airflow.providers_manager.ProvidersManager", "kind": "Gdef"}, "RUN_ID_REGEX": {".class": "SymbolTableNode", "cross_ref": "airflow.models.dagrun.RUN_ID_REGEX", "kind": "Gdef"}, "RedocView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowBaseView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.RedocView", "name": "RedocView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.RedocView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.RedocView", "airflow.www.views.AirflowBaseView", "builtins.object"], "names": {".class": "SymbolTable", "default_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.RedocView.default_view", "name": "default_view", "type": "builtins.str"}}, "redoc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.RedocView.redoc", "name": "redoc", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.RedocView.redoc", "name": "redoc", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.RedocView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.RedocView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RemovedInAirflow3Warning": {".class": "SymbolTableNode", "cross_ref": "airflow.exceptions.RemovedInAirflow3Warning", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "flask.wrappers.Response", "kind": "Gdef"}, "SCHEDULER_QUEUED_DEPS": {".class": "SymbolTableNode", "cross_ref": "airflow.ti_deps.dependencies_deps.SCHEDULER_QUEUED_DEPS", "kind": "Gdef"}, "SENSITIVE_FIELD_PLACEHOLDER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SENSITIVE_FIELD_PLACEHOLDER", "name": "SENSITIVE_FIELD_PLACEHOLDER", "type": "builtins.str"}}, "SchedulerJobRunner": {".class": "SymbolTableNode", "cross_ref": "airflow.jobs.scheduler_job_runner.SchedulerJobRunner", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "SerializedDagModel": {".class": "SymbolTableNode", "cross_ref": "airflow.models.serialized_dag.SerializedDagModel", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.Session", "name": "Session", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}}}, "SlaMiss": {".class": "SymbolTableNode", "cross_ref": "airflow.models.slamiss.SlaMiss", "kind": "Gdef"}, "SlaMissModelView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowModelView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.SlaMissModelView", "name": "SlaMissModelView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.SlaMissModelView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.SlaMissModelView", "airflow.www.views.AirflowModelView", "builtins.object"], "names": {".class": "SymbolTable", "_set_notification_property": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "items", "attr", "new_value", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.SlaMissModelView._set_notification_property", "name": "_set_notification_property", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "items", "attr", "new_value", "session"], "arg_types": ["airflow.www.views.SlaMissModelView", {".class": "Instance", "args": ["airflow.models.slamiss.SlaMiss"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.bool", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_notification_property of SlaMissModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.SlaMissModelView._set_notification_property", "name": "_set_notification_property", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "items", "attr", "new_value", "session"], "arg_types": ["airflow.www.views.SlaMissModelView", {".class": "Instance", "args": ["airflow.models.slamiss.SlaMiss"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.bool", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_notification_property of SlaMissModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "action_muldelete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.SlaMissModelView.action_muldelete", "name": "action_muldelete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.SlaMissModelView.action_muldelete", "name": "action_muldelete", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_mulemailsent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.SlaMissModelView.action_mulemailsent", "name": "action_mulemailsent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "items"], "arg_types": ["airflow.www.views.SlaMissModelView", {".class": "Instance", "args": ["airflow.models.slamiss.SlaMiss"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "action_mulemailsent of SlaMissModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.SlaMissModelView.action_mulemailsent", "name": "action_mulemailsent", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_mulemailsentfalse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.SlaMissModelView.action_mulemailsentfalse", "name": "action_mulemailsentfalse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "items"], "arg_types": ["airflow.www.views.SlaMissModelView", {".class": "Instance", "args": ["airflow.models.slamiss.SlaMiss"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "action_mulemailsent<PERSON>lse of SlaMissModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.SlaMissModelView.action_mulemailsentfalse", "name": "action_mulemailsentfalse", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_mulnotificationsent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.SlaMissModelView.action_mulnotificationsent", "name": "action_mulnotificationsent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "items"], "arg_types": ["airflow.www.views.SlaMissModelView", {".class": "Instance", "args": ["airflow.models.slamiss.SlaMiss"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "action_mulnotificationsent of SlaMissModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.SlaMissModelView.action_mulnotificationsent", "name": "action_mulnotificationsent", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_mulnotificationsentfalse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.SlaMissModelView.action_mulnotificationsentfalse", "name": "action_mulnotificationsentfalse", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "items"], "arg_types": ["airflow.www.views.SlaMissModelView", {".class": "Instance", "args": ["airflow.models.slamiss.SlaMiss"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "action_mulnotificationsentfalse of SlaMissModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.SlaMissModelView.action_mulnotificationsentfalse", "name": "action_mulnotificationsentfalse", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "add_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SlaMissModelView.add_columns", "name": "add_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "base_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SlaMissModelView.base_filters", "name": "base_filters", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "base_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SlaMissModelView.base_order", "name": "base_order", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SlaMissModelView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SlaMissModelView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "datamodel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SlaMissModelView.datamodel", "name": "datamodel", "type": "airflow.www.utils.CustomSQLAInterface"}}, "edit_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SlaMissModelView.edit_columns", "name": "edit_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "formatters_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SlaMissModelView.formatters_columns", "name": "formatters_columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "label_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SlaMissModelView.label_columns", "name": "label_columns", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SlaMissModelView.list_columns", "name": "list_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "method_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SlaMissModelView.method_permission_name", "name": "method_permission_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SlaMissModelView.route_base", "name": "route_base", "type": "builtins.str"}}, "search_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.SlaMissModelView.search_columns", "name": "search_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.SlaMissModelView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.SlaMissModelView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "State": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.state.State", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TaskFail": {".class": "SymbolTableNode", "cross_ref": "airflow.models.taskfail.TaskFail", "kind": "Gdef"}, "TaskGroup": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.task_group.TaskGroup", "kind": "Gdef"}, "TaskInstance": {".class": "SymbolTableNode", "cross_ref": "airflow.models.taskinstance.TaskInstance", "kind": "Gdef"}, "TaskInstanceEditForm": {".class": "SymbolTableNode", "cross_ref": "airflow.www.forms.TaskInstanceEditForm", "kind": "Gdef"}, "TaskInstanceModelView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowModelView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.TaskInstanceModelView", "name": "TaskInstanceModelView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.TaskInstanceModelView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.TaskInstanceModelView", "airflow.www.views.AirflowModelView", "builtins.object"], "names": {".class": "SymbolTable", "_clear_task_instances": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "task_instances", "session", "clear_downstream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.TaskInstanceModelView._clear_task_instances", "name": "_clear_task_instances", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "task_instances", "session", "clear_downstream"], "arg_types": ["airflow.www.views.TaskInstanceModelView", {".class": "Instance", "args": ["airflow.models.taskinstance.TaskInstance"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clear_task_instances of TaskInstanceModelView", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "action_clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "task_instances", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.TaskInstanceModelView.action_clear", "name": "action_clear", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "task_instances", "session"], "arg_types": ["airflow.www.views.TaskInstanceModelView", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "action_clear of TaskInstanceModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.TaskInstanceModelView.action_clear", "name": "action_clear", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_clear_downstream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "task_instances", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.TaskInstanceModelView.action_clear_downstream", "name": "action_clear_downstream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "task_instances", "session"], "arg_types": ["airflow.www.views.TaskInstanceModelView", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "action_clear_downstream of TaskInstanceModelView", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.TaskInstanceModelView.action_clear_downstream", "name": "action_clear_downstream", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_muldelete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.TaskInstanceModelView.action_muldelete", "name": "action_muldelete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.TaskInstanceModelView.action_muldelete", "name": "action_muldelete", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_set_failed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.TaskInstanceModelView.action_set_failed", "name": "action_set_failed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.TaskInstanceModelView.action_set_failed", "name": "action_set_failed", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_set_retry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.TaskInstanceModelView.action_set_retry", "name": "action_set_retry", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.TaskInstanceModelView.action_set_retry", "name": "action_set_retry", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_set_skipped": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.TaskInstanceModelView.action_set_skipped", "name": "action_set_skipped", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.TaskInstanceModelView.action_set_skipped", "name": "action_set_skipped", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_set_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tis"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.TaskInstanceModelView.action_set_success", "name": "action_set_success", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.TaskInstanceModelView.action_set_success", "name": "action_set_success", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "add_exclude_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.add_exclude_columns", "name": "add_exclude_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "base_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.base_filters", "name": "base_filters", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "base_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.base_order", "name": "base_order", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "datamodel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.datamodel", "name": "datamodel", "type": "airflow.www.utils.CustomSQLAInterface"}}, "duration_f": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.TaskInstanceModelView.duration_f", "name": "duration_f", "type": null}}, "edit_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.edit_columns", "name": "edit_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "edit_form": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.edit_form", "name": "edit_form", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "airflow.www.forms.TaskInstanceEditForm", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "formatters_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.formatters_columns", "name": "formatters_columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "label_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.label_columns", "name": "label_columns", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.list_columns", "name": "list_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "log_url_formatter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.TaskInstanceModelView.log_url_formatter", "name": "log_url_formatter", "type": null}}, "method_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.method_permission_name", "name": "method_permission_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "order_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.order_columns", "name": "order_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "page_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.page_size", "name": "page_size", "type": "builtins.int"}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.route_base", "name": "route_base", "type": "builtins.str"}}, "search_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskInstanceModelView.search_columns", "name": "search_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "set_task_instance_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "tis", "target_state", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.TaskInstanceModelView.set_task_instance_state", "name": "set_task_instance_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "tis", "target_state", "session"], "arg_types": ["airflow.www.views.TaskInstanceModelView", {".class": "Instance", "args": ["airflow.models.taskinstance.TaskInstance"], "extra_attrs": null, "type_ref": "typing.Collection"}, "airflow.utils.state.TaskInstanceState", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_task_instance_state of TaskInstanceModelView", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.TaskInstanceModelView.set_task_instance_state", "name": "set_task_instance_state", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "tis", "target_state", "session"], "arg_types": ["airflow.www.views.TaskInstanceModelView", {".class": "Instance", "args": ["airflow.models.taskinstance.TaskInstance"], "extra_attrs": null, "type_ref": "typing.Collection"}, "airflow.utils.state.TaskInstanceState", {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_task_instance_state of TaskInstanceModelView", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.TaskInstanceModelView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.TaskInstanceModelView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TaskInstanceNote": {".class": "SymbolTableNode", "cross_ref": "airflow.models.taskinstance.TaskInstanceNote", "kind": "Gdef"}, "TaskInstanceState": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.state.TaskInstanceState", "kind": "Gdef"}, "TaskLogReader": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.log.log_reader.TaskLogReader", "kind": "Gdef"}, "TaskRescheduleModelView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowModelView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.TaskRescheduleModelView", "name": "TaskRescheduleModelView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.TaskRescheduleModelView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.TaskRescheduleModelView", "airflow.www.views.AirflowModelView", "builtins.object"], "names": {".class": "SymbolTable", "base_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskRescheduleModelView.base_filters", "name": "base_filters", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "base_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskRescheduleModelView.base_order", "name": "base_order", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskRescheduleModelView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskRescheduleModelView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "datamodel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskRescheduleModelView.datamodel", "name": "datamodel", "type": "airflow.www.utils.CustomSQLAInterface"}}, "duration_f": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.TaskRescheduleModelView.duration_f", "name": "duration_f", "type": null}}, "formatters_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskRescheduleModelView.formatters_columns", "name": "formatters_columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "label_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskRescheduleModelView.label_columns", "name": "label_columns", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskRescheduleModelView.list_columns", "name": "list_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "method_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskRescheduleModelView.method_permission_name", "name": "method_permission_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "related_views": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskRescheduleModelView.related_views", "name": "related_views", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DagRunModelView", "ret_type": "airflow.www.views.DagRunModelView", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskRescheduleModelView.route_base", "name": "route_base", "type": "builtins.str"}}, "search_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TaskRescheduleModelView.search_columns", "name": "search_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.TaskRescheduleModelView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.TaskRescheduleModelView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimeRestriction": {".class": "SymbolTableNode", "cross_ref": "airflow.timetables.base.TimeRestriction", "kind": "Gdef"}, "Trigger": {".class": "SymbolTableNode", "cross_ref": "airflow.models.trigger.Trigger", "kind": "Gdef"}, "TriggerModelView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowModelView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.TriggerModelView", "name": "TriggerM<PERSON>lView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.TriggerModelView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.TriggerModelView", "airflow.www.views.AirflowModelView", "builtins.object"], "names": {".class": "SymbolTable", "base_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TriggerModelView.base_order", "name": "base_order", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TriggerModelView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TriggerModelView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "datamodel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TriggerModelView.datamodel", "name": "datamodel", "type": "airflow.www.utils.CustomSQLAInterface"}}, "formatters_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TriggerModelView.formatters_columns", "name": "formatters_columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TriggerModelView.list_columns", "name": "list_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "method_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TriggerModelView.method_permission_name", "name": "method_permission_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TriggerModelView.route_base", "name": "route_base", "type": "builtins.str"}}, "search_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.TriggerModelView.search_columns", "name": "search_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.TriggerModelView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.TriggerModelView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TriggererJobRunner": {".class": "SymbolTableNode", "cross_ref": "airflow.jobs.triggerer_job_runner.TriggererJobRunner", "kind": "Gdef"}, "VariableModelView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowModelView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.VariableModelView", "name": "VariableModelView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.VariableModelView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.VariableModelView", "airflow.www.views.AirflowModelView", "builtins.object"], "names": {".class": "SymbolTable", "_show": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pk"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.VariableModelView._show", "name": "_show", "type": null}}, "action_muldelete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.VariableModelView.action_muldelete", "name": "action_muldelete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.VariableModelView.action_muldelete", "name": "action_muldelete", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "action_varexport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.VariableModelView.action_varexport", "name": "action_varexport", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.VariableModelView.action_varexport", "name": "action_varexport", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "add_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.add_columns", "name": "add_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "base_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.base_order", "name": "base_order", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "datamodel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.datamodel", "name": "datamodel", "type": "airflow.www.utils.CustomSQLAInterface"}}, "edit_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.edit_columns", "name": "edit_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "edit_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.edit_template", "name": "edit_template", "type": "builtins.str"}}, "extra_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.extra_args", "name": "extra_args", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<lambda>", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "formatters_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.formatters_columns", "name": "formatters_columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["airflow.www.views.VariableModelView"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hidden_field_formatter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "hidden_field_formatter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.VariableModelView.hidden_field_formatter", "name": "hidden_field_formatter", "type": null}}, "list_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.list_columns", "name": "list_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "list_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.list_template", "name": "list_template", "type": "builtins.str"}}, "method_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.method_permission_name", "name": "method_permission_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "prefill_form": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "form", "request_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.VariableModelView.prefill_form", "name": "prefill_form", "type": null}}, "prefill_show": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.VariableModelView.prefill_show", "name": "prefill_show", "type": null}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.route_base", "name": "route_base", "type": "builtins.str"}}, "search_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.search_columns", "name": "search_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "show_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.show_columns", "name": "show_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "show_template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.show_template", "name": "show_template", "type": "builtins.str"}}, "show_widget": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.show_widget", "name": "show_widget", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["_args", "_kwds"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "airflow.www.widgets.AirflowVariableShowWidget", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validators_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.VariableModelView.validators_columns", "name": "validators_columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "airflow.www.views.validators", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.validators", "source_any": null, "type_of_any": 3}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "varimport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.VariableModelView.varimport", "name": "varimport", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.VariableModelView.varimport", "name": "varimport", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.VariableModelView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.VariableModelView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "XCom": {".class": "SymbolTableNode", "cross_ref": "airflow.models.xcom.XCom", "kind": "Gdef"}, "XComModelView": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["airflow.www.views.AirflowModelView"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "airflow.www.views.XComModelView", "name": "XComModelView", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "airflow.www.views.XComModelView", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "airflow.www.views", "mro": ["airflow.www.views.XComModelView", "airflow.www.views.AirflowModelView", "builtins.object"], "names": {".class": "SymbolTable", "action_muldelete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "items"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "airflow.www.views.XComModelView.action_muldelete", "name": "action_muldelete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "airflow.www.views.XComModelView.action_muldelete", "name": "action_muldelete", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "base_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.XComModelView.base_filters", "name": "base_filters", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "base_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.XComModelView.base_order", "name": "base_order", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "base_permissions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.XComModelView.base_permissions", "name": "base_permissions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "class_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.XComModelView.class_permission_name", "name": "class_permission_name", "type": "builtins.str"}}, "datamodel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.XComModelView.datamodel", "name": "datamodel", "type": "airflow.www.utils.CustomSQLAInterface"}}, "formatters_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.XComModelView.formatters_columns", "name": "formatters_columns", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "list_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.XComModelView.list_columns", "name": "list_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "list_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.XComModelView.list_title", "name": "list_title", "type": "builtins.str"}}, "method_permission_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.XComModelView.method_permission_name", "name": "method_permission_name", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "pre_add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.XComModelView.pre_add", "name": "pre_add", "type": null}}, "pre_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "item"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.XComModelView.pre_update", "name": "pre_update", "type": null}}, "route_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.XComModelView.route_base", "name": "route_base", "type": "builtins.str"}}, "search_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views.XComModelView.search_columns", "name": "search_columns", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "airflow.www.views.XComModelView.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "airflow.www.views.XComModelView", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WHATWG_C0_CONTROL_OR_SPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "airflow.www.views._WHATWG_C0_CONTROL_OR_SPACE", "name": "_WHATWG_C0_CONTROL_OR_SPACE", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.views.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.views.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.views.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.views.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.views.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "airflow.www.views.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_safe_parse_datetime": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["v", "allow_empty", "strict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views._safe_parse_datetime", "name": "_safe_parse_datetime", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["v", "allow_empty", "strict"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_safe_parse_datetime", "ret_type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abort": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.abort", "kind": "Gdef"}, "action": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.action", "name": "action", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.action", "source_any": null, "type_of_any": 3}}}, "action_logging": {".class": "SymbolTableNode", "cross_ref": "airflow.www.decorators.action_logging", "kind": "Gdef"}, "add_user_permissions_to_dag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["sender", "template", "context", "extra"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.add_user_permissions_to_dag", "name": "add_user_permissions_to_dag", "type": null}}, "airflow": {".class": "SymbolTableNode", "cross_ref": "airflow", "kind": "Gdef"}, "and_": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.and_", "name": "and_", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.and_", "source_any": null, "type_of_any": 3}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "as_unicode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.as_unicode", "name": "as_unicode", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.as_unicode", "source_any": null, "type_of_any": 3}}}, "auth": {".class": "SymbolTableNode", "cross_ref": "airflow.www.auth", "kind": "Gdef"}, "before_render_template": {".class": "SymbolTableNode", "cross_ref": "flask.signals.before_render_template", "kind": "Gdef"}, "cache": {".class": "SymbolTableNode", "cross_ref": "airflow.compat.functools.cache", "kind": "Gdef"}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "functools.cached_property", "kind": "Gdef"}, "case": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.case", "name": "case", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.case", "source_any": null, "type_of_any": 3}}}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "conf": {".class": "SymbolTableNode", "cross_ref": "airflow.configuration.conf", "kind": "Gdef"}, "configupdater": {".class": "SymbolTableNode", "cross_ref": "configupdater", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "create_connection_form_class": {".class": "SymbolTableNode", "cross_ref": "airflow.www.forms.create_connection_form_class", "kind": "Gdef"}, "create_session": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.session.create_session", "kind": "Gdef"}, "croniter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.croniter", "name": "croniter", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.croniter", "source_any": null, "type_of_any": 3}}}, "dag_edges": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.dag_edges.dag_edges", "kind": "Gdef"}, "dag_to_grid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["dag", "dag_runs", "session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.dag_to_grid", "name": "dag_to_grid", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["dag", "dag_runs", "session"], "arg_types": ["airflow.models.dag.DagModel", {".class": "Instance", "args": ["airflow.models.dagrun.DagRun"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "AnyType", "missing_import_name": "airflow.www.views.Session", "source_any": null, "type_of_any": 3}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dag_to_grid", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "desc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.desc", "name": "desc", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.desc", "source_any": null, "type_of_any": 3}}}, "errors": {".class": "SymbolTableNode", "cross_ref": "airflow.models.errors", "kind": "Gdef"}, "escape": {".class": "SymbolTableNode", "cross_ref": "markupsafe._speedups.escape", "kind": "Gdef"}, "exactly_one": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.helpers.exactly_one", "kind": "Gdef"}, "expose": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.expose", "name": "expose", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.expose", "source_any": null, "type_of_any": 3}}}, "flash": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.flash", "kind": "Gdef"}, "flask": {".class": "SymbolTableNode", "cross_ref": "flask", "kind": "Gdef"}, "flask_session": {".class": "SymbolTableNode", "cross_ref": "flask.globals.session", "kind": "Gdef"}, "func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.func", "name": "func", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.func", "source_any": null, "type_of_any": 3}}}, "g": {".class": "SymbolTableNode", "cross_ref": "flask.globals.g", "kind": "Gdef"}, "get_airflow_app": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.airflow_flask_app.get_airflow_app", "kind": "Gdef"}, "get_airflow_health": {".class": "SymbolTableNode", "cross_ref": "airflow.api.common.airflow_health.get_airflow_health", "kind": "Gdef"}, "get_auth_manager": {".class": "SymbolTableNode", "cross_ref": "airflow.www.extensions.init_auth_manager.get_auth_manager", "kind": "Gdef"}, "get_dataset_triggered_next_run_info": {".class": "SymbolTableNode", "cross_ref": "airflow.models.dag.get_dataset_triggered_next_run_info", "kind": "Gdef"}, "get_date_time_num_runs_dag_runs_form_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["www_request", "session", "dag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.get_date_time_num_runs_dag_runs_form_data", "name": "get_date_time_num_runs_dag_runs_form_data", "type": null}}, "get_doc_url_for_provider": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.docs.get_doc_url_for_provider", "kind": "Gdef"}, "get_docs_url": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.docs.get_docs_url", "kind": "Gdef"}, "get_hostname": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.net.get_hostname", "kind": "Gdef"}, "get_key_paths": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["input_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.get_key_paths", "name": "get_key_paths", "type": null}}, "get_order_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.get_order_args", "name": "get_order_args", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.get_order_args", "source_any": null, "type_of_any": 3}}}, "get_page_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.get_page_args", "name": "get_page_args", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.get_page_args", "source_any": null, "type_of_any": 3}}}, "get_page_size_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.get_page_size_args", "name": "get_page_size_args", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.get_page_size_args", "source_any": null, "type_of_any": 3}}}, "get_query_count": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.db.get_query_count", "kind": "Gdef"}, "get_safe_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.get_safe_url", "name": "get_safe_url", "type": null}}, "get_task_stats_from_query": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["qry"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.get_task_stats_from_query", "name": "get_task_stats_from_query", "type": null}}, "get_value_from_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["key_path", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.get_value_from_path", "name": "get_value_from_path", "type": null}}, "gzipped": {".class": "SymbolTableNode", "cross_ref": "airflow.www.decorators.gzipped", "kind": "Gdef"}, "has_request_context": {".class": "SymbolTableNode", "cross_ref": "flask.ctx.has_request_context", "kind": "Gdef"}, "htmlsafe_json_dumps": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.htmlsafe_json_dumps", "kind": "Gdef"}, "infer_time_unit": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.dates.infer_time_unit", "kind": "Gdef"}, "insort_left": {".class": "SymbolTableNode", "cross_ref": "_bisect.insort_left", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.inspect", "name": "inspect", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.inspect", "source_any": null, "type_of_any": 3}}}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "joinedload": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.joinedload", "name": "joinedload", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.joinedload", "source_any": null, "type_of_any": 3}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "lazy_gettext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.lazy_gettext", "name": "lazy_gettext", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.lazy_gettext", "source_any": null, "type_of_any": 3}}}, "lazy_object_proxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.lazy_object_proxy", "name": "lazy_object_proxy", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.lazy_object_proxy", "source_any": null, "type_of_any": 3}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "make_response": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.make_response", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "method_not_allowed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.method_not_allowed", "name": "method_not_allowed", "type": null}}, "models": {".class": "SymbolTableNode", "cross_ref": "airflow.models", "kind": "Gdef"}, "needs_expansion": {".class": "SymbolTableNode", "cross_ref": "airflow.models.operator.needs_expansion", "kind": "Gdef"}, "node_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["node_id", "label", "node_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.node_dict", "name": "node_dict", "type": null}}, "not_found": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.not_found", "name": "not_found", "type": null}}, "nvd3": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.nvd3", "name": "nvd3", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.nvd3", "source_any": null, "type_of_any": 3}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "permissions": {".class": "SymbolTableNode", "cross_ref": "airflow.security.permissions", "kind": "Gdef"}, "pformat": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.pformat", "kind": "Gdef"}, "plugins_manager": {".class": "SymbolTableNode", "cross_ref": "airflow.plugins_manager", "kind": "Gdef"}, "provide_session": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.session.provide_session", "kind": "Gdef"}, "re2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.re2", "name": "re2", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.re2", "source_any": null, "type_of_any": 3}}}, "redirect": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.redirect", "kind": "Gdef"}, "redirect_or_json": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["origin", "msg", "status", "status_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.redirect_or_json", "name": "redirect_or_json", "type": null}}, "render_template": {".class": "SymbolTableNode", "cross_ref": "flask.templating.render_template", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "restrict_to_dev": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.restrict_to_dev", "name": "restrict_to_dev", "type": null}}, "sanitize_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.sanitize_args", "name": "sanitize_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["args"], "arg_types": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sanitize_args", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scale_time_units": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.dates.scale_time_units", "kind": "Gdef"}, "secrets_masker": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.log.secrets_masker", "kind": "Gdef"}, "select": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.select", "name": "select", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.select", "source_any": null, "type_of_any": 3}}}, "send_from_directory": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.send_from_directory", "kind": "Gdef"}, "set_dag_run_state_to_failed": {".class": "SymbolTableNode", "cross_ref": "airflow.api.common.mark_tasks.set_dag_run_state_to_failed", "kind": "Gdef"}, "set_dag_run_state_to_queued": {".class": "SymbolTableNode", "cross_ref": "airflow.api.common.mark_tasks.set_dag_run_state_to_queued", "kind": "Gdef"}, "set_dag_run_state_to_success": {".class": "SymbolTableNode", "cross_ref": "airflow.api.common.mark_tasks.set_dag_run_state_to_success", "kind": "Gdef"}, "set_state": {".class": "SymbolTableNode", "cross_ref": "airflow.api.common.mark_tasks.set_state", "kind": "Gdef"}, "settings": {".class": "SymbolTableNode", "cross_ref": "airflow.settings", "kind": "Gdef"}, "show_traceback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "airflow.www.views.show_traceback", "name": "show_traceback", "type": null}}, "sqla": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.sqla", "name": "sqla", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.sqla", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "task_group_to_dict": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.task_group.task_group_to_dict", "kind": "Gdef"}, "td_format": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.timezone.td_format", "kind": "Gdef"}, "timezone": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.timezone", "kind": "Gdef"}, "to_boolean": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.strings.to_boolean", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "union_all": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.union_all", "name": "union_all", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.union_all", "source_any": null, "type_of_any": 3}}}, "unquote": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.unquote", "kind": "Gdef"}, "url_for": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.url_for", "kind": "Gdef"}, "urljoin": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urljoin", "kind": "Gdef"}, "urlsplit": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlsplit", "kind": "Gdef"}, "utcnow": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.timezone.utcnow", "kind": "Gdef"}, "utils_json": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.json", "kind": "Gdef"}, "validators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "airflow.www.views.validators", "name": "validators", "type": {".class": "AnyType", "missing_import_name": "airflow.www.views.validators", "source_any": null, "type_of_any": 3}}}, "version": {".class": "SymbolTableNode", "cross_ref": "airflow.__version__", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "wwwutils": {".class": "SymbolTableNode", "cross_ref": "airflow.www.utils", "kind": "Gdef"}, "yaml": {".class": "SymbolTableNode", "cross_ref": "airflow.utils.yaml", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/www/views.py"}