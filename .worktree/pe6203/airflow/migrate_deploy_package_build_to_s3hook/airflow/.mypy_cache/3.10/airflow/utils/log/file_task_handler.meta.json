{"data_mtime": 1756971054, "dep_lines": [39, 40, 36, 37, 38, 41, 42, 45, 30, 34, 35, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 32, 562, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 25, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["airflow.utils.log.logging_mixin", "airflow.utils.log.non_caching_file_handler", "airflow.executors.executor_loader", "airflow.utils.context", "airflow.utils.helpers", "airflow.utils.session", "airflow.utils.state", "airflow.models.taskinstance", "urllib.parse", "airflow.configuration", "airflow.exceptions", "__future__", "inspect", "logging", "os", "warnings", "contextlib", "enum", "functools", "pathlib", "typing", "pendulum", "httpx", "builtins", "_frozen_importlib", "abc", "airflow.models", "airflow.models.taskinstancekey", "configparser"], "hash": "0e6b56e23ac4dd5479bba720df4263f3b5b26e0c", "id": "airflow.utils.log.file_task_handler", "ignore_all": true, "interface_hash": "18584ffe36d1b92247752c8d7f690c11b22b7330", "mtime": 1743589731, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/.venv/lib/python3.10/site-packages/airflow/utils/log/file_task_handler.py", "plugin_data": null, "size": 25059, "suppressed": [], "version_id": "1.15.0"}