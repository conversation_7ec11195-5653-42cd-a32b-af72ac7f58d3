{".class": "MypyFile", "_fullname": "aetion.adip.airflow.integrations.s3.s3_support", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AetionS3FileSystem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "name": "AetionS3FileSystem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "aetion.adip.airflow.integrations.s3.s3_support", "mro": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "aws_conn_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "aws_conn_id"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AetionS3FileSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_hook": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem._hook", "name": "_hook", "type": {".class": "UnionType", "items": ["airflow.providers.amazon.aws.hooks.s3.S3Hook", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_is_s3_root": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem._is_s3_root", "name": "_is_s3_root", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_s3_root of AetionS3FileSystem", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_s3_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem._parse_s3_path", "name": "_parse_s3_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_s3_path of AetionS3FileSystem", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "aws_conn_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.aws_conn_id", "name": "aws_conn_id", "type": "builtins.str"}}, "cat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path", "as_bytes", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.cat", "name": "cat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path", "as_bytes", "encoding"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.bool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cat of AetionS3FileSystem", "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_files_ready": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "source_path", "tables_count", "default_partitions_count", "partitions_counts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.check_files_ready", "name": "check_files_ready", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "source_path", "tables_count", "default_partitions_count", "partitions_counts"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.int", "builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_files_ready of AetionS3FileSystem", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.client", "name": "client", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.client", "name": "client", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "client of AetionS3FileSystem", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "copy_basic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "src", "dst"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.copy_basic", "name": "copy_basic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "src", "dst"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_basic of AetionS3FileSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_basic_directory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_path", "target_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.copy_basic_directory", "name": "copy_basic_directory", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_path", "target_path"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_basic_directory of AetionS3FileSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_files_parallel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "source_path", "target_path", "files", "copy_parallelism"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.copy_files_parallel", "name": "copy_files_parallel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "source_path", "target_path", "files", "copy_parallelism"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_files_parallel of AetionS3FileSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_files_parallel_sync": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "source_path", "target_path", "files", "copy_parallelism"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.copy_files_parallel_sync", "name": "copy_files_parallel_sync", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "source_path", "target_path", "files", "copy_parallelism"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_files_parallel_sync of AetionS3FileSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_with_pattern_parallel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "source_path", "target_path", "pattern", "copy_parallelism"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.copy_with_pattern_parallel", "name": "copy_with_pattern_parallel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "source_path", "target_path", "pattern", "copy_parallelism"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy_with_pattern_parallel of AetionS3FileSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "download_files_parallel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "source_path", "target_path", "files", "copy_parallelism"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.download_files_parallel", "name": "download_files_parallel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "source_path", "target_path", "files", "copy_parallelism"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "download_files_parallel of AetionS3FileSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "download_files_parallel_sync": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "source_path", "target_path", "files", "copy_parallelism"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.download_files_parallel_sync", "name": "download_files_parallel_sync", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "source_path", "target_path", "files", "copy_parallelism"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "download_files_parallel_sync of AetionS3FileSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exists": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.exists", "name": "exists", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exists of AetionS3FileSystem", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exists_folder": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.exists_folder", "name": "exists_folder", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exists_folder of AetionS3FileSystem", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_next_revision": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_path", "prev_revision"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.find_next_revision", "name": "find_next_revision", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "source_path", "prev_revision"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_next_revision of AetionS3FileSystem", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rpath", "lpath", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "rpath", "lpath", "recursive"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of AetionS3FileSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.hook", "name": "hook", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hook of AetionS3FileSystem", "ret_type": "airflow.providers.amazon.aws.hooks.s3.S3Hook", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.hook", "name": "hook", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hook of AetionS3FileSystem", "ret_type": "airflow.providers.amazon.aws.hooks.s3.S3Hook", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of AetionS3FileSystem", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "listdir": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "path", "detail"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.listdir", "name": "listdir", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "path", "detail"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "listdir of AetionS3FileSystem", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path", "detail", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.ls", "name": "ls", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path", "detail", "recursive"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ls of AetionS3FileSystem", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "open": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path", "mode", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path", "mode", "encoding"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open of AetionS3FileSystem", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "path", "mode", "encoding"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open of AetionS3FileSystem", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.put", "name": "put", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "data", "path"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", {".class": "UnionType", "items": ["builtins.bytes", "builtins.str", "_io.BytesIO"], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put of AetionS3FileSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_file_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.read_file_content", "name": "read_file_content", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_file_content of AetionS3FileSystem", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "path", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.rm", "name": "rm", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "path", "recursive"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rm of AetionS3FileSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "split_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.split_path", "name": "split_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "split_path of AetionS3FileSystem", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "local_path", "s3_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.upload", "name": "upload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "local_path", "s3_path"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upload of AetionS3FileSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upload_dir_parallel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "local_dir", "s3_prefix", "max_workers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.upload_dir_parallel", "name": "upload_dir_parallel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "local_dir", "s3_prefix", "max_workers"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "upload_dir_parallel of AetionS3FileSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "walk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "path", "maxdepth", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.walk", "name": "walk", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "path", "maxdepth", "kwargs"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "walk of AetionS3FileSystem", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "walk_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.walk_all", "name": "walk_all", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "walk_all of AetionS3FileSystem", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int", "datetime.datetime"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "aetion.adip.airflow.integrations.s3.s3_support.AetionS3FileSystem", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "S3Hook": {".class": "SymbolTableNode", "cross_ref": "airflow.providers.amazon.aws.hooks.s3.S3Hook", "kind": "Gdef"}, "S3Location": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location", "name": "S3Location", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["bucket", "key"]}}, "module_name": "aetion.adip.airflow.integrations.s3.s3_support", "mro": ["aetion.adip.airflow.integrations.s3.s3_support.S3Location", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "bucket"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "bucket", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "bucket", "key"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of S3Location", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of S3Location", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of S3Location", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of S3Location", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "bucket", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "bucket", "key"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of S3Location", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._NT", "id": -1, "name": "_NT", "namespace": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location._source", "name": "_source", "type": "builtins.str"}}, "bucket": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location.bucket", "name": "bucket", "type": "builtins.str"}}, "bucket-redefinition": {".class": "SymbolTableNode", "cross_ref": "aetion.adip.airflow.integrations.s3.s3_support.S3Location.bucket", "kind": "<PERSON><PERSON><PERSON>"}, "key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location.key", "name": "key", "type": "builtins.str"}}, "key-redefinition": {".class": "SymbolTableNode", "cross_ref": "aetion.adip.airflow.integrations.s3.s3_support.S3Location.key", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "aetion.adip.airflow.integrations.s3.s3_support.S3Location.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": "aetion.adip.airflow.integrations.s3.s3_support.S3Location"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "StreamingBody": {".class": "SymbolTableNode", "cross_ref": "botocore.response.StreamingBody", "kind": "Gdef"}, "StringIO": {".class": "SymbolTableNode", "cross_ref": "_io.StringIO", "kind": "Gdef"}, "TextIOWrapper": {".class": "SymbolTableNode", "cross_ref": "_io.TextIOWrapper", "kind": "Gdef"}, "ThreadPoolExecutor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures.thread.ThreadPoolExecutor", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "as_completed": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.as_completed", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef"}, "glob": {".class": "SymbolTableNode", "cross_ref": "glob", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "parse_s3_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.parse_s3_url", "name": "parse_s3_url", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["url"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_s3_url", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "aetion.adip.airflow.integrations.s3.s3_support.S3Location"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "resolve_relative_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["full_key", "base_prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "aetion.adip.airflow.integrations.s3.s3_support.resolve_relative_key", "name": "resolve_relative_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["full_key", "base_prefix"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_relative_key", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}}, "path": "/Users/<USER>/projects/connector/.worktree/pe6203/airflow/migrate_deploy_package_build_to_s3hook/airflow/dags/aetion/adip/airflow/integrations/s3/s3_support.py"}