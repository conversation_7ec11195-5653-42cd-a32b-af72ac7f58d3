defaults:
  - /step_group_cluster_config/k8s_step_group_config@_here_
  - _self_
default:
  autoscale:
    enabled: "true"
    initial_executors: 1
    max_executors: 20
    min_executors: 1
  driver_conf:
    instance_type: "r5a.4xlarge"
    pyspark_memory_overhead: "26500m"
  spark_conf:
    aetion.dataset.automaticNdcFormatting: "false"
    spark.decommission.enabled: "true"
    spark.driver.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
    spark.driver.maxResultSize: "0"
    spark.eventLog.enabled: "true"
    spark.executor.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
    spark.hadoop.fs.s3a.connection.maximum: 300
    spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
    spark.hadoop.fs.s3a.threads.max: 200
    spark.logConf: "true"
    spark.memory.offHeap.enabled: "false"
    spark.shuffle.io.maxRetries: 12
    spark.shuffle.io.numConnectionsPerPeer: 8
    spark.sql.adaptive.coalescePartitions.enabled: "true"
    spark.sql.adaptive.enabled: "true"
    spark.sql.adaptive.localShuffleReader.enabled: "true"
    spark.sql.adaptive.skewJoin.enabled: "true"
    spark.sql.legacy.timeParserPolicy: "LEGACY"
    spark.sql.parquet.binaryAsString: "true"
    spark.sql.shuffle.partitions: 2000
    spark.storage.decommission.enabled: "true"
    spark.storage.decommission.rddBlocks.enabled: "true"
    spark.storage.decommission.shuffleBlocks.enabled: "true"
    spark.storage.decommission.shuffleBlocks.maxThreads: 32
  worker_conf:
    instance_type: "c5.4xlarge"
full_job:
  autoscale:
    enabled: "true"
    initial_executors: 1
    max_executors: 20
    min_executors: 1
  driver_conf:
    instance_type: "r5a.8xlarge"
    pyspark_memory_overhead: "53000m"
  spark_conf:
    spark.dynamicAllocation.cachedExecutorIdleTimeout: "30m"
    spark.dynamicAllocation.executorIdleTimeout: "15m"
    spark.memory.storageFraction: 0.005
    spark.sql.catalog.default: "my_catalog"
    spark.sql.catalog.my_catalog.type: "hadoop"
    spark.sql.catalog.my_catalog.warehouse: "s3a://adn.aetion.com/etl/marketscan/**********/full-shard/iceberg/"
    spark.sql.parquet.columnarReaderBatchSize: 256
    spark.sql.shuffle.partitions: 6000
  worker_conf:
    instance_type: "r5a.4xlarge"
    pyspark_memory_overhead: "26000m"
full_patient_job:
  autoscale:
    enabled: "true"
    initial_executors: 1
    max_executors: 20
    min_executors: 1
  driver_conf:
    instance_type: "r5a.8xlarge"
    pyspark_memory_overhead: "53000m"
  spark_conf:
    spark.dynamicAllocation.cachedExecutorIdleTimeout: "30m"
    spark.dynamicAllocation.executorIdleTimeout: "15m"
    spark.memory.storageFraction: 0.005
    spark.sql.parquet.columnarReaderBatchSize: 256
  worker_conf:
    instance_type: "r5a.4xlarge"
    pyspark_memory_overhead: "26000m"
full_shard_job:
  autoscale:
    enabled: "true"
    initial_executors: 1
    max_executors: 20
    min_executors: 1
  driver_conf:
    instance_type: "c5.9xlarge"
  spark_conf:
    spark.dynamicAllocation.cachedExecutorIdleTimeout: "30m"
    spark.dynamicAllocation.executorIdleTimeout: "15m"
    spark.hadoop.fs.s3a.connection.maximum: 500
    spark.hadoop.fs.s3a.endpoint: "s3.amazonaws.com"
    spark.hadoop.fs.s3a.impl: "org.apache.hadoop.fs.s3a.S3AFileSystem"
    spark.hadoop.fs.s3a.threads.max: 256
    spark.memory.offHeap.enabled: "false"
    spark.sql.adaptive.coalescePartitions.enabled: "false"
    spark.task.cpus: 2
  worker_conf:
    instance_type: "c5.9xlarge"
  hadoop_conf:
    fs.s3a.connection.maximum: 500
    fs.s3a.threads.max: 256
