defaults:
  - /step_group_cluster_config/databricks_step_group_config@_here_
  - _self_
default:
  autoscale:
    max_workers: 20
    min_workers: 1
  aws_attributes:
    availability: "SPOT"
    first_on_demand: "1"
    spot_bid_price_percent: "65"
    zone_id: "auto"
  driver_node_type_id: "r5a.4xlarge"
  enable_elastic_disk: "true"
  node_type_id: "i3.2xlarge"
  spark_conf:
    aetion.dataset.automaticNdcFormatting: "false"
    spark.databricks.adaptive.autoOptimizeShuffle.enabled: "false"
    spark.databricks.io.cache.enabled: "false"
    spark.decommission.enabled: "true"
    spark.driver.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
    spark.driver.maxResultSize: "20g"
    spark.executor.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
    spark.hadoop.fs.s3a.connection.maximum: 300
    spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
    spark.hadoop.fs.s3a.threads.max: 200
    spark.memory.offHeap.enabled: "false"
    spark.shuffle.io.maxRetries: 12
    spark.shuffle.io.numConnectionsPerPeer: 8
    spark.sql.adaptive.coalescePartitions.enabled: "true"
    spark.sql.adaptive.enabled: "true"
    spark.sql.adaptive.localShuffleReader.enabled: "true"
    spark.sql.adaptive.skewJoin.enabled: "true"
    spark.sql.legacy.timeParserPolicy: "LEGACY"
    spark.sql.parquet.binaryAsString: "true"
    spark.sql.shuffle.partitions: 2000
    spark.storage.decommission.enabled: "true"
    spark.storage.decommission.rddBlocks.enabled: "true"
    spark.storage.decommission.shuffleBlocks.enabled: "true"
    spark.storage.decommission.shuffleBlocks.maxThreads: 32
full_job:
  autoscale:
    max_workers: 20
    min_workers: 1
  aws_attributes:
    availability: "SPOT"
    first_on_demand: "1"
    spot_bid_price_percent: "65"
    zone_id: "auto"
  driver_node_type_id: "r5a.4xlarge"
  enable_elastic_disk: "true"
  node_type_id: "i3.2xlarge"
  spark_conf:
    spark.sql.shuffle.partitions: 6000
full_patient_job:
  autoscale:
    max_workers: 20
    min_workers: 1
  aws_attributes:
    availability: "SPOT"
    first_on_demand: "1"
    spot_bid_price_percent: "65"
    zone_id: "auto"
  driver_node_type_id: "r5a.4xlarge"
  enable_elastic_disk: "true"
  node_type_id: "i3.2xlarge"
  spark_conf:
    spark.memory.storageFraction: 0.005
    spark.sql.parquet.columnarReaderBatchSize: 256
full_shard_job:
  autoscale:
    max_workers: 30
    min_workers: 1
  aws_attributes:
    availability: "SPOT"
    first_on_demand: "1"
    spot_bid_price_percent: "65"
    zone_id: "auto"
  driver_node_type_id: "rg-fleet.4xlarge"
  enable_elastic_disk: "true"
  node_type_id: "rg-fleet.8xlarge"
  spark_conf:
    spark.hadoop.fs.s3a.connection.maximum: 500
    spark.hadoop.fs.s3a.threads.max: 256
    spark.memory.offHeap.enabled: "false"
    spark.sql.adaptive.coalescePartitions.enabled: "false"
    spark.task.cpus: 2
