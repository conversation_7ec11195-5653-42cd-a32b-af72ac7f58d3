defaults:
  - /step_group_cluster_config/k8s_step_group_config@_here_
  - _self_
default:
  autoscale:
    initial_executors: 1
    max_executors: 20
    min_executors: 1
  driver_conf:
    instance_type: "r5a.4xlarge"
  num_workers: 5
  spark_conf:
    spark.driver.maxResultSize: "20g"
    spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
    spark.network.timeout: 800
    spark.sql.adaptive.coalescePartitions.enabled: "true"
    spark.sql.adaptive.enabled: "true"
    spark.sql.adaptive.localShuffleReader.enabled: "true"
    spark.sql.adaptive.skewJoin.enabled: "true"
    spark.sql.legacy.timeParserPolicy: "LEGACY"
    spark.sql.parquet.binaryAsString: "true"
  worker_conf:
    instance_type: "r5a.2xlarge"
full_job:
  autoscale:
    initial_executors: 1
    max_executors: 20
    min_executors: 1
  driver_conf:
    instance_type: "r5a.4xlarge"
  num_workers: 5
  spark_conf:
    spark.sql.shuffle.partitions: 6000
  worker_conf:
    instance_type: "r5a.2xlarge"
full_patient_job:
  autoscale:
    initial_executors: 1
    max_executors: 20
    min_executors: 1
  driver_conf:
    instance_type: "r5a.4xlarge"
  num_workers: 5
  spark_conf:
    spark.memory.storageFraction: 0.005
    spark.sql.parquet.columnarReaderBatchSize: 256
  worker_conf:
    instance_type: "r5a.2xlarge"
full_shard_job:
  autoscale:
    initial_executors: 1
    max_executors: 30
    min_executors: 1
  driver_conf:
    instance_type: "r5a.4xlarge"
  num_workers: 5
  spark_conf:
    spark.hadoop.fs.s3a.connection.maximum: 500
    spark.hadoop.fs.s3a.threads.max: 256
    spark.memory.offHeap.enabled: "false"
    spark.sql.adaptive.coalescePartitions.enabled: "false"
    spark.task.cpus: 2
  worker_conf:
    instance_type: "r5a.4xlarge"
