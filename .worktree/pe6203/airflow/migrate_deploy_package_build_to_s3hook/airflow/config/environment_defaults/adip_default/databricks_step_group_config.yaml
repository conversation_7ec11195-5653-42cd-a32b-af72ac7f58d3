defaults:
  - /step_group_cluster_config/databricks_step_group_config@_here_
  - _self_
full_job:
  node_type_id: "${default_adip_overrides.databricks_job_cluster_node_type_id}"
  spark_conf:
    spark.sql.shuffle.partitions: 6000
full_patient_job:
  node_type_id: "${default_adip_overrides.databricks_job_cluster_node_type_id}"
  spark_conf:
    spark.sql.parquet.columnarReaderBatchSize: "256,"
    spark.memory.storageFraction: 0.005
full_shard_job:
  autoscale:
    max_workers: 30
    min_workers: 1
  driver_node_type_id: "rg-fleet.4xlarge"
  node_type_id: "rg-fleet.8xlarge"
  spark_conf:
    spark.sql.adaptive.coalescePartitions.enabled: False
    spark.memory.offHeap.enabled: False
    spark.hadoop.fs.s3a.connection.maximum: 500
    spark.hadoop.fs.s3a.threads.max: 256
    spark.task.cpus: 2
default:
  autoscale:
    max_workers: 20
    min_workers: 1
  aws_attributes:
    availability: "SPOT"
    first_on_demand: 1
    spot_bid_price_percent: 65
    zone_id: "${default_adip_overrides.databricks_job_cluster_zone_id}"
  driver_node_type_id: "r5a.4xlarge"
  enable_elastic_disk: True
  node_type_id: "${default_adip_overrides.databricks_job_cluster_node_type_id}"
  spark_conf:
    aetion.dataset.automaticNdcFormatting: False
    spark.databricks.adaptive.autoOptimizeShuffle.enabled: False
    spark.databricks.io.cache.enabled: False
    spark.decommission.enabled: True
    spark.driver.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
    spark.driver.maxResultSize: "20g"
    spark.executor.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
    spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
    spark.hadoop.fs.s3a.connection.maximum: 300
    spark.hadoop.fs.s3a.threads.max: 200
    spark.memory.offHeap.enabled: False
    spark.sql.adaptive.coalescePartitions.enabled: True
    spark.sql.adaptive.enabled: True
    spark.sql.adaptive.localShuffleReader.enabled: True
    spark.sql.adaptive.skewJoin.enabled: True
    spark.sql.legacy.timeParserPolicy: "LEGACY"
    spark.sql.parquet.binaryAsString: True
    spark.sql.shuffle.partitions: 2000
    spark.storage.decommission.enabled: True
    spark.storage.decommission.rddBlocks.enabled: True
    spark.storage.decommission.shuffleBlocks.enabled: True
    spark.storage.decommission.shuffleBlocks.maxThreads: 32
    spark.shuffle.io.maxRetries: 12
    spark.shuffle.io.numConnectionsPerPeer: 8
