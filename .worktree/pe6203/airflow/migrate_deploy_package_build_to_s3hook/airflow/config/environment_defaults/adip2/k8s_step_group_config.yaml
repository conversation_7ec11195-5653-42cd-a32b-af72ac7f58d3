defaults:
  - /step_group_cluster_config/k8s_step_group_config@_here_
  - /spark_cluster_config/cluster_config/worker_8c_60gb_driver_16c_120gb_autoscale_1to20@default
  - _self_
full_job:
  autoscale:
    initial_executors: 1
    max_executors: 20
    min_executors: 1
  driver_conf:
    instance_type: "r5a.4xlarge"
  num_workers: 5
  spark_conf:
    spark.sql.shuffle.partitions: 6000
  worker_conf:
    instance_type: "r5a.2xlarge"
full_patient_job:
  autoscale:
    initial_executors: 1
    max_executors: 20
    min_executors: 1
  driver_conf:
    instance_type: "r5a.4xlarge"
  num_workers: 5
  spark_conf:
    spark.memory.storageFraction: 0.005
    spark.sql.parquet.columnarReaderBatchSize: 256
  worker_conf:
    instance_type: "r5a.2xlarge"
full_shard_job:
  autoscale:
    initial_executors: 1
    max_executors: 30
    min_executors: 1
  driver_conf:
    instance_type: "r5a.4xlarge"
  num_workers: 5
  spark_conf:
    spark.hadoop.fs.s3a.connection.maximum: 500
    spark.hadoop.fs.s3a.threads.max: 256
    spark.memory.offHeap.enabled: "false"
    spark.sql.adaptive.coalescePartitions.enabled: "false"
    spark.task.cpus: 2
  worker_conf:
    instance_type: "r5a.4xlarge"
