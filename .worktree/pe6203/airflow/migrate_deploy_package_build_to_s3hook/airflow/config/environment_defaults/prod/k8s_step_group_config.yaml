defaults:
  - /step_group_cluster_config/k8s_step_group_config@_here_
  - /k8s_spark_config@default.spark_conf
  - _self_
default:
  autoscale:
    enabled: "true"
    initial_executors: 1
    max_executors: 20
    min_executors: 1
  driver_conf:
    instance_type: "r5a.4xlarge"
    spark_memory_overhead_factor: ".2"
  worker_conf:
    spark_memory_overhead_factor: ".2"
    instance_type: "c5.4xlarge"

full_job:
  autoscale:
    enabled: "true"
    initial_executors: 1
    max_executors: 20
    min_executors: 1
  driver_conf:
    instance_type: "r5a.4xlarge"
  spark_conf:
    spark.decommission.enabled: "false"
    spark.hadoop.fs.s3a.connection.maximum: 300
    spark.sql.shuffle.partitions: 6000
  worker_conf:
    instance_type: "c5.4xlarge"
full_patient_job:
  autoscale:
    enabled: "true"
    initial_executors: 1
    max_executors: 20
    min_executors: 1
  driver_conf:
    instance_type: "r5a.8xlarge"
  worker_conf:
    instance_type: "m6a.4xlarge"
full_shard_job:
  autoscale:
    enabled: "true"
    initial_executors: 1
    max_executors: 20
    min_executors: 1
  driver_conf:
    instance_type: "c5a.8xlarge"
  spark_conf:
    spark.hadoop.fs.s3a.connection.maximum: 500
    spark.hadoop.fs.s3a.threads.max: 256
    spark.memory.offHeap.enabled: "false"
    spark.sql.adaptive.coalescePartitions.enabled: "false"
    spark.task.cpus: 2
  worker_conf:
    instance_type: "c5a.16xlarge"
