## 🧩 Assigning Cluster Configs to Steps

Each step in your pipeline can be associated with a specific **step group**, some are already mapped by [default](../../../adip/adip_common/dags_configuration/default_adip_config/steps/data_ingestion/create_full_shard.yaml), and each step group can have a corresponding **cluster configuration** (e.g., driver/worker specs, autoscaling behavior, Spark settings).

There are two ways to assign custom cluster configurations, ([both implemented in this adip2 configuration file](../environments/adip2/pipeline/data_ingestion_adip2_marketscan_tiny_adm_k8s/data_ingestion_adip2_marketscan_tiny_adm_k8s.yaml)):

---

### ✅ Recommended: Import cluster config from existing predefined config groups and manually assign the step group

Use the `defaults:` list to import a predefined cluster configuration for a given step group.

```yaml
defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - /spark_cluster_config/cluster_config/worker_8c_60gb_driver_16c_120gb_autoscale_1to20@steps_spark_config.generate_full_enums_and_dictionaries_only
  - _self_
```
And also make sure you override the default step group assigning the new one matching the imported cluster config name:

```yaml
steps:
  generate_full_enums_and_dictionaries:
    step_group: "generate_full_enums_and_dictionaries_only"
```

In this example:
- The step `generate_full_enums_and_dictionaries` is assigned to the step group `generate_full_enums_and_dictionaries_only`.
- The corresponding cluster config is imported via:
  ```yaml
  - /spark_cluster_config/cluster_config/worker_8c_60gb_driver_16c_120gb_autoscale_1to20@steps_spark_config.generate_full_enums_and_dictionaries_only
  ```
- This ensures consistent and reusable resource configuration across projects and clients.

---

### ⚙️ Alternative: Inline the cluster configuration directly

You can also define a custom configuration inline under `steps_spark_config`:

```yaml
steps_spark_config:
  unarchiver_only:
    autoscale:
      initial_executors: 1
      max_executors: 20
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
    worker_conf:
      instance_type: "r5a.2xlarge"
```

This approach allows you to define the driver/worker instance types and autoscaling behavior without importing from a central config, you **ALWAYS** need to route the step to the step_group:

```yaml
steps:
  unarchiver:
    step_group: "unarchiver_only"
```

---

### 🧠 Notes

- The `step_group` defined under each step (e.g. `unarchiver`, `generate_full_enums_and_dictionaries`) determines which config block is used from `steps_spark_config`.
- The **recommended approach** is to use the default import mechanism via `defaults:` to keep configurations DRY, reusable, and version-controlled.
- You may mix and match inline and imported configs if needed, avoid modifying the default steps in `/spark_cluster_config/cluster_config/`.
