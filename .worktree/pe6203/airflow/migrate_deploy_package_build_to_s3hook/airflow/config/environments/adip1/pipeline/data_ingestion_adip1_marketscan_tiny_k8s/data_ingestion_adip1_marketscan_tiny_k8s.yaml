defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "adip1"
dataset: "marketscan_tiny"
revision: "20240208"
tag: "default"
rdc_dbc_name: marketscan_tiny-default
upload_bucket: "s3://adip1.app.dev.aetion.com/upload/marketscan_tiny/20240208"
deployment_config:
  aep-dev: "adip1.app.dev.aetion.com"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2018-12-31"
  LAB_START_DATE: "2013-12-31"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
transform_path: "marketscan_tiny/adn/current"
validation_path: "marketscan_tiny/adn/current"
celeborn_feature:
  enabled: true
  spark_conf:
    spark.shuffle.manager: "org.apache.spark.shuffle.celeborn.SparkShuffleManager"
domain: "app.dev.aetion.com"
dynamic_flat_tables: false
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
is_k8s: true
sampled_data_percentage: "50"
service_account: "spark-operator-client-common"
skip_single_shard: true
source_files_password: ""
use_smart_sampling: false
