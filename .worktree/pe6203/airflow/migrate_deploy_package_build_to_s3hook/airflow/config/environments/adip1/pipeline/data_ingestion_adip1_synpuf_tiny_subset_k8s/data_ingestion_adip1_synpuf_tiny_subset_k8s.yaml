defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "adip1"
dataset: "synpuf_tiny_subset"
revision: "20221004"
tag: "default"
rdc_dbc_name: synpuf_tiny_subset-default
upload_bucket: "s3://adip1.app.dev.aetion.com/upload/synpuf_tiny_subset/20221004"
deployment_config:
  aep-dev: "adip1.app.dev.aetion.com"
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
transform_path: "synpuf_tiny_subset/current"
validation_path: "synpuf_tiny_subset/current"
celeborn_feature:
  enabled: true
copy_spec: false
domain: "app.dev.aetion.com"
dynamic_flat_tables: false
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
is_k8s: true
regenerate_all_flat_tables: true
service_account: "spark-operator-client-common"
source_files_password: ""
