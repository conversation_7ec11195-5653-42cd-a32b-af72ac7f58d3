defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "iqvia_pharmetrics"
client: "daiichisankyo"
revision: "20240927"
is_k8s: False
alert_user: "@denis.kozlov"
deployment_config:
  pfizer: "pfizer.aetion.com"
dynamic_patient_table: True

git_branch_override: "daiichisankyo_iqvia_pharmetrics_adm_20240918"
hive_vars:
  GDR_END_DATE: "2022-11-15"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/daiichisankyo-databricks-etl-iam"
rde_revision_check: "20240918"
source_files_password: ""
transform_path: "iqvia_pharmetrics/daiichisankyo/current"
upload_bucket: "daiichisankyo.aetion.com/upload/iqvia_pharmetrics/20240927"
use_smart_sampling: False
validation_path: "iqvia_pharmetrics/daiichisankyo/current"
validation_vars:
  GDR_END_DATE: "2022-11-15"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 80
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.default.parallelism: 8000
      spark.driver.maxResultSize: "0g"
      spark.network.timeout: "1200"
      spark.sql.autoBroadcastJoinThreshold: 209715200
      spark.sql.broadcastTimeout: 12000
      spark.sql.shuffle.partitions: 8000
      spark.task.maxDirectResultSize: 2097152000
  default:
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.default.parallelism: 4000
      spark.driver.maxResultSize: "0g"
      spark.network.timeout: "1200"
      spark.sql.autoBroadcastJoinThreshold: 209715200
      spark.sql.broadcastTimeout: 12000
      spark.sql.shuffle.partitions: 2000
      spark.task.maxDirectResultSize: 2097152000
