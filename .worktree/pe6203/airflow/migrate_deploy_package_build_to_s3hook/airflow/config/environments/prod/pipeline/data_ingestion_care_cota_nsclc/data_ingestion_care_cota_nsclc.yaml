defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@evgeniy.varganov"
client: "care"
dataset: "cota_nsclc"
revision: "20240415"
tag: "default"
rdc_dbc_name: cota_nsclc-default
upload_bucket: "care.aetion.com/upload/cota_nsclc/20240415"
deployment_config:
  care: "care.aetion.com"
hive_vars:
  GDR_END_DATE: "2023-07-02"
  GDR_START_DATE: "1955-07-02"
validation_vars:
  GDR_END_DATE: "2023-07-02"
  GDR_START_DATE: "1955-07-02"
transform_path: "cota_nsclc/care/current"
validation_path: "cota_nsclc/care/current"
dynamic_patient_table: true
git_branch_override: "master"
iam_arn: "arn:aws:iam::627533566824:instance-profile/care-databricks-etl-iam"
is_k8s: false
regenerate_all_flat_tables: false
skip_single_shard: true
source_files_password: ""
use_smart_sampling: false
steps_spark_config:
  default:
    num_workers: 2
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
