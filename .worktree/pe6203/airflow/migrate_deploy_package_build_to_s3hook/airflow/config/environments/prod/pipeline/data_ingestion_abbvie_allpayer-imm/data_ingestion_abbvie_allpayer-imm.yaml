defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@denis.kozlov"
client: "abbvie"
dataset: "allpayer_imm"
revision: "20211007"
tag: abbvie
rdc_dbc_name: allpayer_imm-abbvie
upload_bucket: "abbvie.aetion.com/upload/allpayer-imm/20211007"
hive_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2007-01-01"
  MASKED_BIRTH_DATE: "1930 and Earlier"
validation_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2007-01-01"
transform_path: "humedica/allpayer-imm-current"
validation_path: "humedica/allpayer-imm-current"
git_branch_override: "remotes/origin/abbvie-optum-market-clarity-imm-20211007"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
is_k8s: false
source_files_password: "HA82NA9ALQ"
steps_spark_config:
  full_job:
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 40
  default:
    node_type_id: "rd-fleet.2xlarge"
    num_workers: 10
    spark_conf:
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: true
      spark.default.parallelism: 1000
      spark.driver.maxResultSize: 0
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
      spark.sql.shuffle.partitions: 1000
    spark_version: "14.3.x-scala2.12"
