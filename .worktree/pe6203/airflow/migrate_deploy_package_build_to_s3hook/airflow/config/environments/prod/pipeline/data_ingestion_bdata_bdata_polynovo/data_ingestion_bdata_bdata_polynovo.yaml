defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@mikhail.skiba"
client: "bdata"
dataset: "bdata_polynovo"
revision: "20231219"
tag: "polynovo"
rdc_dbc_name: bdata_polynovo-polynovo
upload_bucket: "bdata.aetion.com/upload/bdata_polynovo/20231219"
deployment_config:
  nestcc: "nestcc.aetion.com"
hive_vars:
  GDR_END_DATE: "2023-12-19"
  GDR_START_DATE: "2016-01-01"
validation_vars:
  GDR_END_DATE: "2023-12-19"
  GDR_START_DATE: "2016-01-01"
transform_path: "bdata/polynovo/current"
validation_path: "bdata/polynovo/current"
dynamic_patient_table: true
git_branch_override: "bdata-bdata_polynovo-20231219"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bdata-databricks-etl-iam"
is_k8s: false
rde_revision_check: "20231219"
regenerate_all_flat_tables: true
skip_single_shard: true
source_files_password: ""
use_smart_sampling: false
steps_spark_config:
  default:
    node_type_id: "rd-fleet.xlarge"
    num_workers: 0
    spark_conf:
      aetion.dataset.automaticGdrFiltering: true
      spark.databricks.cluster.profile: "singleNode"
      spark.default.parallelism: 10
      spark.master: "local[*, 4]"
      spark.sql.shuffle.partitions: 10
