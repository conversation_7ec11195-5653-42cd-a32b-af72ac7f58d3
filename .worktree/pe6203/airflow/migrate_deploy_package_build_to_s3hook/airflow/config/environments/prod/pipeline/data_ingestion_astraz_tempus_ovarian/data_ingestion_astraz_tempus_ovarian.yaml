defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "tempus_ovarian"
client: "astraz"
revision: "20230110"
is_k8s: False
alert_user: "@denis.kozlov"
deployment_config:
  astraz: "astraz.aetion.com"
dynamic_patient_table: True
git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2022-10-31"
  GDR_START_DATE: "1940-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/astraz-databricks-etl-iam"
source_files_password: ""
transform_path: "tempus_ovarian/astraz/current"
upload_bucket: "astraz.aetion.com/upload/tempus_ovarian/20230110"
validation_path: "tempus_ovarian/astraz/current"
validation_vars:
  GDR_END_DATE: "2022-10-31"
  GDR_START_DATE: "1940-01-01"
steps_spark_config:
  full_job:
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.broadcastTimeout: 800
