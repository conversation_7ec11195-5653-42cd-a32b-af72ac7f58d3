defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: optum_cdm
client: biogen
revision: "********"
is_k8s: true
alert_user: "@oksana.antropova"
alert_scientist: "@brent.arakaki"
deployment_config:
  biogen: biogen.aetion.com
dynamic_patient_table: true
flat_tables_to_regenerate:
  - LABRESULTS_FLAT
  - MEDICAL_FLAT
git_branch_override: biogen_optum_cdm_********
hive_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "2007-01-01"
pyspark_memory_overhead_factor: ".2"
rde_revision_check: "********"
regenerate_all_flat_tables: false
service_account: spark-operator-client-biogen
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: optum/cdm/biogen
upload_bucket: biogen.aetion.com/upload/optum_cdm/********
validation_path: optum/cdm/biogen/current
validation_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "2007-01-01"
steps:
  unarchiver:
    step_group: "unarchiver_only"
  generate_full_enums_and_dictionaries:
    step_group: "full_job"
steps_spark_config:
  full_job:
    autoscale:
      enabled: "true"
      initial_executors: 80
      max_executors: 80
      min_executors: 80
    spark_conf:
      aetion.dataset.automaticGdrFiltering: "false"
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 10000
      spark.task.cpus: 3
      spark.memory.fraction: "0.85"
      spark.memory.storageFraction: "0.1"
      spark.memory.offHeap.enabled: "false"
      spark.memory.offHeap.size: 40g
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.kryo.registrationRequired: "false"
      spark.kryo.unsafe: "true"
      spark.kryo.referenceTracking: "false"
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "1024m"
      spark.decommission.enabled: "false"
      spark.storage.decommission.enabled: "false"
      spark.storage.decommission.rddBlocks.enabled: "false"
      spark.storage.decommission.shuffleBlocks.enabled: "false"
    worker_conf:
      instance_type: r5ad.8xlarge
      memory_on_disk: "800G"
      memory: "190g"
      spark_memory_overhead: "25g"
  full_patient_job:
    autoscale:
      enabled: "true"
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      aetion.dataset.automaticGdrFiltering: "false"
      spark.sql.parquet.enableVectorizedReader: "false"
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 1
      spark.decommission.enabled: "false"
      spark.storage.decommission.enabled: "false"
      spark.storage.decommission.rddBlocks.enabled: "false"
      spark.storage.decommission.shuffleBlocks.enabled: "false"
      spark.memory.offHeap.enabled: "true"
      spark.memory.offHeap.size: "40g"
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.kryo.registrationRequired: "false"
      spark.kryo.unsafe: "true"
      spark.kryo.referenceTracking: "false"
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "1024m"
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: "750G"
      spark_memory_overhead: "30g"
      memory: "150377m"
  full_shard_job:
    autoscale:
      enabled: "true"
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
  default:
    autoscale:
      enabled: "true"
      initial_executors: 10
      max_executors: 10
      min_executors: 10
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      aetion.dataset.automaticGdrFiltering: "false"
      spark.driver.maxResultSize: 0
      spark.sql.adaptive.autoBroadcastJoinThreshold: -1
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.constraintPropagation.enabled: "false"
      spark.sql.legacy.createHiveTableByDefault: "false"
      spark.sql.optimizer.dynamicPartitionPruning.enabled: "false"
      spark.sql.shuffle.partitions: 2000
      spark.sql.statistics.fallBackToHdfs: "false"
      spark.task.cpus: 3
    worker_conf:
      instance_type: r5.4xlarge
      memory_on_disk: 128G
  unarchiver_only:
    autoscale:
      enabled: "true"
      initial_executors: 1
      max_executors: 16
      min_executors: 1
    driver_conf:
      instance_type: r5a.xlarge
      pyspark_memory_overhead: 7000m
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
    worker_conf:
      instance_type: c5.4xlarge
      memory_on_disk: 128G
