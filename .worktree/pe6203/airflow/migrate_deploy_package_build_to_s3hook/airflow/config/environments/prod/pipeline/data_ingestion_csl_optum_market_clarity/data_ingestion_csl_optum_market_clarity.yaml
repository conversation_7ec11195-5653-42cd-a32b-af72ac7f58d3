defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum_market_clarity"
client: "csl"
revision: "20230703"
is_k8s: False
alert_user: "@mikhail.skiba"
deployment_config:
  csl: "csl.aetion.com"
dynamic_flat_tables: False
dynamic_patient_table: True

git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2022-09-30"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/csl-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "humedica/optum_market_clarity_onc/csl/current"
upload_bucket: "csl.aetion.com/upload/optum_market_clarity/20230703"
use_smart_sampling: True
validation_path: "humedica/optum_market_clarity_onc/csl/current"
validation_vars:
  GDR_END_DATE: "2022-09-30"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
      region: ""
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 40
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions: "-Xloggc:/dev/null -XX:+UseG1GC -XX:+UseStringDeduplication -XX:ParallelGCThreads=20 -XX:ConcGCThreads=5 -XX:InitiatingHeapOccupancyPercent=70 -XX:MaxGCPauseMillis=200"
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
      spark.network.timeout: 1200
      spark.sql.autoBroadcastJoinThreshold: 262144000
      spark.sql.broadcastTimeout: 1200
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 1000
      spark.task.maxDirectResultSize: 2097152000
  default:
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 40
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: 20971520
      spark.sql.broadcastTimeout: 600
  unarchiver_job_only:
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 20
    spark_conf: null
    spark_env_vars:
      export TMPDIR: "/local_disk0/tmp"
