defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@bartlomiej.cielecki"
client: "astraz"
dataset: "optum_cdm"
revision: "20221206"
tag: astraz
rdc_dbc_name: optum_cdm-astraz
upload_bucket: "astraz.aetion.com/upload/optum-cdm/20221206"
deployment_config:
  astraz: "astraz.aetion.com"

hive_vars:
  GDR_END_DATE: "2022-06-30"
  GDR_START_DATE: "2007-01-01"
validation_vars:
  GDR_END_DATE: "2022-06-30"
  GDR_START_DATE: "2007-01-01"
transform_path: "optum/cdm/astraz/current"
validation_path: "optum/cdm/astraz/current"
git_branch_override: "astraz_optum_cdm_20221206"
iam_arn: "arn:aws:iam::627533566824:instance-profile/astraz-databricks-etl-iam"
is_k8s: false
regenerate_all_flat_tables: true
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 60
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.broadcastTimeout: 800
      spark.sql.legacy.timeParserPolicy: "LEGACY"
      spark.sql.parquet.binaryAsString: true
      spark.sql.parquet.enableVectorizedReader: false
  default:
    spark_conf:
      spark.sql.shuffle.partitions: 1000
