defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@oksana.antropova"
client: "adn"
dataset: "omny_sample_dermatology"
revision: "20230717"
tag: "default"
rdc_dbc_name: omny_sample_dermatology-default
upload_bucket: "adn.aetion.com/upload/omny_sample_dermatology/20230717"
deployment_config:
  research: "demo.aetion.com"

hive_vars:
  GDR_END_DATE: "2021-04-30"
  GDR_START_DATE: "2016-01-01"
validation_vars:
  GDR_END_DATE: "2021-04-30"
  GDR_START_DATE: "2016-01-01"
transform_path: "omny_sample_dermatology/current"
validation_path: "omny_sample_dermatology/current"
git_branch_override: "adn_omny_sample_20230717"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
is_k8s: false
source_files_password: ""
use_smart_sampling: false
