defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@ivan.babic"
client: "bwhdope"
dataset: "optum_cdm"
revision: "********"
tag: "bwh"
rdc_dbc_name: optum_cdm-bwh
upload_bucket: "bwhdope.aetion.com/upload/optum_cdm/********"
deployment_config:
  bwhdope: "bwhdope.aetion.com"
hive_vars:
  GDR_END_DATE: "2025-05-31"
  GDR_START_DATE: "2004-01-01"
validation_vars:
  GDR_END_DATE: "2025-05-31"
  GDR_START_DATE: "2004-01-01"
transform_path: "optum/cdm/bwh/current"
validation_path: "optum/cdm/bwh/current"
alert_scientist: "@talia.piretra"
dynamic_patient_table: true
git_branch_override: "bwhdope-optum_cdm-********"
is_k8s: true
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: false
service_account: "spark-operator-client-bwhdope"
source_files_password: ""
spark_memory_overhead_factor: ".2"
use_smart_sampling: false
validate_data_formats: true
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 60
      max_executors: 80
      min_executors: 20
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.catalogImplementation: "hive"
      spark.sql.shuffle.partitions: 20000
      spark_memory_overhead_factor: ".3"
      spark.task.cpus: 4
    driver_conf:
      instance_type: "r5a.8xlarge"
    worker_conf:
      instance_type: "i3en.12xlarge"
  full_patient_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 80
      min_executors: 1
    driver_conf:
      instance_type: "i3en.12xlarge"
      pyspark_memory_overhead_factor: ".3"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 140000
      spark.task.cpus: 10
    worker_conf:
      pyspark_memory_overhead_factor: ".3"
      instance_type: "i3en.24xlarge"
  full_shard_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 80
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: "i3en.6xlarge"
  default:
    autoscale:
      enabled: true
      initial_executors: 10
      max_executors: 10
      min_executors: 10
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.catalogImplementation: "hive"
      spark.sql.constraintPropagation.enabled: false
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 3
    worker_conf:
      instance_type: "c5.4xlarge"
  unarchiver_job_only:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 16
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
    worker_conf:
      instance_type: "c5.4xlarge"
