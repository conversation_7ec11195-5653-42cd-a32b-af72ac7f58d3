defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@denis.kozlov"
client: "adn"
dataset: "marketscan_random"
revision: "20220119"
tag: 10M_RANDOM_SAMPLE
rdc_dbc_name: marketscan_random-10M_RANDOM_SAMPLE
upload_bucket: "adn.aetion.com/upload/marketscan_random/20220119"
deployment_config:
  demo: "demo.aetion.com"

hive_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2016-03-31"
  LAB_END_DATE: "2021-03-31"
  LAB_START_DATE: "2016-03-31"
validation_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2016-03-31"
transform_path: "marketscan_random/current"
validation_path: "marketscan_random/current"
git_branch_override: "adn_ms-random_20220119"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
is_k8s: false
source_files_password: ""
