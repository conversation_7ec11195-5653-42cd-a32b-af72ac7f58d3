defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "synpuf_omop"
client: "adn"
revision: "20240516"
is_k8s: False
alert_user: "@denis.kozlov"
deployment_config:
  adn: "adn.aetion.com"
git_branch_override: "adn-synpuf_omop-20240516"
hive_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
rde_revision_check: "20240516"
source_files_password: ""
transform_path: "cms/synpuf_omop/current"
upload_bucket: "s3://adn.aetion.com/upload/synpuf_omop/20240516"
validation_path: "cms/synpuf_omop/current"
validation_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
steps_spark_config:
  full_job:
    spark_conf:
      spark.default.parallelism: 6000
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 6000
