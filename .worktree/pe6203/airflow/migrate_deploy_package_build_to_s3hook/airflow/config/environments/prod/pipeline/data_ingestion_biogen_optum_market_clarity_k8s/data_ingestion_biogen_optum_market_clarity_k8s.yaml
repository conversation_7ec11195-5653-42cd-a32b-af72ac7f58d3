defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "optum_market_clarity"
client: "biogen"
revision: "********"
is_k8s: True
alert_user: "@evgeniy.varganov"
deployment_config:
  biogen: "biogen.aetion.com"
dynamic_patient_table: True

git_branch_override: "biogen-optum_market_clarity-********"
hive_vars:
  GDR_END_DATE: "2024-09-30"
  GDR_START_DATE: "2007-01-01"
  MASKED_BIRTH_DATE: "1932 and Earlier"
iam_arn: "arn:aws:iam::************:instance-profile/biogen-databricks-etl-iam"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: False
service_account: "spark-operator-client-biogen"
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: "humedica/optum_market_clarity/biogen"
upload_bucket: "biogen.aetion.com/upload/optum_market_clarity/********"
use_smart_sampling: False
validation_path: "humedica/optum_market_clarity/biogen/current"
validation_vars:
  GDR_END_DATE: "2024-09-30"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    autoscale:
      enabled: True
      initial_executors: 100
      max_executors: 100
      min_executors: 100
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 30000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "i3en.6xlarge"
  full_patient_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 100
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 140000
      spark.task.cpus: 2
    worker_conf:
      instance_type: "i3en.6xlarge"
  full_shard_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 120
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.task.cpus: 2
    worker_conf:
      instance_type: "i3en.6xlarge"
  default:
    autoscale:
      enabled: True
      initial_executors: 25
      max_executors: 25
      min_executors: 25
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 3000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "r5.4xlarge"
  unarchiver_job_only:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 16
      min_executors: 1
    driver_conf:
      instance_type: "r5a.xlarge"
      pyspark_memory_overhead: "7000m"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
    worker_conf:
      instance_type: "c5.4xlarge"
