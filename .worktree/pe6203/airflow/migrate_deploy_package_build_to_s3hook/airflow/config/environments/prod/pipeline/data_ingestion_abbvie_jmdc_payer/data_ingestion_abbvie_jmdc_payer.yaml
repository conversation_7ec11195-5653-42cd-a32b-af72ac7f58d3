defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "jmdc_payer"
client: "abbvie"
revision: "20240927"
is_k8s: False
alert_user: "@evgeniy.varganov"
deployment_config:
  abbvie: "abbvie.aetion.com"
dynamic_patient_table: True

git_branch_override: "abbvie-jmdc_payer-20240927"
hive_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "1900-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
regenerate_all_flat_tables: False
transform_path: "jmdc_payer/abbvie/current"
upload_bucket: "abbvie.aetion.com/upload/jmdc_payer/20240927"
use_smart_sampling: False
validation_path: "jmdc_payer/abbvie/current"
validation_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "1900-01-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 10
      min_workers: 1
    node_type_id: "i3en.12xlarge"
    spark_conf:
      spark.memory.offheap.enabled: False
      spark.task.cpus: 2
  default:
    num_workers: 10
    spark_conf:
      spark.default.parallelism: 1000
      spark.driver.maxResultSize: 0
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.sql.shuffle.partitions: 20000
    spark_version: "14.3.x-scala2.12"
  unarchiver_job_only:
    autoscale:
      max_workers: 0
      min_workers: 0
    custom_tags:
      - key: "ResourceClass"
        value: "SingleNode"
    driver_node_type_id: "r6g.4xlarge"
    node_type_id: "r6g.4xlarge"
    num_workers: 0
    spark_conf:
      spark.databricks.cluster.profile: "singleNode"
      spark.hadoop.fs.s3a.connection.maximum: 100
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.master: "local[*, 4]"
