defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /databricks_adm_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "adip-prod"
dataset: "marketscan_tiny_adm"
revision: "20231114"
tag: "default"
rdc_dbc_name: marketscan_tiny_adm-default
upload_bucket: "s3://adip-prod.app.us-east-1.aetion.com/upload/marketscan_tiny_adm/20231114"
deployment_config:
  aep-prod: "adip-prod.app.us-east-1.aetion.com"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
transform_path: "marketscan_adm/adn/1.1"
validation_path: "marketscan_adm/1.1"
domain: "app.us-east-1.aetion.com"
dynamic_flat_tables: true
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
iam_arn: "arn:aws:iam::627533566824:instance-profile/adip-prod-infra-prod-g-databricks-etl-iam"
is_k8s: false
pre_partitioned_data_url: "adip-prod.app.us-east-1.aetion.com/upload/marketscan_tiny_adm/20231114"
sampled_data_percentage: "50"
source_files_password: ""
use_smart_sampling: false
