defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "cmsnoac"
client: "bwh"
revision: "20230301"
is_k8s: False
alert_user: "@rafal.kwiatkowski"
deployment_config:
  bwhdope: "bwh-dope.partners.org/aetion"
git_branch_override: "remotes/origin/bwh-cmsnoac-20230301"
hive_vars:
  GDR_END_DATE: "2020-12-31"
  GDR_START_DATE: "2009-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "cms_noac/bwh/current"
upload_bucket: "bwh.aetion.com/upload/cmsnoac/20230301"
use_smart_sampling: False
validation_path: "cms_noac/bwh/current"
validation_vars:
  GDR_END_DATE: "2020-12-31"
  GDR_START_DATE: "2009-01-01"
steps_spark_config:
  full_job:
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      spark.default.parallelism: 4000
      spark.driver.maxResultSize: "0g"
      spark.memory.offHeap.enabled: False
      spark.sql.autoBroadcastJoinThreshold: "-1"
      spark.sql.broadcastTimeout: 12000
      spark.sql.shuffle.partitions: 6000
      spark.task.cpus: 2
      spark.task.maxDirectResultSize: 2097152000
  full_shard_job:
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      spark.default.parallelism: 4000
      spark.driver.maxResultSize: "0g"
      spark.memory.offHeap.enabled: False
      spark.sql.autoBroadcastJoinThreshold: "-1"
      spark.sql.broadcastTimeout: 12000
      spark.sql.shuffle.partitions: 6000
      spark.task.cpus: 2
      spark.task.maxDirectResultSize: 2097152000
  default:
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      spark.default.parallelism: 4000
      spark.driver.maxResultSize: "0g"
      spark.memory.offHeap.enabled: False
      spark.sql.autoBroadcastJoinThreshold: 209715200
      spark.sql.broadcastTimeout: 12000
      spark.sql.shuffle.partitions: 6000
      spark.task.cpus: 2
      spark.task.maxDirectResultSize: 2097152000
