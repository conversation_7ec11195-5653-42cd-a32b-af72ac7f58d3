defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "flatiron_ovarian"
client: "astraz"
revision: "20230518"
is_k8s: False
alert_user: "@rafal.kwi<PERSON><PERSON>"
use_smart_sampling: False

regenerate_all_flat_tables: True
deployment_config:
  astraz: "astraz.aetion.com"
hive_vars:
  GDR_END_DATE: "2023-05-17"
  GDR_START_DATE: "1900-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/astraz-databricks-etl-iam"
source_files_password: ""
git_branch_override: "astraz_flatiron_ovarian_20230518"
transform_path: "flatiron/astraz/current"
upload_bucket: "astraz.aetion.com/upload/flatiron_ovarian/20230518"
validation_path: "flatiron/astraz/current/"
validation_vars:
  GDR_END_DATE: "2023-05-17"
  GDR_START_DATE: "1900-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    spark_conf:
      spark.default.parallelism: 6000
      spark.task.cpus: 2
      spark.memory.offHeap.enabled: False
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 6000
  default:
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
