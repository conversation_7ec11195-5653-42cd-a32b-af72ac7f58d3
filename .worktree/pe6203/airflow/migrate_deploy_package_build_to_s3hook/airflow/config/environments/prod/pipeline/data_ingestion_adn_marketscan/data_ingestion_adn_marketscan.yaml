defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@mikhail.skiba"
client: "adn"
dataset: "marketscan"
revision: "20190601"
tag: "pl"
rdc_dbc_name: marketscan-pl
upload_bucket: "adn.aetion.com/upload/marketscan/20190601"
deployment_config:
  demo: "demo.aetion.com"
hive_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2018-09-30"
  LAB_START_DATE: "2013-12-31"
validation_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2013-12-31"
transform_path: "marketscan/adn/current"
validation_path: "marketscan/adn/current"
git_branch_override: "remotes/origin/demo_marketscan_pl_20190601"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
is_k8s: false
source_files_password: ""
