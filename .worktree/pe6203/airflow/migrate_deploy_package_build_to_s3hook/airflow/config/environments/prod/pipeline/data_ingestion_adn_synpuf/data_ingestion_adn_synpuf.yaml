defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@nikolay.kharin"
client: "adn"
dataset: "synpuf"
revision: "2021071202"
tag: "default"
rdc_dbc_name: synpuf-default
upload_bucket: "s3://adn.aetion.com/upload/synpuf/2021071202"
deployment_config:
  alnylam: "alnylam.aetion.com"
hive_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
validation_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
transform_path: "cms/synpuf/20210712"
validation_path: "cms/synpuf/20210712"
git_branch_override: "remotes/origin/master"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
is_k8s: false
pre_partitioned_data_url: "adn.aetion.com/etl/synpuf/20210712/"
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    spark_conf:
      spark.default.parallelism: 6000
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 6000
  default:
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
