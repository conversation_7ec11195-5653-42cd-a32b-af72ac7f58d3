defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "axogen"
dataset: "ranger"
revision: "20221011"
tag: "default"
rdc_dbc_name: ranger-default
upload_bucket: "axogen.aetion.com/upload/ranger/20221011"
deployment_config:
  axogen: "axogen.aetion.com"
hive_vars:
  GDR_END_DATE: "2022-10-04"
  GDR_START_DATE: "1900-01-01"
  REVISION: "20221011"
validation_vars:
  GDR_END_DATE: "2022-10-04"
  GDR_START_DATE: "1900-01-01"
transform_path: "axogen/ranger/current"
validation_path: "axogen/ranger/current"
git_branch_override: "axogen_ranger_20221011"
iam_arn: "arn:aws:iam::627533566824:instance-profile/axogen-databricks-etl-iam"
is_k8s: false
regenerate_all_flat_tables: true
skip_single_shard: true
source_files_password: ""
