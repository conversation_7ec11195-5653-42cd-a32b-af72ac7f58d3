defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "mdv"
client: "chugai"
revision: "20220309"
is_k8s: False
alert_user: "@nikolay.kharin"
deployment_config:
  chugai: "chugai.aetion.com"

git_branch_override: "chugai_mdv_20220309"
hive_vars:
  GDR_END_DATE: "2021-12-31"
  GDR_START_DATE: "1900-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/chugai-databricks-etl-iam"
source_files_password: ""
transform_path: "mdv/chugaicurrent"
upload_bucket: "s3://chugai.aetion.com/upload/mdv/20220309"
use_smart_sampling: True
validation_path: "mdv/chugaicurrent"
validation_vars:
  GDR_END_DATE: "2021-12-31"
  GDR_START_DATE: "1900-01-01"
