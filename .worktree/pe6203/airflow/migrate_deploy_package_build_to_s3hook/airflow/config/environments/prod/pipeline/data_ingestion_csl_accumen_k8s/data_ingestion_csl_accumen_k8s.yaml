defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: accumen
client: csl
revision: "**********"
is_k8s: true
alert_user: "@rafal.kwiatkowski"
deployment_config:
  csl: csl.aetion.com
dynamic_patient_table: true
git_branch_override: csl_accumen_**********
hive_vars:
  GDR_END_DATE: "2025-02-13"
  GDR_START_DATE: "1922-06-24"
pre_partitioned_data_url: csl.aetion.com/etl/accumen/********
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: true
sampled_data_percentage: ".5"
service_account: spark-operator-client-csl
skip_single_shard: true
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: accumen/csl/current
upload_bucket: csl.aetion.com/upload/accumen/**********
use_smart_sampling: false
validation_path: accumen/csl/current
validation_vars:
  GDR_END_DATE: "2025-02-13"
  GDR_START_DATE: "1922-06-24"
steps_spark_config:
  full_shard_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 20
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      aetion.dataset.automaticNdcFormatting: false
      spark.decommission.enabled: true
      spark.driver.extraJavaOptions: -XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000
      spark.driver.maxResultSize: "0"
      spark.eventLog.enabled: true
      spark.executor.extraJavaOptions: -XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000
      spark.hadoop.fs.s3a.connection.maximum: 300
      spark.hadoop.fs.s3a.server-side-encryption-algorithm: AES256
      spark.hadoop.fs.s3a.threads.max: 200
      spark.logConf: true
      spark.memory.offHeap.enabled: false
      spark.shuffle.io.maxRetries: 12
      spark.shuffle.io.numConnectionsPerPeer: 8
      spark.sql.adaptive.coalescePartitions.enabled: true
      spark.sql.adaptive.enabled: true
      spark.sql.adaptive.localShuffleReader.enabled: true
      spark.sql.adaptive.skewJoin.enabled: true
      spark.sql.legacy.timeParserPolicy: LEGACY
      spark.sql.parquet.binaryAsString: true
      spark.sql.shuffle.partitions: 2000
      spark.storage.decommission.enabled: true
      spark.storage.decommission.rddBlocks.enabled: true
      spark.storage.decommission.shuffleBlocks.enabled: true
      spark.storage.decommission.shuffleBlocks.maxThreads: 32
    worker_conf:
      instance_type: c5.4xlarge
