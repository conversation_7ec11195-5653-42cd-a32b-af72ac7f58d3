defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@denis.kozlov"
client: "bayer"
dataset: "optum_cdm"
revision: "20240515"
tag: bayer
rdc_dbc_name: optum_cdm-bayer
upload_bucket: "bayer.aetion.com/upload/optum-cdm/20240515"
deployment_config:
  bayerriverhf: "bayerriverhf.aetion.com"
hive_vars:
  GDR_END_DATE: "2023-03-31"
  GDR_START_DATE: "2007-01-01"
validation_vars:
  GDR_END_DATE: "2023-03-31"
  GDR_START_DATE: "2007-01-01"
transform_path: "optum/cdm/bibayer/current"
validation_path: "optum/cdm/bibayer/current"
dynamic_patient_table: true
flat_tables_to_regenerate:
  - confinement_flat
  - medical_flat
iam_arn: "arn:aws:iam::627533566824:instance-profile/bayer-databricks-etl-iam"
is_k8s: false
regenerate_all_flat_tables: true
source_files_password: ""
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 40
      min_workers: 40
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: false
      spark.rpc.numRetries: 10
      spark.sql.legacy.timeParserPolicy: "LEGACY"
      spark.sql.parquet.binaryAsString: true
      spark.sql.parquet.columnarReaderBatchSize: 256
      spark.sql.shuffle.partitions: 20000
  full_patient_job:
    node_type_id: "i3en.6xlarge"
    autoscale:
      min_workers: 1
      max_workers: 80
  full_shard_job:
    autoscale:
      min_workers: 1
      max_workers: 80
    spark_conf:
      spark.task.cpus: 2
  default:
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.sql.shuffle.partitions: 1000
