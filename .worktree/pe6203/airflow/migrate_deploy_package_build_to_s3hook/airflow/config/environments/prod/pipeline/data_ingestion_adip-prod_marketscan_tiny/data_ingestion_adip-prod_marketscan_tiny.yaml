defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "adip-prod"
dataset: "marketscan_tiny"
revision: "20240208"
tag: "default"
rdc_dbc_name: marketscan_tiny-default
upload_bucket: "s3://adip-prod.app.us-east-1.aetion.com/upload/marketscan_tiny/20240208"
deployment_config:
  aep-prod: "adip-prod.app.us-east-1.aetion.com"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2018-12-31"
  LAB_START_DATE: "2013-12-31"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
transform_path: "marketscan_tiny/adn/current"
validation_path: "marketscan_tiny/adn/current"
domain: "app.us-east-1.aetion.com"
dynamic_flat_tables: false
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
iam_arn: "arn:aws:iam::627533566824:instance-profile/adip-prod-infra-prod-g-databricks-etl-iam"
is_k8s: false
source_files_password: ""
use_smart_sampling: false
