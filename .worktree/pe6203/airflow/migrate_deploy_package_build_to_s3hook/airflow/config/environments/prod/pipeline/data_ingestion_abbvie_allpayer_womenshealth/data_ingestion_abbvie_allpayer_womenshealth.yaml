defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "abbvie"
dataset: "allpayer_womenshealth"
revision: "20211209"
tag: "default"
rdc_dbc_name: allpayer_womenshealth-default
upload_bucket: "s3://abbvie.aetion.com/upload/allpayer_womenshealth/20211209"
deployment_config:
  abbvie: "abbvie.aetion.com"
hive_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2007-01-01"
  REVISION: "20211209"
validation_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2007-01-01"
transform_path: "humedica/allpayer_womenshealth20211209"
validation_path: "humedica/allpayer_womenshealth20211209"
git_branch_override: "remotes/origin/abbvie_womenshealth_20211209"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
is_k8s: false
source_files_password: "HA82NA9ALQ"
steps_spark_config:
  full_job:
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 40
