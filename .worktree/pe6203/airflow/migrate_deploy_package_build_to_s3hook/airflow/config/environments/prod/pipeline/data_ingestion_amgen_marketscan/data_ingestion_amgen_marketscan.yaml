defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "amgen"
revision: "20250624"
is_k8s: False
alert_user: "@nikolay.kharin"
deployment_config:
  amgen: "amgen.aetion.com"
dynamic_patient_table: True

git_branch_override: "amgen-marketscan-20250624"
hive_vars:
  EV_END_DATE: "2024-06-30"
  EV_START_DATE: "2023-10-01"
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "2004-12-31"
  LAB_END_DATE: "2024-04-30"
  LAB_START_DATE: "2010-01-01"
  MAX_DATE: "9999-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/amgen-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "marketscan/amgen/current"
upload_bucket: "amgen.aetion.com/upload/marketscan/20250624"
use_smart_sampling: False
validation_path: "marketscan/amgen/current"
validation_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "2004-12-31"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 50
      min_workers: 1
    node_type_id: "c5.4xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 10000
  default:
    spark_conf:
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: True
      spark.driver.maxResultSize: "0"
      spark.network.timeout: 1200
      spark.sql.parquet.enableVectorizedReader: False
