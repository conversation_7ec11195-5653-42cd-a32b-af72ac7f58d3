defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "sample"
client: "arcadia"
revision: "20220622"
is_k8s: False
alert_user: "@denis.kozlov"
deployment_config:
  arcadia: "arcadia.aetion.com"

git_branch_override: "arcadia-sample-20220622"
hive_vars:
  GDR_END_DATE: "2022-07-07"
  GDR_START_DATE: "1900-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/arcadia-databricks-etl-iam"
source_files_password: ""
transform_path: "sample/arcadia/current"
upload_bucket: "s3://arcadia.aetion.com/upload/sample/20220622"
validation_path: "sample/arcadia/current"
validation_vars:
  GDR_END_DATE: "2022-07-07"
  GDR_START_DATE: "1900-01-01"
steps_spark_config:
  full_job:
    driver_node_type_id: "rd-fleet.4xlarge"
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 20
    spark_conf:
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: True
      spark.default.parallelism: 1000
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: 262144000
      spark.sql.broadcastTimeout: 1200
      spark.sql.objectHashAggregate.sortBased.fallbackThreshold: 1024
      spark.task.maxDirectResultSize: 2097152000
  default:
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 20
    spark_conf:
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: True
      spark.default.parallelism: 1000
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
