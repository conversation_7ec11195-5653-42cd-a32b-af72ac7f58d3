defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@mikhail.skiba"
client: "arena"
dataset: "marketscan_ibd"
revision: "20220318"
tag: "ARENA"
rdc_dbc_name: marketscan_ibd-ARENA
upload_bucket: "arena.aetion.com/upload/marketscan_ibd/20220318"
deployment_config:
  arena: "arena.aetion.com"
hive_vars:
  GDR_END_DATE: "2021-06-30"
  GDR_START_DATE: "2012-12-31"
  LAB_END_DATE: "2021-06-30"
  LAB_START_DATE: "2012-12-31"
validation_vars:
  GDR_END_DATE: "2021-06-30"
  GDR_START_DATE: "2012-12-31"
transform_path: "marketscan_ibd/arena/current"
validation_path: "marketscan_ibd/arena/current"
git_branch_override: "arena-marketscan_ibd-20220318"
iam_arn: "arn:aws:iam::627533566824:instance-profile/arena-databricks-etl-iam"
is_k8s: false
source_files_password: ""
