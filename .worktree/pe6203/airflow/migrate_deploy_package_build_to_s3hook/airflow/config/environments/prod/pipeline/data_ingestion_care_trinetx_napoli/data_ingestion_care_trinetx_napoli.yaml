defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "trinetx_napoli"
client: "care"
revision: "20230621"
is_k8s: False
alert_user: "@rafal.kwi<PERSON>kowski"
deployment_config:
  care: "care.aetion.com"

git_branch_override: "care_trinetx_napoli_20230621"
hive_vars:
  GDR_END_DATE: "2021-05-25"
  GDR_START_DATE: "1936-03-31"
iam_arn: "arn:aws:iam::627533566824:instance-profile/care-databricks-etl-iam"
regenerate_all_flat_tables: True
transform_path: "trinetx_napoli/care/current"
upload_bucket: "care.aetion.com/upload/trinetx_napoli/20230621"
use_smart_sampling: False
validation_path: "trinetx_napoli/care/current"
validation_vars:
  GDR_END_DATE: "2021-05-25"
  GDR_START_DATE: "1936-03-31"
