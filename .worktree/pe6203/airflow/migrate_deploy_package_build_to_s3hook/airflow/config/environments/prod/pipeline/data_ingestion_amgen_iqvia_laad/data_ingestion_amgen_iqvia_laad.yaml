defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "iqvia_laad"
client: "amgen"
revision: "20240620"
is_k8s: False
alert_user: "@bartlomiej.cielecki"
deployment_config:
  amgen: "amgen.aetion.com"
dynamic_patient_table: True

git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2025-02-10"
  GDR_START_DATE: "1900-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/amgen-databricks-etl-iam"
regenerate_all_flat_tables: False
source_files_password: ""
transform_path: "iqvia_laad/amgen/current"
upload_bucket: "amgen.aetion.com/upload/iqvia_laad/20240620"
validation_path: "iqvia_laad/amgen/current"
validation_vars:
  GDR_END_DATE: "2025-02-10"
  GDR_START_DATE: "1900-01-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 50
      min_workers: 50
    node_type_id: "i3en.6xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.shuffle.partitions: 40000
  full_patient_job:
    autoscale:
      max_workers: 100
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.shuffle.partitions: 20000
  default:
    autoscale:
      max_workers: 40
      min_workers: 40
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
      spark.sql.shuffle.partitions: 1000
