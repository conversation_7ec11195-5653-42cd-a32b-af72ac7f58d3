defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "culmination"
client: "culmination"
revision: "20240517"
is_k8s: False
alert_user: "@rafal.kwiatkowski"
deployment_config:
  culmination: "culmination.aetion.com"
dynamic_patient_table: True

git_branch_override: "culmination_culmination_20240517"
hive_vars:
  GDR_END_DATE: "2023-12-29"
  GDR_START_DATE: "1936-04-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/culmination-databricks-etl-iam"
regenerate_all_flat_tables: True
skip_single_shard: True
transform_path: "culmination/culmination/current"
upload_bucket: "culmination.aetion.com/upload/culmination/20240517"
use_smart_sampling: False
validation_path: "culmination/culmination/current"
validation_vars:
  G<PERSON>_END_DATE: "2023-12-29"
  GDR_START_DATE: "1936-04-01"
