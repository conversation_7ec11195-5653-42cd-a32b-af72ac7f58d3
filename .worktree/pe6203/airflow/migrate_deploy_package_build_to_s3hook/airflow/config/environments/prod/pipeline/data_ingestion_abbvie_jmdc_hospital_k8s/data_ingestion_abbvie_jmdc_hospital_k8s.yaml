defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: jmdc_hospital
client: abbvie
revision: "********"
is_k8s: true
alert_user: "@bartlomiej.cielecki"
deployment_config:
  abbvie: abbvie.aetion.com
dynamic_patient_table: true
git_branch_override: abbvie-jmdc_hospital-********
hive_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "1911-10-12"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: true
service_account: spark-operator-client-abbvie
spark_memory_overhead_factor: ".2"
transform_path: jmdc_hospital/abbvie/current
upload_bucket: abbvie.aetion.com/upload/jmdc_hospital/********
use_smart_sampling: false
validation_path: jmdc_hospital/abbvie/current
validation_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "1911-10-12"
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 60
      max_executors: 60
      min_executors: 60
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
  full_patient_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 140000
      spark.task.cpus: 2
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
  full_shard_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
  enums_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 80
      min_executors: 1
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
    worker_conf:
      instance_type: r5a.4xlarge
      memory_on_disk: ""
  default:
    autoscale:
      enabled: true
      initial_executors: 10
      max_executors: 10
      min_executors: 10
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.constraintPropagation.enabled: false
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 3
    worker_conf:
      instance_type: c5.4xlarge
      memory_on_disk: 128G
steps:
  generate_full_enums_and_dictionaries:
    step_group: "enums_job"
