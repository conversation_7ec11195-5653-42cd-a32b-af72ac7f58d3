defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "adn"
dataset: "marketscan_wo_labs"
revision: "20220617"
tag: "adn"
rdc_dbc_name: marketscan_wo_labs-adn
upload_bucket: "adn.aetion.com/upload/marketscan_wo_labs/20220617"
deployment_config:
  adn: "adn.aetion.com"
transform_path: "marketscan_wo_labs/adn/current"
validation_path: "marketscan_wo_labs/adn/current"
data_cuts:
  - hive_vars:
      DESCR: "AEP 5 Year Bundle"
      GDR_END_DATE: "2021-09-30"
      GDR_START_DATE: "2016-09-30"
      LAB_END_DATE: "2021-09-30"
      LAB_START_DATE: "2016-09-30"
      TAG: "5y"
    name: "5y"
    validation_vars:
      GDR_END_DATE: "2021-09-30"
      GDR_START_DATE: "2016-09-30"
  - hive_vars:
      DESCR: "AEP 3 Year Bundle"
      GDR_END_DATE: "2021-09-30"
      GDR_START_DATE: "2018-09-30"
      LAB_END_DATE: "2021-09-30"
      LAB_START_DATE: "2018-09-30"
      TAG: "3y"
    name: "3y"
    validation_vars:
      GDR_END_DATE: "2021-09-30"
      GDR_START_DATE: "2018-09-30"
git_branch_override: "remotes/origin/adn-marketscan-20220617"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
is_k8s: false
pre_partitioned_data_url: "adn.aetion.com/etl/marketscan/20220617"
source_files_password: ""
steps_spark_config:
  full_job:
    num_workers: 40
