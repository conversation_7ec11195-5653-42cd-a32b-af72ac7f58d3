defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "bwh"
revision: "**********"
is_k8s: False
alert_user: "@bartlomiej.cielecki"
deployment_config:
  bwhdope: "bwh-dope.partners.org/aetion"
dynamic_patient_table: True

regenerate_all_flat_tables: True
git_branch_override: "bwh_marketscan_**********"
hive_vars:
  GDR_END_DATE: "2022-12-31"
  GDR_START_DATE: "2012-12-31"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
pre_partitioned_data_url: "bwh.aetion.com/etl/marketscan/20240509"
source_files_password: ""
transform_path: "marketscan/bwh/current"
upload_bucket: "bwh.aetion.com/upload/marketscan/**********"
use_smart_sampling: False
validation_path: "marketscan/bwh/current"
validation_vars:
  GDR_END_DATE: "2022-12-31"
  GDR_START_DATE: "2012-12-31"
steps_spark_config:
  default:
    autoscale:
      max_workers: 10
      min_workers: 10
  full_job:
    node_type_id: "i3en.6xlarge"
    autoscale:
      max_workers: 40
      min_workers: 40
    spark_conf:
      spark.sql.shuffle.partitions: 10000
  full_patient_job:
    node_type_id: "i3en.6xlarge"
    autoscale:
      max_workers: 40
      min_workers: 1
    spark_conf:
      spark.sql.shuffle.partitions: 10000
  full_shard_job:
    autoscale:
      max_workers: 60
      min_workers: 1
  unarchiver_job_only:
    autoscale:
      max_workers: 30
      min_workers: 1
    node_type_id: "c5.4xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
