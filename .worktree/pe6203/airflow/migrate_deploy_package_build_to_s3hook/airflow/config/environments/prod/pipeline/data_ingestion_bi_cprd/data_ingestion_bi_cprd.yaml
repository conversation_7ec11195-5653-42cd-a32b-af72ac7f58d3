defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "cprd"
client: "bi"
revision: "20210302"
is_k8s: False
git_branch_override: "remotes/origin/bi-cprd-20210302"
hive_vars:
  GDR_END_DATE: "2020-12-31"
  GDR_START_DATE: "1987-11-21"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bi-databricks-etl-iam"
transform_path: "cprd/bi20210302"
upload_bucket: "bi.aetion.com/upload/cprd/20210302"
validation_path: "cprd/bi20210302"
steps_spark_config:
  full_job:
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 20
    spark_conf:
      spark.default.parallelism: 1000
      spark.sql.shuffle.partitions: 1000
  full_shard_job:
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 40
    spark_conf:
      enable_elastic_disk: True
      spark.driver.maxResultSize: 0
      spark.executor.heartbeatInterval: "600"
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
      spark.memory.offHeap.enabled: False
      spark.network.timeout: 1200
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.autoBroadcastJoinThreshold: 20971520
      spark.sql.broadcastTimeout: 1200
      spark.task.cpus: 4
      spark.task.maxDirectResultSize: 2097152000
  default:
    spark_conf:
      spark.sql.shuffle.partitions: 1000
