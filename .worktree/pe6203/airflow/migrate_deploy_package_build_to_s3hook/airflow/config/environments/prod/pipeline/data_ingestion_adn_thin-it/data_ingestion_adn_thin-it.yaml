defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@denis.kozlov"
client: "adn"
dataset: "thin_it"
revision: "20220728"
tag: default
rdc_dbc_name: thin_it-default
upload_bucket: "s3://adn.aetion.com/upload/thin-it/20220728"
deployment_config:
  demo: "demo.aetion.com"
hive_vars:
  GDR_END_DATE: "2022-07-25"
  GDR_START_DATE: "1900-01-01"
validation_vars:
  GDR_END_DATE: "2022-07-25"
  GDR_START_DATE: "1900-01-01"
transform_path: "thin-it/adn/current"
validation_path: "thin-it/adn/current"
git_branch_override: "adn-thin_it-20220728"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
is_k8s: false
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    spark_conf:
      spark.default.parallelism: 6000
      spark.network.timeout: 800
      spark.sql.shuffle.partitions: 6000
  default:
    spark_conf:
      aetion.dataset.dynamicCode: true
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
