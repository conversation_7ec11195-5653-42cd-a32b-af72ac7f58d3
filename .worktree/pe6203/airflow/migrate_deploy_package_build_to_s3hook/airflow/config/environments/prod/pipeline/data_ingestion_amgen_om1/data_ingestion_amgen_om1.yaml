defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "om1"
client: "amgen"
revision: "20240417"
is_k8s: False
alert_user: "@denis.kozlov"
deployment_config:
  amgen: "amgen.aetion.com"
dynamic_patient_table: True
git_branch_override: "amgen_om1_20240417"
hive_vars:
  GDR_END_DATE: "2024-02-14"
  GDR_START_DATE: "2013-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/amgen-databricks-etl-iam"
rde_revision_check: "20240417"
source_files_password: ""
transform_path: "om1/amgen/current"
upload_bucket: "amgen.aetion.com/upload/om1/20240417"
validation_path: "om1/amgen/current"
validation_vars:
  GDR_END_DATE: "2024-02-14"
  GDR_START_DATE: "2013-01-01"
steps_spark_config:
  full_job:
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      spark.default.parallelism: 4000
      spark.memory.offHeap.enabled: False
      spark.network.timeout: "1200"
      spark.sql.autoBroadcastJoinThreshold: 209715200
      spark.sql.broadcastTimeout: 12000
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 2
      spark.task.maxDirectResultSize: **********
