defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@mikhail.skiba"
client: "adn"
dataset: "synthetic_omop"
revision: "20250604"
tag: "adn"
rdc_dbc_name: synthetic_omop-adn
upload_bucket: "adn.aetion.com/upload/synthetic_omop/20250604"
deployment_config:
  adn: "adn.aetion.com"
hive_vars:
  GDR_END_DATE: "2020-10-01"
  GDR_START_DATE: "1909-01-01"
validation_vars:
  GDR_END_DATE: "2020-10-01"
  GDR_START_DATE: "1909-01-01"
transform_path: "synthetic_omop/adn/current"
validation_path: "synthetic_omop/adn/current"
dynamic_flat_tables: false
dynamic_patient_table: true
git_branch_override: "adn_synthetic_omop_20250604"
iam_arn: "arn:aws:iam::************:instance-profile/adn-databricks-etl-iam"
is_k8s: true
regenerate_all_flat_tables: true
service_account: "spark-operator-client-adn"
skip_single_shard: true
source_files_password: ""
steps_spark_config:
  default:
    autoscale:
      enabled: true
      initial_executors: 2
      max_executors: 5
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "5000m"
      spark_memory_overhead: "5000m"
    worker_conf:
      instance_type: "c5.4xlarge"
    spark_conf:
      spark.sql.legacy.timeParserPolicy: "LEGACY"
      aetion.dataset.automaticGdrFiltering: false
