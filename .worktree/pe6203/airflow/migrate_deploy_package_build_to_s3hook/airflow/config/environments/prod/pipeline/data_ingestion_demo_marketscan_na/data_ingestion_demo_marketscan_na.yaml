defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@alexandra.soboleva"
client: "demo"
dataset: "marketscan_na"
revision: "202108"
tag: "default"
rdc_dbc_name: marketscan_na-default
upload_bucket: "demo.aetion.com/upload/marketscan_na/202108"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2018-12-31"
  LAB_START_DATE: "2013-12-31"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
transform_path: "marketscan/demo202108"
validation_path: "marketscan/demo202108"
git_branch_override: "remotes/origin/demo-marketscan-202108"
iam_arn: "arn:aws:iam::627533566824:instance-profile/demo-databricks-etl-iam"
is_k8s: false
pre_partitioned_data_url: "demo.aetion.com/etl/marketscan/202108/"
source_files_password: ""
