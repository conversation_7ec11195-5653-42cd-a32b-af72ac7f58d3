defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@denis.kozlov"
client: "bwh"
dataset: "optum_cdm"
revision: "20240926"
tag: bwh
rdc_dbc_name: optum_cdm-bwh
upload_bucket: "bwh.aetion.com/upload/optum-cdm/20240926"
deployment_config:
  bwhdope: "bwh-dope.partners.org/aetion"

hive_vars:
  GDR_END_DATE: "2024-08-31"
  GDR_START_DATE: "2004-01-01"
validation_vars:
  GDR_END_DATE: "2024-08-31"
  GDR_START_DATE: "2004-01-01"
transform_path: "optum/cdm/bwh/current"
validation_path: "optum/cdm/bwh"
dynamic_patient_table: true
git_branch_override: "bwh-optum_cdm-20240926"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
is_k8s: false
regenerate_all_flat_tables: false
source_files_password: ""
use_smart_sampling: false
validate_data_formats: true
steps_spark_config:
  full_shard_job:
    node_type_id: "rg-fleet.8xlarge"
  full_job:
    autoscale:
      max_workers: 40
      min_workers: 40
    driver_node_type_id: "r5a.4xlarge"
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: false
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
  full_patient_job:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: false
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
  default:
    autoscale:
      max_workers: 10
      min_workers: 10
    driver_node_type_id: "rg-fleet.4xlarge"
    node_type_id: "rg-fleet.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.sql.shuffle.partitions: 1000
  unarchiver_job_only:
    autoscale:
      max_workers: 16
      min_workers: 1
    driver_node_type_id: "r5a.xlarge"
    node_type_id: "c5.4xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
