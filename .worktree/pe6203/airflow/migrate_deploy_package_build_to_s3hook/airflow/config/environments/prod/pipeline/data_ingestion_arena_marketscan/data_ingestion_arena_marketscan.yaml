defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "arena"
revision: "20220318"
is_k8s: False
alert_user: "@mikhail.skiba"
hive_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2012-12-31"
  LAB_END_DATE: "2021-03-31"
  LAB_START_DATE: "2013-01-01"
validation_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2012-12-31"
deployment_config:
  arena: "arena.aetion.com"
git_branch_override: "arean-marketscan_ibd-20211223"
iam_arn: "arn:aws:iam::627533566824:instance-profile/arena-databricks-etl-iam"
source_files_password: ""

transform_path: "marketscan_ibd/arena"
upload_bucket: "arena.aetion.com/upload/marketscan/20220318"
validation_path: "marketscan_ibd/arena"
