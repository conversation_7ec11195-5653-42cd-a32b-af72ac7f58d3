defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@evgeniy.varganov"
client: "bwhdope"
dataset: "cms_noac"
revision: "**********"
tag: "default"
rdc_dbc_name: cms_noac-default
upload_bucket: "bwhdope.aetion.com/upload/cms_noac/**********"
deployment_config:
  bwhdope: "bwhdope.aetion.com"
hive_vars:
  GDR_END_DATE: "2023-11-30"
  GDR_START_DATE: "2009-01-01"
validation_vars:
  GDR_END_DATE: "2023-11-30"
  GDR_START_DATE: "2009-01-01"
transform_path: "cms_noac/bwh/current"
validation_path: "cms_noac/bwh/current"
alert_scientist: "@ryan.gentil"
dynamic_patient_table: true
flat_tables_to_regenerate:
  - CARRIER_LINE_UNION_FLAT
  - PATIENT_ATTRIBUTE
git_branch_override: "bwhdope-cms_noac-**********"
is_k8s: true
pre_partitioned_data_url: "bwhdope.aetion.com/etl/cms_noac/********/"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: false
service_account: "spark-operator-client-bwhdope"
source_files_password: ""
spark_memory_overhead_factor: ".2"
use_smart_sampling: false
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 40
      max_executors: 40
      min_executors: 40
    driver_conf:
      instance_type: "r5a.8xlarge"
      pyspark_memory_overhead: "53000m"
      spark_memory_overhead: "53000m"
    spark_conf:
      ignore.comment.aqe: "Adaptive Query Execution (AQE)"
      ignore.comment.jvm_memory: "Memory & JVM Optimizations"
      ignore.comment.network: "Network & Timeout Settings"
      ignore.comment.shuffle_optimizations: "Shuffle Optimizations"
      ignore.comment.spill_shuffle_memory: "Spill & Shuffle Memory Management"
      spark.driver.extraJavaOptions:
        "-XX:+UseG1GC -XX:MaxGCPauseMillis=500 -XX:+PrintGCDetails
        -XX:InitiatingHeapOccupancyPercent=40"
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions:
        "-XX:+UseG1GC -XX:MaxGCPauseMillis=500 -XX:+PrintGCDetails
        -XX:InitiatingHeapOccupancyPercent=40"
      spark.executor.heartbeatInterval: "120s"
      spark.memory.fraction: "0.7"
      spark.memory.storageFraction: "0.3"
      spark.network.timeout: "1000s"
      spark.rdd.compress: true
      spark.shuffle.compress: true
      spark.shuffle.consolidateFiles: true
      spark.shuffle.io.clientThreads: 256
      spark.shuffle.io.maxRetries: "20"
      spark.shuffle.io.retryWait: "15s"
      spark.shuffle.io.serverThreads: 256
      spark.shuffle.sort.bypassMergeThreshold: "1000"
      spark.shuffle.spill.compress: true
      spark.sql.adaptive.coalescePartitions.enabled: true
      spark.sql.adaptive.enabled: true
      spark.sql.adaptive.localShuffleReader.enabled: true
      spark.sql.adaptive.shuffle.targetPostShuffleInputSize: "256MB"
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: "5000"
    worker_conf:
      instance_type: "i3en.6xlarge"
      spark_memory_overhead: "70g"
  full_patient_job:
    spark_conf:
      spark.driver.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.driver.maxResultSize: "0"
      spark.executor.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.hadoop.fs.s3a.connection.maximum: 300
      spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
      spark.hadoop.fs.s3a.threads.max: 200
      spark.memory.fraction: "0.5"
      spark.memory.storageFraction: "0.1"
      spark.memory.offHeap.enabled: "true"
      spark.memory.offHeap.size: "40G"
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.kryo.registrationRequired: "false"
      spark.kryo.unsafe: "false"
      spark.kryo.referenceTracking: "false"
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "512m"
      spark.shuffle.compress: "true"
      spark.rdd.compress: "true"
      spark.shuffle.spill.compress: "true"
      spark.io.compression.codec: "zstd"
      spark.io.compression.zstd.level: "6"
      spark.task.cpus: 1
      spark.sql.adaptive.coalescePartitions.enabled: "true"
      spark.sql.adaptive.enabled: "true"
      spark.sql.adaptive.localShuffleReader.enabled: "true"
      spark.sql.adaptive.skewJoin.enabled: "true"
      spark.sql.legacy.timeParserPolicy: "LEGACY"
      spark.sql.parquet.binaryAsString: "true"
      spark.sql.shuffle.partitions: 2000
      spark.decommission.enabled: "false"
      spark.storage.decommission.enabled: "false"
      spark.storage.decommission.rddBlocks.enabled: "false"
      spark.storage.decommission.shuffleBlocks.enabled: "false"
      spark.storage.decommission.shuffleBlocks.maxThreads: 32
      spark.sql.parquet.columnarReaderBatchSize: 256
      spark.shuffle.io.maxRetries: "8"
      spark.shuffle.io.numConnectionsPerPeer: "2"
      spark.shuffle.io.clientThreads: "16"
      spark.shuffle.io.serverThreads: "16"
      spark.shuffle.io.threads: "64"
      spark.shuffle.io.retryWait: "30s"
      spark.shuffle.file.buffer: "256k"
      spark.shuffle.unsafe.file.output.buffer: "1m"
      spark.network.timeout: "600s"
      spark.shuffle.io.connectionTimeout: "300s"
      spark.dynamicAllocation.executorIdleTimeout: "1800s"
      spark.dynamicAllocation.cachedExecutorIdleTimeout: "3600s"
      spark.dynamicAllocation.shuffleTracking.timeout: "1800s"
      spark.dynamicAllocation.sustainedSchedulerBacklogTimeout: "150s"
      spark.dynamicAllocation.schedulerBacklogTimeout: "10s"
      spark.dynamicAllocation.executorAllocationRatio: "1.0"
    autoscale:
      enabled: true
      initial_executors: 20
      max_executors: 60
      min_executors: 20
    worker_conf:
      instance_type: "r5ad.8xlarge"
      spark_memory_overhead: "40g"
      memory_on_disk: "500G"
  full_shard_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: "r5ad.8xlarge"
      spark_memory_overhead: "26000m"
    spark_conf:
      spark.memory.offHeap.enabled: false
      spark.task.cpus: 2
    worker_conf:
      instance_type: "r5ad.8xlarge"
      spark_memory_overhead: "26000m"
  default:
    autoscale:
      enabled: true
      initial_executors: 10
      max_executors: 30
      min_executors: 10
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.sql.shuffle.partitions: 1000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "r5ad.xlarge"
  unarchiver_job_only:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 16
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
    worker_conf:
      instance_type: "m5d.4xlarge"
