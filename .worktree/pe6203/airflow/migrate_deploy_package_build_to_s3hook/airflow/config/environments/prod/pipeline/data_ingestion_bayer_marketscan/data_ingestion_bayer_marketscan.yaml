defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "bayer"
revision: "201903"
is_k8s: False
hive_vars:
  GDR_END_DATE: "2018-03-31"
  GDR_START_DATE: "2001-12-31"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bayer-databricks-etl-iam"
source_files_password: ""
transform_path: "marketscan/bayer2017v3"
upload_bucket: "bayer.aetion.com/upload/marketscan/201903"
validation_path: "marketscan/bayer2017v3"
