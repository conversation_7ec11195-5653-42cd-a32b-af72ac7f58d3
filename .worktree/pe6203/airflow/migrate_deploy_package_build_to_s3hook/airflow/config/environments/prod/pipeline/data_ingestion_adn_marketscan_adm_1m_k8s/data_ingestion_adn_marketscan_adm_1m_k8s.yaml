defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /k8s_adm_step_group_config@steps_spark_config
  - _self_
alert_user: "@evgeniy.varganov"
client: "adn"
dataset: "marketscan_adm_1m"
revision: "********"
tag: "default"
rdc_dbc_name: marketscan_adm_1m-default
upload_bucket: "adn.aetion.com/upload/marketscan_adm_1m/********"
deployment_config:
  dds: "dds.aetion.com"

hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
transform_path: "marketscan_adm_1m/adn/current_filter"
validation_path: "marketscan_adm_1m/adn/current_filter"
git_branch_override: "adn-marketscan_adm_1m-********"
iam_arn: "arn:aws:iam::************:instance-profile/adn-databricks-etl-iam"
is_k8s: true
sampled_data_percentage: ".5"
service_account: "spark-operator-client-adn"
skip_single_shard: true
