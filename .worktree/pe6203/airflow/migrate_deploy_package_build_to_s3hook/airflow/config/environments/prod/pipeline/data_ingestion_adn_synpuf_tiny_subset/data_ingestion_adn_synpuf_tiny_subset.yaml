defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@Damian"
client: "adn"
dataset: "synpuf_tiny_subset"
revision: "20221004"
tag: "default"
rdc_dbc_name: synpuf_tiny_subset-default
upload_bucket: "adn.aetion.com/upload/synpuf_tiny_subset/20221004"
deployment_config:
  ci: "ci.aetion.com"
  qa: "cms.dev.aetion.com"
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
transform_path: "synpuf_tiny_subset/current"
validation_path: "synpuf_tiny_subset/current"
dynamic_flat_tables: false
dynamic_patient_table: true
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
is_k8s: false
regenerate_all_flat_tables: true
skip_single_shard: false
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    node_type_id: "rd-fleet.xlarge"
    spark_conf:
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 1
  default:
    node_type_id: "rd-fleet.xlarge"
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
