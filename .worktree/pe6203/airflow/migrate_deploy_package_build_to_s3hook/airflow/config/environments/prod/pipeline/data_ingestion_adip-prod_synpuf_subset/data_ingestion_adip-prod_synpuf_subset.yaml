defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "adip-prod"
dataset: "synpuf_subset"
revision: "20211018"
tag: "default"
rdc_dbc_name: synpuf_subset-default
upload_bucket: "s3://adip-prod.app.us-east-1.aetion.com/upload/synpuf_subset/20211018"
deployment_config:
  aep-prod: "adip-prod.app.us-east-1.aetion.com"
hive_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
validation_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
transform_path: "synpuf_subset/20211018"
validation_path: "synpuf_subset/20211018"
domain: "app.us-east-1.aetion.com"
dynamic_flat_tables: false
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
iam_arn: "arn:aws:iam::627533566824:instance-profile/adip-prod-infra-prod-g-databricks-etl-iam"
is_k8s: false
regenerate_all_flat_tables: false
skip_single_shard: false
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "auto"
    spark_conf:
      spark.default.parallelism: 6000
      spark.executor.cores: 8
      spark.sql.shuffle.partitions: 6000
  default:
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
