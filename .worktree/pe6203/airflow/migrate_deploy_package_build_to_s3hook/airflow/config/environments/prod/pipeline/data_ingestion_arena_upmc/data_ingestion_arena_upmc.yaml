defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@alexandra.soboleva"
client: "arena"
dataset: "upmc"
revision: "2022033102"
tag: "default"
rdc_dbc_name: upmc-default
upload_bucket: "arena.aetion.com/upload/upmc/2022033102"
deployment_config:
  arena: "arena.aetion.com"

hive_vars:
  GDR_END_DATE: "2021-12-31"
  GDR_START_DATE: "2015-06-01"
validation_vars:
  GDR_END_DATE: "2021-12-31"
  GDR_START_DATE: "2015-06-01"
transform_path: "upmc/arena/current"
validation_path: "upmc/arena/current"
git_branch_override: "arena-upmc-2022033102-fix"
iam_arn: "arn:aws:iam::627533566824:instance-profile/arena-databricks-etl-iam"
is_k8s: false
pre_partitioned_data_url: "arena.aetion.com/etl/upmc/20220331/"
source_files_password: ""
