defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@denis.kozlov"
client: "abbvie"
dataset: "neurology"
revision: "20211210"
tag: "default"
rdc_dbc_name: neurology-default
upload_bucket: "abbvie.aetion.com/upload/neurology/20211210"
deployment_config:
  abbvie: "abbvie.aetion.com"

hive_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2007-01-01"
  MASKED_BIRTH_DATE: "1931 and Earlier"
validation_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2007-01-01"
transform_path: "humedica/neurology/abbvie/current"
validation_path: "humedica/neurology/abbvie/current"
git_branch_override: "abbvie_optum_MC_neurology_20211210"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
is_k8s: false
source_files_password: "HA82NA9ALQ"
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 80
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.network.timeout: 1200
      spark.sql.shuffle.partitions: 10000
  full_patient_job:
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 90
    spark_conf:
      spark.default.parallelism: 8000
      spark.driver.maxResultSize: 0
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.network.timeout: 1200
  unarchiver_job_only:
    node_type_id: "rd-fleet.xlarge"
    num_workers: 80
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
