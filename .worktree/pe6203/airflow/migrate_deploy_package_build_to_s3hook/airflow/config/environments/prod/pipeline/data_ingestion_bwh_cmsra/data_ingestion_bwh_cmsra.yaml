defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@bartlomiej.cielecki"
client: "bwh"
dataset: "cmsra"
revision: "20221115"
tag: "default"
rdc_dbc_name: cmsra-default
upload_bucket: "bwh.aetion.com/upload/cmsra/20221115"
deployment_config:
  bwhdope: "bwh-dope.partners.org/aetion"

hive_vars:
  GDR_END_DATE: "2019-12-31"
  GDR_START_DATE: "2006-01-01"
validation_vars:
  GDR_END_DATE: "2019-12-31"
  GDR_START_DATE: "2006-01-01"
transform_path: "cms_ra/bwh/current"
validation_path: "cms_ra/bwh/current"
flat_tables_to_regenerate:
  - medpar_surgical_flat
git_branch_override: "remotes/origin/bwh-cmsra-20221115v2"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
is_k8s: false
regenerate_all_flat_tables: false
source_files_password: ""
use_smart_sampling: false
steps_spark_config:
  full_shard_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 512
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    spark_conf:
      spark.driver.maxResultSize: "200g"
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 1000
  default:
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.sql.broadcastTimeout: 800
