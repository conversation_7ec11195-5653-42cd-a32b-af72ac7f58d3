defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "trinetx"
client: "csl"
revision: "********"
is_k8s: True
alert_user: "@oksana.antropova"
deployment_config:
  csl: "csl.aetion.com"
dynamic_patient_table: True

git_branch_override: "csl_trinetx_********"
hive_vars:
  GDR_END_DATE: "2023-08-30"
  GDR_START_DATE: "1915-10-01"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: True
service_account: "spark-operator-client-csl"
spark_memory_overhead_factor: ".2"
transform_path: "trinetx/csl/current"
upload_bucket: "csl.aetion.com/upload/trinetx/********"
use_smart_sampling: False
validation_path: "trinetx/csl/current"
validation_vars:
  GDR_END_DATE: "2023-08-30"
  GDR_START_DATE: "1915-10-01"
