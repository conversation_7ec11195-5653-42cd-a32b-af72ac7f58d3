defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "flatiron_sclc"
client: "amgen"
revision: "20241031"
is_k8s: False
alert_user: "@rafal.kwi<PERSON>kowski"
deployment_config:
  amgen: "amgen.aetion.com"
dynamic_patient_table: True

git_branch_override: "amgen_flatiron_sclc_20241031"
hive_vars:
  GDR_END_DATE: "2024-10-07"
  GDR_START_DATE: "1900-01-01"
  MAX_DATE: "9999-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/amgen-databricks-etl-iam"
rde_revision_check: "20241031"
regenerate_all_flat_tables: True
skip_single_shard: True
source_files_password: ""
transform_path: "flatiron/amgen/sclc/current"
upload_bucket: "amgen.aetion.com/upload/flatiron_sclc/20241031"
validation_path: "flatiron/amgen/sclc/current"
validation_vars:
  GDR_END_DATE: "2024-10-07"
  GDR_START_DATE: "1900-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.memory.offHeap.enabled: False
      spark.sql.parquet.enableVectorizedReader: False
      spark.task.cpus: 2
  default:
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
