defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "HV005240"
client: "healthverity"
revision: "********"
is_k8s: True
alert_user: "@evgeniy.varganov"
alert_scientist: "@evgeniy.varganov"
deployment_config:
  healthverity: "healthverity.aetion.com"
git_branch_override: "healthverity-HV005240-********"
hive_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "1900-01-01"
service_account: "spark-operator-client-healthverity"
source_files_password: ""
transform_path: "HV005240/healthverity"
upload_bucket: "healthverity.aetion.com/upload/HV005240/********"
dynamic_patient_table: True
regenerate_all_flat_tables: False
use_smart_sampling: False
validation_path: "HV005240/healthverity"
validation_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "1900-01-01"
