defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /k8s_adm_step_group_config@steps_spark_config
  - _self_
dataset: "kythera_adm"
client: "csl"
revision: "********"
is_k8s: True
alert_user: "@denis.kozlov"
deployment_config:
  dds: "dds.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True
git_branch_override: "csl-kythera_adm-********"
hive_vars:
  GDR_END_DATE: "2024-07-31"
  GDR_START_DATE: "2015-10-01"
native_data_for_adm_url: "s3://csl.aetion.com/etl/kythera/********"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: True
sampled_data_percentage: ".5"
service_account: "spark-operator-client-csl"
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: "kythera_adm/csl/1.4"
upload_bucket: "csl.aetion.com/upload/kythera_adm/********"
use_smart_sampling: False
validation_path: "kythera_adm/csl/1.4"
validation_vars:
  GDR_END_DATE: "2024-07-31"
  GDR_START_DATE: "2015-10-01"
steps_spark_config:
  full_job:
    autoscale:
      enabled: True
      initial_executors: 10
      max_executors: 30
      min_executors: 10
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "r5a.4xlarge"
  full_patient_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 20
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 140000
      spark.task.cpus: 2
    worker_conf:
      instance_type: "r5a.4xlarge"
  full_shard_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 30
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: "r5a.4xlarge"
  default:
    autoscale:
      enabled: True
      initial_executors: 10
      max_executors: 20
      min_executors: 10
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.driver.maxResultSize: 0
      spark.sql.shuffle.partitions: 500
      spark.task.cpus: 1
    worker_conf:
      instance_type: "r5ad.xlarge"
