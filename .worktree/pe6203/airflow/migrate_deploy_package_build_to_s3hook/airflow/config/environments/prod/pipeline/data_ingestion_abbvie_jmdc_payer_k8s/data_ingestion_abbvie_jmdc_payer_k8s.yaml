defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: jmdc_payer
client: abbvie
revision: "********"
is_k8s: true
alert_user: "@bartlomiej.cielecki"
deployment_config:
  abbvie: abbvie.aetion.com
dynamic_patient_table: true
git_branch_override: abbvie-jmdc_payer-********
hive_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "1900-01-01"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: false
service_account: spark-operator-client-abbvie
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: jmdc_payer/abbvie/current
upload_bucket: abbvie.aetion.com/upload/jmdc_payer/********
use_smart_sampling: false
validation_path: jmdc_payer/abbvie/current
validation_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "1900-01-01"
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 60
      max_executors: 60
      min_executors: 60
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 10000
      spark.task.cpus: 1
      spark.memory.offHeap.enabled: true
      spark.memory.offHeap.size: "30g"
      spark.shuffle.io.maxRetries: 8
      spark.shuffle.io.numConnectionsPerPeer: 2
      spark.io.compression.codec: "zstd"
      spark.io.compression.zstd.level: 6
      spark.kryo.referenceTracking: false
      spark.kryo.registrationRequired: false
      spark.kryo.unsafe: true
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "1024m"
      spark.network.timeout: "600s"
      spark.rdd.compress: true
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.shuffle.compress: true
      spark.shuffle.file.buffer: "256k"
      spark.shuffle.io.clientThreads: 16
      spark.shuffle.io.connectionTimeout: "300s"
      spark.shuffle.io.retryWait: "30s"
      spark.shuffle.io.serverThreads: 16
      spark.shuffle.io.threads: 64
      spark.shuffle.spill.compress: true
      spark.shuffle.unsafe.file.output.buffer: 1m
      spark.sql.adaptive.coalescePartitions.initialPartitionNum: 1200
      spark.sql.adaptive.coalescePartitions.minPartitionSize: "512MB"
      spark.sql.adaptive.coalescePartitions.parallelismFirst: false
      spark.sql.adaptive.optimize.skewJoin.enabled: true
      spark.sql.adaptive.skewJoin.skewedPartitionFactor: 3
      spark.sql.adaptive.skewJoin.skewedPartitionThresholdInBytes: "2GB"
    worker_conf:
      instance_type: i3en.6xlarge
      # apply manually if cores doesn't work
      cores: 8
      memory_on_disk: 500G
      spark_memory_overhead: 30g
      pyspark_memory_overhead: 26500m
  full_patient_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 140000
      spark.task.cpus: 2
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
  full_shard_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
  default:
    autoscale:
      enabled: true
      initial_executors: 10
      max_executors: 10
      min_executors: 10
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.constraintPropagation.enabled: false
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 3
    worker_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
      memory_on_disk: ""
      cores: 15
  unarchiver_only:
    autoscale:
      initial_executors: 1
      max_executors: 20
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: 20g
      memory_on_disk: ""
      # cores not reflected
      cores: 10
    worker_conf:
      instance_type: "r5ad.4xlarge"
      pyspark_memory_overhead: 20g
      # cores not reflected
      cores: 10
  enums_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 80
      min_executors: 1
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
    worker_conf:
      instance_type: r5a.4xlarge
      memory_on_disk: ""
steps:
  unarchiver:
    step_group: "unarchiver_only"
  generate_full_enums_and_dictionaries:
    step_group: "enums_job"
