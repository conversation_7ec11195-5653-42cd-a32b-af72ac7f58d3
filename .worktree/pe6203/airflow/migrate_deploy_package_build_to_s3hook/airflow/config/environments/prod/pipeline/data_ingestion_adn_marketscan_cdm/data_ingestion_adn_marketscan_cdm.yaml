defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_cdm"
client: "adn"
revision: "**********"
is_k8s: False
alert_user: "@mikhail.skiba"
deployment_config:
  adn: "adn.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True
flat_tables_to_regenerate:
  - hospital_admission_cost_flat
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
pre_partitioned_data_url: "adn.aetion.com/etl/marketscan_cdm/20230526"
sampled_data_percentage: ".5"
source_files_password: ""
transform_path: "marketscan_cdm/adn/current"
upload_bucket: "adn.aetion.com/upload/marketscan_cdm/**********"
use_smart_sampling: False
validation_path: "marketscan_cdm/current"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 60
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions: "-Xloggc:/dev/null -XX:+UseG1GC -XX:+UseStringDeduplication -XX:ParallelGCThreads=20 -XX:ConcGCThreads=5 -XX:InitiatingHeapOccupancyPercent=70 -XX:MaxGCPauseMillis=200"
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 10000
      spark.task.maxDirectResultSize: 2097152000
  default:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "r-fleet.4xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 10000
