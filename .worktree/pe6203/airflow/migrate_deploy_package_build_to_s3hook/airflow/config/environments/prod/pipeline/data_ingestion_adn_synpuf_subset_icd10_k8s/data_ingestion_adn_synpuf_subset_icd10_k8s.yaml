defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@rafal.kwiatkowski"
client: adn
dataset: synpuf_subset_icd10
revision: "********"
tag: adn
rdc_dbc_name: synpuf_subset_icd10-adn
upload_bucket: adn.aetion.com/upload/synpuf_subset_icd10/********
deployment_config:
  research: demo.aetion.com
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
transform_path: synpuf_subset_icd10/adn/current
validation_path: synpuf_subset_icd10/adn/current
dynamic_patient_table: true
git_branch_override: adn_synpuf_subset_icd10_********
is_k8s: true
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: true
sampled_data_percentage: ".5"
service_account: spark-operator-client-adn
skip_single_shard: false
source_files_password: ""
spark_memory_overhead_factor: ".2"
use_smart_sampling: false
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 40
      max_executors: 40
      min_executors: 40
    spark_conf:
      aetion.dataset.automaticNdcFormatting: false
      spark.decommission.enabled: true
      spark.driver.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.driver.maxResultSize: "0g"
      spark.executor.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.hadoop.fs.s3a.connection.maximum: 300
      spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
      spark.hadoop.fs.s3a.threads.max: 200
      spark.memory.offHeap.enabled: true
      spark.memory.offHeap.size: "40g"
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.kryo.registrationRequired: false
      spark.kryo.unsafe: true
      spark.kryo.referenceTracking: false
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "1024m"
      spark.shuffle.compress: true
      spark.rdd.compress: true
      spark.shuffle.spill.compress: true
      spark.io.compression.codec: "zstd"
      spark.io.compression.zstd.level: "6"
      spark.sql.adaptive.coalescePartitions.enabled: true
      spark.sql.adaptive.coalescePartitions.initialPartitionNum: "1200"
      spark.sql.adaptive.coalescePartitions.minPartitionSize: "512MB"
      spark.sql.adaptive.coalescePartitions.parallelismFirst: false
      spark.sql.adaptive.enabled: true
      spark.sql.adaptive.localShuffleReader.enabled: true
      spark.sql.adaptive.optimize.skewJoin.enabled: true
      spark.sql.adaptive.skewJoin.enabled: true
      spark.sql.adaptive.skewJoin.skewedPartitionFactor: "3"
      spark.sql.adaptive.skewJoin.skewedPartitionThresholdInBytes: "2GB"
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.legacy.timeParserPolicy: "LEGACY"
      spark.sql.parquet.binaryAsString: true
      spark.sql.shuffle.partitions: 40000
      spark.storage.decommission.enabled: false
      spark.storage.decommission.rddBlocks.enabled: false
      spark.storage.decommission.shuffleBlocks.enabled: false
      spark.storage.decommission.shuffleBlocks.maxThreads: 32
      spark.network.io.connectionTimeout: "600s"
      spark.network.io.maxRetries: "12"
      spark.network.io.preferDirectBufs: true
      spark.network.io.retryWait: "5s"
      spark.network.timeout: "600s"
      spark.reducer.maxSizeInFlight: "128m"
      spark.rpc.askTimeout: "300s"
      spark.rpc.lookupTimeout: "300s"
      spark.rpc.message.maxSize: "1024"
      spark.task.cpus: 1
      spark.shuffle.io.maxRetries: "8"
      spark.shuffle.io.numConnectionsPerPeer: "2"
      spark.shuffle.io.clientThreads: "16"
      spark.shuffle.io.serverThreads: "16"
      spark.shuffle.io.threads: "64"
      spark.shuffle.io.retryWait: "30s"
      spark.shuffle.file.buffer: "256k"
      spark.shuffle.unsafe.file.output.buffer: "1m"
      spark.shuffle.io.connectionTimeout: "300s"
    worker_conf:
      # Change manually in _V2 variable
      cores: 10
      instance_type: i3en.6xlarge
      memory_on_disk: "500G"
      spark_memory_overhead: "20g"
  full_patient_job:
    autoscale:
      enabled: true
      initial_executors: 40
      max_executors: 40
      min_executors: 40
    spark_conf:
      aetion.dataset.automaticNdcFormatting: false
      spark.decommission.enabled: true
      spark.driver.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.driver.maxResultSize: "0g"
      spark.executor.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.hadoop.fs.s3a.connection.maximum: 300
      spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
      spark.hadoop.fs.s3a.threads.max: 200
      spark.memory.offHeap.enabled: true
      spark.memory.offHeap.size: "40g"
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.kryo.registrationRequired: false
      spark.kryo.unsafe: true
      spark.kryo.referenceTracking: false
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "1024m"
      spark.shuffle.compress: true
      spark.rdd.compress: true
      spark.shuffle.spill.compress: true
      spark.io.compression.codec: "zstd"
      spark.io.compression.zstd.level: "6"
      spark.sql.adaptive.coalescePartitions.enabled: true
      spark.sql.adaptive.coalescePartitions.initialPartitionNum: "1200"
      spark.sql.adaptive.coalescePartitions.minPartitionSize: "512MB"
      spark.sql.adaptive.coalescePartitions.parallelismFirst: false
      spark.sql.adaptive.enabled: true
      spark.sql.adaptive.localShuffleReader.enabled: true
      spark.sql.adaptive.optimize.skewJoin.enabled: true
      spark.sql.adaptive.skewJoin.enabled: true
      spark.sql.adaptive.skewJoin.skewedPartitionFactor: "3"
      spark.sql.adaptive.skewJoin.skewedPartitionThresholdInBytes: "2GB"
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.legacy.timeParserPolicy: "LEGACY"
      spark.sql.parquet.binaryAsString: true
      spark.sql.shuffle.partitions: 40000
      spark.storage.decommission.enabled: false
      spark.storage.decommission.rddBlocks.enabled: false
      spark.storage.decommission.shuffleBlocks.enabled: false
      spark.storage.decommission.shuffleBlocks.maxThreads: 32
      spark.network.io.connectionTimeout: "600s"
      spark.network.io.maxRetries: "12"
      spark.network.io.preferDirectBufs: true
      spark.network.io.retryWait: "5s"
      spark.network.timeout: "600s"
      spark.reducer.maxSizeInFlight: "128m"
      spark.rpc.askTimeout: "300s"
      spark.rpc.lookupTimeout: "300s"
      spark.rpc.message.maxSize: "1024"
      spark.task.cpus: 1
      spark.shuffle.io.maxRetries: "8"
      spark.shuffle.io.numConnectionsPerPeer: "2"
      spark.shuffle.io.clientThreads: "16"
      spark.shuffle.io.serverThreads: "16"
      spark.shuffle.io.threads: "64"
      spark.shuffle.io.retryWait: "30s"
      spark.shuffle.file.buffer: "256k"
      spark.shuffle.unsafe.file.output.buffer: "1m"
      spark.shuffle.io.connectionTimeout: "300s"
    worker_conf:
      # Change manually in _V2 variable
      cores: 10
      instance_type: i3en.6xlarge
      memory_on_disk: "500G"
      spark_memory_overhead: "20g"
  enums_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 80
      min_executors: 1
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 10000
      spark.task.cpus: 1
      spark.sql.parquet.int96RebaseModeInRead: "CORRECTED"
      spark.sql.parquet.int96RebaseModeInWrite: "CORRECTED"
      spark.sql.parquet.datetimeRebaseModeInRead: "CORRECTED"
      spark.sql.parquet.datetimeRebaseModeInWrite: "CORRECTED"
    worker_conf:
      instance_type: r5a.4xlarge
      memory_on_disk: ""
  sampled_data_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 40
      min_executors: 1
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 50000
      spark.task.cpus: 1
      spark.sql.parquet.int96RebaseModeInRead: "CORRECTED"
      spark.sql.parquet.int96RebaseModeInWrite: "CORRECTED"
      spark.sql.parquet.datetimeRebaseModeInRead: "CORRECTED"
      spark.sql.parquet.datetimeRebaseModeInWrite: "CORRECTED"
    worker_conf:
      instance_type: i3en.6xlarge
steps:
  generate_full_enums_and_dictionaries:
    step_group: "enums_job"
  create_single_partition:
    step_group: "enums_job"
  create_full_partition:
    step_group: "enums_job"
  create_single_patient:
    step_group: "enums_job"
  generate_sampled_data:
    step_group: "sampled_data_job"
