defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "obesity"
client: "curimeta"
revision: "20231117"
is_k8s: False
alert_user: "@evgeniy.varganov"
git_branch_override: "remotes/origin/curimeta_obesity_20231117"
hive_vars:
  GDR_END_DATE: "2025-02-16"
  GDR_START_DATE: "1981-07-22"
iam_arn: "arn:aws:iam::627533566824:instance-profile/curimeta-databricks-etl-iam"
source_files_password: ""
transform_path: "dummy"
upload_bucket: "curimeta.aetion.com/upload/obesity/20231117"
validation_path: "dummy"
