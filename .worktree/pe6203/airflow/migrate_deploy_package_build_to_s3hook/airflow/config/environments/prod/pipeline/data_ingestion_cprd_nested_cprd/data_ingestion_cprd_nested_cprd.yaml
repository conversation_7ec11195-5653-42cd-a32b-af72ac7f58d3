defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "cprd"
dataset: "nested_cprd"
revision: "201812"
tag: "default"
rdc_dbc_name: nested_cprd-default
upload_bucket: "cprd.aetion.com/upload/nested_cprd/201812"
hive_vars:
  GDR_END_DATE: "2019-01-01"
  GDR_START_DATE: "1987-11-21"
transform_path: "cprd/201807_nested"
validation_path: "cprd/201807_nested"
artifacts_path_override: "s3://databricks.aetion.com/artifacts/automated_data_ingestion/datasets/nested_cprd/"
git_branch_override: "remotes/origin/nested-cprd"
iam_arn: "arn:aws:iam::627533566824:instance-profile/cprd-databricks-etl-iam"
is_k8s: false
source_files_password: ""
steps_spark_config:
  full_shard_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
      zone_id: "us-east-1b"
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 20
    spark_conf:
      spark.sql.parquet.enableVectorizedReader: false
  unarchiver_job_only:
    aws_attributes:
      ebs_volume_count: 1
      ebs_volume_size: 2048
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    init_scripts:
      - s3:
          destination: "s3://databricks.aetion.com/artifacts/automated_data_ingestion/prod/unarchiver_cluster_init.sh"
          region: ""
    num_workers: 3
    spark_conf:
      spark.executor.cores: "1"
    spark_env_vars:
      export TMPDIR: "/local_disk0/tmp"
