defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "optum_cdm"
client: "abbvie"
revision: "********"
is_k8s: True
alert_user: "@mikhail.skiba"
alert_scientist: "@daniel.thomas"
deployment_config:
  abbvie: "abbvie.aetion.com"
dynamic_patient_table: True

git_branch_override: "abbvie_optum_cdm_********"
hive_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "2000-05-01"
regenerate_all_flat_tables: False
service_account: "spark-operator-client-abbvie"
source_files_password: ""
transform_path: "optum/cdm/abbvie/current"
upload_bucket: "abbvie.aetion.com/upload/optum_cdm/********"
use_smart_sampling: False
validation_path: "optum/cdm/abbvie/current"
validation_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "2000-05-01"
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 60
      max_executors: 60
      min_executors: 20
    spark_conf:
      spark.decommission.enabled: True
      spark.driver.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.executor.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.memory.offHeap.enabled: True
      spark.memory.offHeap.size: "40g"
      spark.memory.fraction: "0.85"
      spark.memory.storageFraction: "0.1"
      spark.sql.adaptive.enabled: True
      spark.sql.adaptive.coalescePartitions.enabled: True
      spark.sql.adaptive.coalescePartitions.minPartitionSize: "512MB"
      spark.sql.adaptive.skewJoin.enabled": True
      spark.sql.adaptive.skewJoin.skewedPartitionThresholdInBytes: "2GB"
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.io.compression.codec: "zstd"
      spark.io.compression.zstd.level: 6
      spark.shuffle.compress: True
      spark.shuffle.io.maxRetries: 12
      spark.shuffle.io.numConnectionsPerPeer: 8
      spark.shuffle.io.threads: 64
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.threads.max: 200
      aetion.dataset.automaticGdrFiltering: false
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.catalogImplementation: hive
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: "750G"
  full_patient_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 1
      spark.decommission.enabled: false
      spark.storage.decommission.enabled: false
      spark.storage.decommission.rddBlocks.enabled: false
      spark.storage.decommission.shuffleBlocks.enabled: false
      spark.memory.offHeap.enabled: true
      spark.memory.offHeap.size: "40g"
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.kryo.registrationRequired: false
      spark.kryo.unsafe: true
      spark.kryo.referenceTracking: false
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "1024m"
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: "750G"
      spark_memory_overhead: "30g"
      memory: "150377m"
  full_shard_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      spark.task.cpus: 2
      spark.memory.offHeap.enabled: true
      spark.memory.offHeap.size: "40g"
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.kryo.registrationRequired: false
      spark.kryo.unsafe: true
      spark.kryo.referenceTracking: false
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "1024m"
      spark.sql.shuffle.partitions: 14000
    worker_conf:
      instance_type: "i3en.6xlarge"
      memory_on_disk: "750G"
  default:
    autoscale:
      enabled: True
      initial_executors: 10
      max_executors: 20
      min_executors: 10
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.constraintPropagation.enabled: False
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 3
    worker_conf:
      instance_type: "c5.4xlarge"
  unarchiver_job_only:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 16
      min_executors: 1
    driver_conf:
      instance_type: "r5a.8xlarge"
      pyspark_memory_overhead: "7000m"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
    worker_conf:
      instance_type: "c5.4xlarge"
