defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: optum_cdm_zip5
client: biogen
revision: "********"
is_k8s: true
alert_user: "@bartlomiej.cielecki"
deployment_config:
  dds: dds.aetion.com
dynamic_patient_table: true
git_branch_override: biogen-optum_cdm_zip5-********
hive_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "2007-01-01"
regenerate_all_flat_tables: true
service_account: spark-operator-client-biogen
source_files_password: ""
transform_path: optum/cdm_zip5/biogen/current
upload_bucket: biogen.aetion.com/upload/optum_cdm_zip5/********
use_smart_sampling: false
validation_path: optum/cdm_zip5/biogen/current
validation_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "2007-01-01"
transform_path: "optum/cdm_zip5/biogen/current"
validation_path: "optum/cdm_zip5/biogen/current"
dynamic_patient_table: true
git_branch_override: "biogen-optum_cdm_zip5-********"
is_k8s: true
regenerate_all_flat_tables: true
service_account: "spark-operator-client-biogen"
source_files_password: ""
use_smart_sampling: false
steps_spark_config:
  full_job:
    autoscale:
      enabled: "true"
      initial_executors: 100
      max_executors: 100
      min_executors: 100
    spark_conf:
      aetion.dataset.automaticGdrFiltering: "false"
      spark.driver.maxResultSize: 0
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 10000
      spark.task.cpus: 3
      spark.memory.fraction: "0.85"
      spark.memory.storageFraction: "0.1"
      spark.memory.offHeap.enabled: "false"
      spark.memory.offHeap.size: 40g
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.kryo.registrationRequired: "false"
      spark.kryo.unsafe: "true"
      spark.kryo.referenceTracking: "false"
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "1024m"
      spark.decommission.enabled: "false"
      spark.storage.decommission.enabled: "false"
      spark.storage.decommission.rddBlocks.enabled: "false"
      spark.storage.decommission.shuffleBlocks.enabled: "false"
    worker_conf:
      instance_type: r5ad.8xlarge
      memory_on_disk: "800G"
      memory: "190g"
      spark_memory_overhead: "25g"
  full_patient_job:
    autoscale:
      enabled: "true"
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      aetion.dataset.automaticGdrFiltering: "false"
      spark.sql.parquet.enableVectorizedReader: "false"
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 1
      spark.decommission.enabled: "false"
      spark.storage.decommission.enabled: "false"
      spark.storage.decommission.rddBlocks.enabled: "false"
      spark.storage.decommission.shuffleBlocks.enabled: "false"
      spark.memory.offHeap.enabled: "true"
      spark.memory.offHeap.size: "40g"
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.kryo.registrationRequired: "false"
      spark.kryo.unsafe: "true"
      spark.kryo.referenceTracking: "false"
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "1024m"
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: "750G"
      spark_memory_overhead: "30g"
      memory: "150377m"
  full_shard_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: ""
  default:
    autoscale:
      enabled: true
      initial_executors: 10
      max_executors: 20
      min_executors: 1
    spark_conf:
      aetion.dataset.automaticGdrFiltering: true
      spark.executor.extraJavaOptions: -XX:+UseG1GC -XX:MaxGCPauseMillis=500 -XX:InitiatingHeapOccupancyPercent=40
      spark.executor.heartbeatInterval: 120s
      spark.logConf: true
      spark.memory.fraction: "0.7"
      spark.memory.offHeap.enabled: false
      spark.memory.storageFraction: "0.4"
      spark.network.timeout: 1000s
      spark.rdd.compress: true
      spark.reducer.maxSizeInFlight: 96m
      spark.shuffle.compress: true
      spark.shuffle.file.buffer: 256k
      spark.shuffle.io.clientThreads: 256
      spark.shuffle.io.maxRetries: "20"
      spark.shuffle.io.numConnectionsPerPeer: "16"
      spark.shuffle.io.retryWait: 10s
      spark.shuffle.io.serverThreads: 256
      spark.shuffle.spill.compress: true
      spark.sql.adaptive.coalescePartitions.enabled: true
      spark.sql.adaptive.enabled: true
      spark.sql.adaptive.localShuffleReader.enabled: true
      spark.sql.adaptive.shuffle.targetPostShuffleInputSize: 256MB
      spark.sql.adaptive.skewJoin.enabled: true
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.catalogImplementation: hive
      spark.sql.objectHashAggregate.sortBased.fallbackThreshold: 16
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 1
    worker_conf:
      instance_type: r5ad.8xlarge
      memory_on_disk: ""
  unarchiver_job_only:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 16
      min_executors: 1
    driver_conf:
      instance_type: r5a.xlarge
      pyspark_memory_overhead: 7000m
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
    worker_conf:
      instance_type: c5.4xlarge
      memory_on_disk: 128G
