defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@Damian"
client: "alnylam"
dataset: "ehr_noclaims"
revision: "20230224"
tag: alnylam
rdc_dbc_name: ehr_noclaims-alnylam
upload_bucket: "alnylam.aetion.com/upload/ehr-noclaims/20230224"
deployment_config:
  alnylam: "alnylam.aetion.com"
hive_vars:
  GDR_END_DATE: "2022-10-31"
  GDR_START_DATE: "1928-02-01"
validation_vars:
  GDR_END_DATE: "2022-10-31"
  GDR_START_DATE: "1928-02-01"
transform_path: "humedica/ehr-noclaims/janssen/current"
validation_path: "humedica/ehr-noclaims/janssen/current"
dynamic_flat_tables: false
dynamic_patient_table: true
git_branch_override: "alnylam_ehrnoclaims_20230224"
iam_arn: "arn:aws:iam::627533566824:instance-profile/alnylam-databricks-etl-iam"
is_k8s: false
regenerate_all_flat_tables: false
source_files_password: ""
use_smart_sampling: false
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 1
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 120
    spark_conf:
      spark.default.parallelism: 8000
      spark.driver.maxResultSize: "0G"
      spark.io.compression.codec: "snappy"
      spark.network.timeout: 1200
      spark.sql.broadcastTimeout: 1200
      spark.sql.parquet.enableVectorizedReader: false
    spark_version: "14.3.x-scala2.12"
  default:
    aws_attributes:
      ebs_volume_count: 1
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 40
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
      spark.io.compression.codec: "snappy"
      spark.network.timeout: 1200
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.broadcastTimeout: 1200
      spark.sql.parquet.enableVectorizedReader: false
      spark.task.maxDirectResultSize: 2097152000
    spark_version: "14.3.x-scala2.12"
