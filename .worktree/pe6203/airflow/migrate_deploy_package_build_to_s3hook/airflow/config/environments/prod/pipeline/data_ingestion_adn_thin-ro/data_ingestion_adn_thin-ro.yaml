defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@denis.kozlov"
client: "adn"
dataset: "thin_ro"
revision: "20220728"
tag: adn
rdc_dbc_name: thin_ro-adn
upload_bucket: "s3://adn.aetion.com/upload/thin-ro/20220728"
hive_vars:
  GDR_END_DATE: "2022-07-18"
  GDR_START_DATE: "1936-05-01"
validation_vars:
  GDR_END_DATE: "2022-07-18"
  GDR_START_DATE: "1936-05-01"
transform_path: "thin-ro/adn/current"
validation_path: "thin-ro/adn/current"
git_branch_override: "adn-thin_ro-20220728"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
is_k8s: false
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    spark_conf:
      spark.default.parallelism: 6000
      spark.sql.shuffle.partitions: 6000
  default:
    spark_conf:
      aetion.dataset.dynamicCode: true
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
