defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "ms_registry"
client: "corrona"
revision: "20201029"
is_k8s: False
git_branch_override: "remotes/origin/corrona-ms-20201029"
hive_vars:
  GDR_END_DATE: "2020-10-05"
  GDR_START_DATE: "2017-09-15"
  MASKED_BIRTH_DATE: "1930 and Earlier"
iam_arn: "arn:aws:iam::627533566824:instance-profile/corrona-databricks-etl-iam"
source_files_password: ""
transform_path: "corrona/ms/20201029"
upload_bucket: "s3://corrona.aetion.com/upload/ms_registry/20201029"
validation_path: "corrona/ms/20201029"
steps_spark_config:
  full_job:
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 40
