defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "loopback"
client: "adn"
revision: "********"
is_k8s: True
alert_user: "@rafal.kwiatkowski"
deployment_config:
  research: "demo.aetion.com"
dynamic_patient_table: True
git_branch_override: "adn_loopback_********"
hive_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "1900-01-01"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: True
sampled_data_percentage: ".5"
service_account: "spark-operator-client-adn"
source_files_password: ""
transform_path: "loopback/adn/current"
upload_bucket: "adn.aetion.com/upload/loopback/********"
use_smart_sampling: False
validation_path: "loopback/adn/current"
validation_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "1900-01-01"
steps_spark_config:
  full_job:
    autoscale:
      enabled: True
      initial_executors: 15
      max_executors: 80
      min_executors: 10
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.network.io.connectionTimeout: "600s"
      spark.network.io.maxRetries: "12"
      spark.network.io.preferDirectBufs: True
      spark.network.io.retryWait: "5s"
      spark.network.timeout: "600s"
      spark.reducer.maxSizeInFlight: "128m"
      spark.rpc.askTimeout: "300s"
      spark.rpc.lookupTimeout: "300s"
      spark.rpc.message.maxSize: "1024"
      spark.sql.shuffle.partitions: 40000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "i3en.6xlarge"
  full_shard_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 80
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.threads.max: 1024
      spark.memory.offHeap.enabled: False
      spark.task.cpus: 3
    worker_conf:
      instance_type: "i3en.6xlarge"
  default:
    autoscale:
      enabled: True
      initial_executors: 10
      max_executors: 30
      min_executors: 10
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.driver.maxResultSize: 0
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
      spark.sql.parquet.int96RebaseModeInWrite: "LEGACY"
      spark.sql.shuffle.partitions: 10000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "r5ad.xlarge"
