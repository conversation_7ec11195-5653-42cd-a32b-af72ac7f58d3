defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: optum_market_clarity_obesity
client: amgen
revision: "********"
is_k8s: true
alert_user: "@denis.kozlov"
alert_scientist: "@daniel.thomas"
deployment_config:
  amgen: amgen.aetion.com
dynamic_patient_table: true
git_branch_override: amgen-optum_market_clarity_obesity-********
hive_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "2007-01-01"
  MASKED_BIRTH_DATE: 1930 and Earlier
iam_arn: arn:aws:iam::************:instance-profile/amgen-databricks-etl-iam
pyspark_memory_overhead_factor: ".2"
regenerate_all_flat_tables: false
service_account: spark-operator-client-amgen
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: humedica/optum_market_clarity_obesity/amgen/current
upload_bucket: amgen.aetion.com/upload/optum_market_clarity_obesity/********
use_smart_sampling: false
validation_path: humedica/optum_market_clarity_obesity/amgen/current
validation_vars:
  GDR_END_DATE: "2025-03-31"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 60
      max_executors: 80
      min_executors: 5
    driver_conf:
      instance_type: r5a.8xlarge
      pyspark_memory_overhead: 54500m
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.memory.offHeap.enabled: true
      spark.memory.offHeap.size: "60g"
      spark.shuffle.io.maxRetries: 8
      spark.shuffle.io.numConnectionsPerPeer: 2
      spark.io.compression.codec: "zstd"
      spark.io.compression.zstd.level: 6
      spark.kryo.referenceTracking: false
      spark.kryo.registrationRequired: false
      spark.kryo.unsafe: true
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "1024m"
      spark.network.timeout: "600s"
      spark.rdd.compress: true
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.shuffle.compress: true
      spark.shuffle.file.buffer: "256k"
      spark.shuffle.io.clientThreads: 16
      spark.shuffle.io.connectionTimeout: "300s"
      spark.shuffle.io.retryWait: "30s"
      spark.shuffle.io.serverThreads: 16
      spark.shuffle.io.threads: 64
      spark.shuffle.spill.compress: true
      spark.shuffle.unsafe.file.output.buffer: 1m
      spark.sql.adaptive.coalescePartitions.minPartitionSize: "256MB"
      spark.sql.adaptive.coalescePartitions.parallelismFirst: false
      spark.sql.adaptive.optimize.skewJoin.enabled: true
      spark.sql.adaptive.skewJoin.skewedPartitionFactor: 3
      spark.sql.adaptive.skewJoin.skewedPartitionThresholdInBytes: "1GB"

      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 1
    worker_conf:
      instance_type: i3en.6xlarge
      memory_on_disk: 256G
  full_patient_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 100
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.memory.offHeap.enabled: true
      spark.memory.offHeap.size: "60g"
      spark.shuffle.io.maxRetries: 8
      spark.shuffle.io.numConnectionsPerPeer: 2
      spark.io.compression.codec: "zstd"
      spark.io.compression.zstd.level: 6
      spark.kryo.referenceTracking: false
      spark.kryo.registrationRequired: false
      spark.kryo.unsafe: true
      spark.kryoserializer.buffer: "128k"
      spark.kryoserializer.buffer.max: "1024m"
      spark.network.timeout: "600s"
      spark.rdd.compress: true
      spark.serializer: "org.apache.spark.serializer.KryoSerializer"
      spark.shuffle.compress: true
      spark.shuffle.file.buffer: "256k"
      spark.shuffle.io.clientThreads: 16
      spark.shuffle.io.connectionTimeout: "300s"
      spark.shuffle.io.retryWait: "30s"
      spark.shuffle.io.serverThreads: 16
      spark.shuffle.io.threads: 64
      spark.shuffle.spill.compress: true
      spark.shuffle.unsafe.file.output.buffer: 1m
      spark.sql.adaptive.coalescePartitions.minPartitionSize: "256MB"
      spark.sql.adaptive.coalescePartitions.parallelismFirst: false
      spark.sql.adaptive.optimize.skewJoin.enabled: true
      spark.sql.adaptive.skewJoin.skewedPartitionFactor: 3
      spark.sql.adaptive.skewJoin.skewedPartitionThresholdInBytes: "1GB"

      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 2000
      spark.task.cpus: 2
    worker_conf:
      instance_type: r5ad.8xlarge
      memory_on_disk: 256G
  full_shard_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 120
      min_executors: 1
    driver_conf:
      instance_type: r5a.4xlarge
      pyspark_memory_overhead: 26500m
      memory_on_disk: ""
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: r5ad.8xlarge
      memory_on_disk: ""
  default:
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.hadoop.parquet.enable.summary-metadata: false
      spark.sql.parquet.mergeSchema: false
      spark.sql.parquet.filterPushdown: true
      spark.sql.hive.metastorePartitionPruning: true
  unarchiver_job_only:
    autoscale:
      enabled: true
      initial_executors: 20
      max_executors: 20
      min_executors: 1
    driver_conf:
      instance_type: r5a.xlarge
      pyspark_memory_overhead: 7000m
    spark_conf:
      spark.default.parallelism: 5000
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
    worker_conf:
      instance_type: c5.xlarge
      pyspark_memory_overhead: 500m
