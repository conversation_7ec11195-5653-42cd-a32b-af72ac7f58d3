defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "loopback"
client: "adn"
revision: "20231024"
is_k8s: False
alert_user: "@rafal.kwiatkowski"
deployment_config:
  research: "demo.aetion.com"
git_branch_override: "adn_loopback_20231024"
hive_vars:
  GDR_END_DATE: "2023-10-24"
  GDR_START_DATE: "1900-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
regenerate_all_flat_tables: False
source_files_password: ""
transform_path: "loopback/adn/current"
upload_bucket: "s3://adn.aetion.com/upload/loopback/20231024"
use_smart_sampling: False
validation_path: "loopback/adn/current"
validation_vars:
  GDR_END_DATE: "2023-10-24"
  GDR_START_DATE: "1900-01-01"
steps:
  unarchiver:
    step_group: "unarchiver_job_only"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 80
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.default.parallelism: 2000
      spark.memory.storageFraction: 0.05
      spark.sql.shuffle.partitions: 20000
  full_patient_job:
    autoscale:
      max_workers: 80
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.default.parallelism: 2000
      spark.memory.storageFraction: 0.05
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
  full_shard_job:
    autoscale:
      max_workers: 80
      min_workers: 1
    driver_node_type_id: "r5a.8xlarge"
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.default.parallelism: 20000
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 2
  default:
    autoscale:
      max_workers: 20
      min_workers: 1
    node_type_id: "c5.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.default.parallelism: 2000
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions: "-Xloggc:/dev/null -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.memory.offHeap.enabled: False
      spark.sql.adaptive.autoBroadcastJoinThreshold: -1
      spark.sql.adaptive.forceOptimizeSkewedJoin: True
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
      spark.sql.shuffle.partitions: 2000
  unarchiver_job_only:
    autoscale:
      max_workers: 16
      min_workers: 1
    driver_node_type_id: "r5a.xlarge"
    node_type_id: "c5.4xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
