defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@oksana.antropova"
client: "adn"
dataset: "omny_sample_opthalmology"
revision: "20230717"
tag: "default"
rdc_dbc_name: omny_sample_opthalmology-default
upload_bucket: "adn.aetion.com/upload/omny_sample_opthalmology/20230717"
deployment_config:
  research: "demo.aetion.com"
hive_vars:
  GDR_END_DATE: "2023-06-07"
  GDR_START_DATE: "2017-04-21"
validation_vars:
  GDR_END_DATE: "2023-06-07"
  GDR_START_DATE: "2017-04-21"
transform_path: "omny_sample_opthalmology/adn/current"
validation_path: "omny_sample_opthalmology/adn/current"
dynamic_patient_table: true
git_branch_override: "adn_omny_sample_20230717"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
is_k8s: false
source_files_password: ""
use_smart_sampling: false
steps_spark_config:
  default:
    num_workers: 2
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
