defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "thin-uk"
client: "adn"
revision: "20220728"
is_k8s: False
alert_user: "@denis.kozlov"
base_params:
  base_paths:
    - /etl/thin-uk/20210723/raw

git_branch_override: "adn-thin_uk-20220728"
hive_vars:
  GDR_END_DATE: "2022-07-18"
  GDR_START_DATE: "1900-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
source_files_password: ""
transform_path: "thin-uk/adn/current"
upload_bucket: "s3://adn.aetion.com/upload/thin-uk/20220728"
validation_path: "thin-uk/adn/current"
validation_vars:
  GDR_END_DATE: "2022-07-18"
  GDR_START_DATE: "1900-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    spark_conf:
      spark.default.parallelism: 6000
      spark.sql.shuffle.partitions: 6000
  default:
    spark_conf:
      aetion.dataset.dynamicCode: True
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
