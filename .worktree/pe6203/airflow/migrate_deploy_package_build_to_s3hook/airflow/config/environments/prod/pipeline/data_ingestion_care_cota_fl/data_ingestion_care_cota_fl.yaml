defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "cota_fl"
client: "care"
revision: "20230927"
is_k8s: False
alert_user: "@oksana.antropova"
deployment_config:
  care: "care.aetion.com"

git_branch_override: "care_cota_fl_20230927"
hive_vars:
  GDR_END_DATE: "2023-06-07"
  GDR_START_DATE: "2017-04-21"
iam_arn: "arn:aws:iam::627533566824:instance-profile/care-databricks-etl-iam"
source_files_password: ""
transform_path: "cota_fl/care/current"
upload_bucket: "care.aetion.com/upload/cota_fl/20230927"
use_smart_sampling: False
validation_path: "cota_fl/care/current"
validation_vars:
  GDR_END_DATE: "2023-06-07"
  GDR_START_DATE: "2017-04-21"
steps_spark_config:
  default:
    num_workers: 2
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
