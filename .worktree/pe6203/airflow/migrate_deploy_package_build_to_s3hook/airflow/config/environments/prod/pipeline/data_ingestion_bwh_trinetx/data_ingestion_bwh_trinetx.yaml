defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "trinetx"
client: "bwh"
revision: "**********"
is_k8s: False
alert_user: "@bartlomiej.cielecki"
deployment_config:
  bwhdope: "bwh-dope.partners.org/aetion"
dynamic_patient_table: True

git_branch_override: "bwh-trinetx-**********"
hive_vars:
  GDR_END_DATE: "2023-08-30"
  GDR_START_DATE: "1915-10-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
pre_partitioned_data_url: "bwh.aetion.com/etl/trinetx/20240123/"
regenerate_all_flat_tables: True
transform_path: "trinetx/bwh/current"
upload_bucket: "bwh.aetion.com/upload/trinetx/**********"
use_smart_sampling: False
validation_path: "trinetx/bwh/current"
validation_vars:
  GDR_END_DATE: "2023-08-30"
  GDR_START_DATE: "1915-10-01"
steps_spark_config:
  full_patient_job:
    node_type_id: "r-fleet.4xlarge"
    spark_conf:
      spark.task.cpus: 1
  unarchiver_job_only:
    autoscale:
      max_workers: 0
      min_workers: 0
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    custom_tags:
      - key: "ResourceClass"
        value: "SingleNode"
    driver_node_type_id: "r6g.4xlarge"
    node_type_id: "r6g.4xlarge"
    num_workers: 0
    spark_conf:
      spark.databricks.cluster.profile: "singleNode"
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.master: "local[*, 4]"
