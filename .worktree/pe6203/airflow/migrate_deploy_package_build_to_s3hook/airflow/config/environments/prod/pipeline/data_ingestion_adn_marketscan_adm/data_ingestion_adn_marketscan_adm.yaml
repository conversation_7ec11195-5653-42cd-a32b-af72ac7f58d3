defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /databricks_adm_step_group_config@steps_spark_config
  - _self_
alert_user: "@mikhail.skiba"
client: "adn"
dataset: "marketscan_adm"
revision: "20241206"
tag: "default"
rdc_dbc_name: marketscan_adm-default
upload_bucket: "adn.aetion.com/upload/marketscan_adm/20241206"
deployment_config:
  dds: "dds.aetion.com"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
transform_path: "marketscan_adm/adn/1.4"
validation_path: "marketscan_adm/1.4"
native_data_for_adm_url: "s3://adn.aetion.com/etl/marketscan/20190601"
dataset_group: "LABS"
dynamic_flat_tables: true
dynamic_patient_table: true
git_branch_override: "adn-maketscan_adm_14-20241206"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
is_k8s: false
regenerate_all_flat_tables: false
sampled_data_percentage: ".5"
source_files_password: ""
use_smart_sampling: false
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 90
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions:
        "-Xloggc:/dev/null -XX:+UseG1GC -XX:+UseStringDeduplication
        -XX:ParallelGCThreads=20 -XX:ConcGCThreads=5 -XX:InitiatingHeapOccupancyPercent=70
        -XX:MaxGCPauseMillis=200"
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.sql.autoBroadcastJoinThreshold: -1
      spark.sql.broadcastTimeout: -1
      spark.sql.shuffle.partitions: 20000
      spark.task.maxDirectResultSize: **********
  default:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 10000
