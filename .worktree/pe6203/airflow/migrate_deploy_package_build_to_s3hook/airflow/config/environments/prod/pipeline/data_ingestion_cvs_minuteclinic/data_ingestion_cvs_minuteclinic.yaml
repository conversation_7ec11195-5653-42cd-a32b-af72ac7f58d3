defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "minuteclinic"
client: "cvs"
revision: "20220812"
is_k8s: False
alert_user: "@nikolay.kharin"
deployment_config:
  aetna: "aetna.aetion.com"
git_branch_override: "remotes/origin/cvs-aetna-20220804"
hive_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/cvs-databricks-etl-iam"
source_files_password: ""
transform_path: "minuteclinic/cvs/20220812"
upload_bucket: "cvs.aetion.com/upload/minuteclinic/20220812"
validation_path: "minuteclinic/cvs/20220812"
validation_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    spark_conf:
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 6000
  default:
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
