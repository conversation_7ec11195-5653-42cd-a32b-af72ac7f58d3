defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "demo"
revision: "202108"
is_k8s: False
alert_user: "@kristina.ushakova"
git_branch_override: "demo-marketscan-replica-20210928"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2018-12-31"
  LAB_START_DATE: "2013-12-31"
iam_arn: "arn:aws:iam::627533566824:instance-profile/demo-databricks-etl-iam"
source_files_password: ""
transform_path: "marketscan/demo20210928"
upload_bucket: "demo.aetion.com/upload/marketscan/202108"
validation_path: "marketscan/demo20210928"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2013-12-31"
steps_spark_config:
  default:
    node_type_id: "rd-fleet.2xlarge"
