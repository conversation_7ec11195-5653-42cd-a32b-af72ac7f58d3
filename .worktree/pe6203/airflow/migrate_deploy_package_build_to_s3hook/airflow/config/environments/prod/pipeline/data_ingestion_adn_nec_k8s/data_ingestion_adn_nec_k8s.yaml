defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@denis.kozlov"
client: "adn"
dataset: "nec"
revision: "********"
tag: "default"
rdc_dbc_name: nec-default
upload_bucket: "adn.aetion.com/upload/nec/********"
deployment_config:
  research: "demo.aetion.com"
hive_vars:
  GDR_END_DATE: "2025-01-31"
  GDR_START_DATE: "1972-02-10"
validation_vars:
  GDR_END_DATE: "2025-01-31"
  GDR_START_DATE: "1972-02-10"
transform_path: "nec/adn/current"
validation_path: "nec/adn/current"
dynamic_patient_table: true
git_branch_override: "adn-nec-********"
is_k8s: true
regenerate_all_flat_tables: true
service_account: "spark-operator-client-adn"
skip_single_shard: true
source_files_password: ""
use_smart_sampling: true
steps_spark_config:
  default:
    autoscale:
      enabled: true
      initial_executors: 2
      max_executors: 5
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "5000m"
      spark_memory_overhead: "5000m"
    worker_conf:
      instance_type: "c5.4xlarge"
