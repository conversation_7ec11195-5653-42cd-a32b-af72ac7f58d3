defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "tempus_keynote45"
client: "care"
revision: "20231127"
is_k8s: False
alert_user: "@bartlomiej.cielecki"
deployment_config:
  research: "demo.aetion.com"

git_branch_override: "care-tempus_keynote45-20231127"
hive_vars:
  GDR_END_DATE: "2022-10-31"
  GDR_START_DATE: "1940-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/care-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "tempus_nsclc_bundle/adn/current"
upload_bucket: "care.aetion.com/upload/tempus_keynote45/20231127"
use_smart_sampling: False
validation_path: "tempus_nsclc_bundle/adn/current"
validation_vars:
  G<PERSON>_END_DATE: "2022-10-31"
  GDR_START_DATE: "1940-01-01"
