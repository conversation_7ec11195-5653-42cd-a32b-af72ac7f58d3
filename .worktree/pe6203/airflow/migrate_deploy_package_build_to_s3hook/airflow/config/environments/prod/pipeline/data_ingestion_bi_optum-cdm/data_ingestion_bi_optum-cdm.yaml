defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@mikhail.koryshov"
client: "bi"
dataset: "optum_cdm"
revision: "20210302"
tag: bi
rdc_dbc_name: optum_cdm-bi
upload_bucket: "bi.aetion.com/upload/optum-cdm/20210302"
deployment_config:
  bi: "bi.aetion.com"

hive_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2007-01-01"
validation_vars:
  GDR_END_DATE: "2021-03-31"
  GDR_START_DATE: "2007-01-01"
transform_path: "optum/cdm/bi/current"
validation_path: "optum/cdm/bibayer"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bi-databricks-etl-iam"
is_k8s: false
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 60
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.broadcastTimeout: 800
      spark.sql.parquet.enableVectorizedReader: false
