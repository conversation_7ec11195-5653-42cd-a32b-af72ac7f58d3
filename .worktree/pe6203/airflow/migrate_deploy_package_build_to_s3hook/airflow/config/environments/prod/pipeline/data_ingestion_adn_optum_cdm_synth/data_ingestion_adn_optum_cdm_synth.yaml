defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum_cdm_synth"
client: "adn"
revision: "20240830"
is_k8s: False
alert_user: "@mikhail.skiba"
deployment_config:
  adn: "demo.aetion.com"
dynamic_patient_table: True

git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2024-03-31"
  GDR_START_DATE: "2000-05-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
regenerate_all_flat_tables: True
skip_single_shard: True
source_files_password: ""
transform_path: "optum/cdm_synth/adn/current"
upload_bucket: "adn.aetion.com/upload/optum_cdm_synth/20240830"
use_smart_sampling: False
validation_path: "optum/cdm_synth/adn/current"
validation_vars:
  GDR_END_DATE: "2024-03-31"
  GDR_START_DATE: "2000-05-01"
