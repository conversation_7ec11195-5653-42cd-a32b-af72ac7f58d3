defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@rafal.kwiatkowski"
client: "cognito"
dataset: "php"
revision: "20230404"
tag: "default"
rdc_dbc_name: php-default
upload_bucket: "cognito.aetion.com/upload/php/20230404"
deployment_config:
  cognito: "cognito.aetion.com"
hive_vars:
  GDR_END_DATE: "2023-01-15"
  GDR_START_DATE: "2001-02-01"
validation_vars:
  GDR_END_DATE: "2023-01-15"
  GDR_START_DATE: "2001-02-01"
transform_path: "php/cognito/current/"
validation_path: "php/cognito/current/"
dynamic_patient_table: true
git_branch_override: "remotes/origin/cognito_providence_20230404"
iam_arn: "arn:aws:iam::627533566824:instance-profile/cognito-databricks-etl-iam"
is_k8s: false
regenerate_all_flat_tables: true
source_files_password: ""
use_smart_sampling: false
steps_spark_config:
  full_job:
    aws_attributes:
      region: ""
    spark_conf:
      spark.default.parallelism: 6000
      spark.sql.parquet.enableVectorizedReader: false
      spark.sql.shuffle.partitions: 6000
  default:
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
      spark.sql.legacy.timeParserPolicy: "CORRECTED"
