defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "flatiron_sclc"
client: "amgen"
revision: "********"
is_k8s: True
alert_user: "@bartlomiej.cielecki"
pyspark_memory_overhead_factor: ".4"
deployment_config:
  amgen: "amgen.aetion.com"
git_branch_override: "amgen_flatiron_sclc_********"
service_account: "spark-operator-client-amgen"
dynamic_patient_table: True
regenerate_all_flat_tables: True

skip_single_shard: True
rde_revision_check: "********"
source_files_password: ""
transform_path: "flatiron/amgen/sclc/current"
upload_bucket: "amgen.aetion.com/upload/flatiron_sclc/********"
validation_path: "flatiron/amgen/sclc/current"
hive_vars:
  GDR_END_DATE: "2024-12-29"
  GDR_START_DATE: "1900-01-01"
  MAX_DATE: "9999-01-01"
validation_vars:
  GDR_END_DATE: "2024-12-29"
  GDR_START_DATE: "1900-01-01"
steps_spark_config:
  full_job:
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.memory.offHeap.enabled: False
      spark.sql.parquet.enableVectorizedReader: False
      spark.task.cpus: 2
    autoscale:
      enabled: True
      min_executors: 1
      initial_executors: 1
      max_executors: 20
  default:
    autoscale:
      enabled: True
      min_executors: 1
      initial_executors: 1
      max_executors: 20
    worker_conf:
      instance_type: "r5.4xlarge"
    driver_conf:
      instance_type: "r5.8xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.task.cpus: 2
      spark.driver.maxResultSize: "0g"
