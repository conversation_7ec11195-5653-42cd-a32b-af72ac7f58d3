defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "optum_market_clarity_hemotology"
client: "bms"
revision: "20241022"
is_k8s: False
alert_user: "@nikolay.kharin"
deployment_config:
  bms: "bms.aetion.com"
dynamic_patient_table: True

git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "2007-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bms-databricks-etl-iam"
regenerate_all_flat_tables: True
transform_path: "humedica/optum_market_clarity_hemotology/bms/current"
upload_bucket: "bms.aetion.com/upload/optum_market_clarity_hemotology/20241022"
use_smart_sampling: False
validation_path: "humedica/optum_market_clarity_hemotology/bms/current"
validation_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "2007-01-01"
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 40
      min_workers: 40
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 10000
  full_patient_job:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "i3en.6xlarge"
    spark_conf:
      spark.sql.shuffle.partitions: 20000
  full_shard_job:
    autoscale:
      max_workers: 40
      min_workers: 1
  default:
    autoscale:
      max_workers: 20
      min_workers: 20
    node_type_id: "r-fleet.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
  unarchiver_job_only:
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
