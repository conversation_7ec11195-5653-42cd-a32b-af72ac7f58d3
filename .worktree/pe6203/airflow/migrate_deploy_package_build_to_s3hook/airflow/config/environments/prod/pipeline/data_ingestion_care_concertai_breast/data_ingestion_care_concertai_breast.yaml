defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@Damian"
client: "care"
dataset: "concertai_breast"
revision: "**********"
tag: "default"
rdc_dbc_name: concertai_breast-default
upload_bucket: "care.aetion.com/upload/concertai_breast/**********"
deployment_config:
  care: "care.aetion.com"
hive_vars:
  GDR_END_DATE: "2023-08-06"
  GDR_START_DATE: "1934-07-08"
validation_vars:
  GDR_END_DATE: "2023-08-06"
  GDR_START_DATE: "1934-07-08"
transform_path: "concertai_breast/care/current"
validation_path: "concertai_breast/care/current"
dynamic_patient_table: false
git_branch_override: "care_concertai_breast_2023100901"
iam_arn: "arn:aws:iam::627533566824:instance-profile/care-databricks-etl-iam"
is_k8s: false
pre_partitioned_data_url: "care.aetion.com/etl/concertai_breast/20231009"
source_files_password: ""
