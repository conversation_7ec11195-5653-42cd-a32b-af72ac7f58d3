defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "mdv"
client: "amgen"
revision: "20240917"
is_k8s: False
alert_user: "@evgeniy.varganov"
deployment_config:
  amgen: "amgen.aetion.com"
dynamic_patient_table: True

git_branch_override: "amgen-mdv-20240917"
hive_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "1911-07-22"
iam_arn: "arn:aws:iam::627533566824:instance-profile/amgen-databricks-etl-iam"
regenerate_all_flat_tables: False
source_files_password: ""
transform_path: "mdv/amgen/current"
upload_bucket: "s3://amgen.aetion.com/upload/mdv/20240917"
use_smart_sampling: False
validation_path: "mdv/amgen/current"
validation_vars:
  G<PERSON>_END_DATE: "2024-06-30"
  GDR_START_DATE: "1911-07-22"
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 50
  full_patient_job:
    autoscale:
      max_workers: 10
      min_workers: 1
    driver_node_type_id: "rg-fleet.2xlarge"
    node_type_id: "rg-fleet.4xlarge"
  default:
    num_workers: 10
    spark_conf:
      spark.default.parallelism: 1000
      spark.driver.maxResultSize: 0
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.sql.shuffle.partitions: 1000
    spark_version: "14.3.x-scala2.12"
  unarchiver_job_only:
    autoscale:
      max_workers: 0
      min_workers: 0
    custom_tags:
      - key: "ResourceClass"
        value: "SingleNode"
    driver_node_type_id: "r6g.4xlarge"
    node_type_id: "r6g.4xlarge"
    num_workers: 0
    spark_conf:
      spark.databricks.cluster.profile: "singleNode"
      spark.hadoop.fs.s3a.connection.maximum: 100
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.master: "local[*, 4]"
