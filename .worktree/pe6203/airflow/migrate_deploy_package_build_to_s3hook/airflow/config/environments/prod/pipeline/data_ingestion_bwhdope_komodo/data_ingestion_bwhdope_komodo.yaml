defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@mikhail.skiba"
client: "bwhdope"
dataset: "komodo"
revision: "20250206"
tag: "bwh"
rdc_dbc_name: komodo-bwh
upload_bucket: "s3://bwhdope.aetion.com/upload/komodo/20250206"
deployment_config:
  bwhdope: "bwh-dope.partners.org"
hive_vars:
  GDR_END_DATE: "2024-05-30"
  GDR_START_DATE: "2010-01-01"
validation_vars:
  GDR_END_DATE: "2024-05-30"
  GDR_START_DATE: "2010-01-01"
transform_path: "komodo/bwh/current"
validation_path: "komodo/bwh/current"
dynamic_patient_table: true
git_branch_override: "bwh_komodo_20250207"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
is_k8s: false
rde_revision_check: "20231004"
regenerate_all_flat_tables: true
source_files_password: ""
steps_spark_config:
  full_job:
    autoscale:
      max_workers: 40
      min_workers: 1
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: false
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.memory.offHeap.enabled: false
    spark_env_vars:
      JNAME: "zulu17-ca-amd64"
    spark_version: "16.2.x-scala2.12"
  unarchiver_job_only:
    autoscale:
      max_workers: 30
      min_workers: 1
    node_type_id: "c5.4xlarge"
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 104857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
