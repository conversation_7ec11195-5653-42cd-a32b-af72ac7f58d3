defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@rafal.kwi<PERSON>kowski"
client: "astellas"
dataset: "iqvia_pharmetrics"
revision: "20240111"
tag: "default"
rdc_dbc_name: iqvia_pharmetrics-default
upload_bucket: "astellas.aetion.com/upload/iqvia_pharmetrics/20240111"
deployment_config:
  astellas: "astellas.aetion.com"
hive_vars:
  GDR_END_DATE: "2023-09-30"
  GDR_START_DATE: "2010-01-01"
validation_vars:
  GDR_END_DATE: "2023-09-30"
  GDR_START_DATE: "2010-01-01"
transform_path: "iqvia_pharmetrics/astellas/current"
validation_path: "iqvia_pharmetrics/astellas/current"
dynamic_patient_table: true
git_branch_override: "astellas_iqvia_pharmetrics_20240111_native"
iam_arn: "arn:aws:iam::627533566824:instance-profile/astellas-databricks-etl-iam"
is_k8s: false
source_files_password: ""
use_smart_sampling: false
