defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@nikolay.kharin"
client: "adn"
dataset: "xcures_aml"
revision: "**********"
tag: "default"
rdc_dbc_name: xcures_aml-default
upload_bucket: "adn.aetion.com/upload/xcures_aml/**********"
deployment_config:
  research: "demo.aetion.com"
hive_vars:
  GDR_END_DATE: "2023-08-19"
  GDR_START_DATE: "1953-02-11"
validation_vars:
  GDR_END_DATE: "2023-08-19"
  GDR_START_DATE: "1953-02-11"
transform_path: "xcures_aml/adn/current"
validation_path: "xcures_aml/adn/current"
dynamic_patient_table: true
git_branch_override: "adn_xcures_aml_fix"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
is_k8s: false
pre_partitioned_data_url: "adn.aetion.com/etl/xcures_aml/20231103/"
regenerate_all_flat_tables: true
skip_single_shard: true
source_files_password: ""
use_smart_sampling: false
