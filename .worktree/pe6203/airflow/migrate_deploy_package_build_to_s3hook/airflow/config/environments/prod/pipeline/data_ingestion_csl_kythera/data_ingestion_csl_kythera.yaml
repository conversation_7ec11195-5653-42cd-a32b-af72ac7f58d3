defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "kythera"
client: "csl"
revision: "**********"
is_k8s: False
alert_user: "@rafal.kwiatkowski"
deployment_config:
  csl: "csl.aetion.com"
dynamic_patient_table: True
git_branch_override: "csl_kythera_20241024"
hive_vars:
  GDR_END_DATE: "2024-07-31"
  GDR_START_DATE: "1957-05-19"
iam_arn: "arn:aws:iam::627533566824:instance-profile/csl-databricks-etl-iam"
pre_partitioned_data_url: "csl.aetion.com/etl/kythera/20241024"
rde_revision_check: "20241024"
regenerate_all_flat_tables: True
skip_single_shard: True
transform_path: "kythera/csl/current"
upload_bucket: "csl.aetion.com/upload/kythera/**********"
validation_path: "kythera/csl/current"
validation_vars:
  GDR_END_DATE: "2024-07-31"
  GDR_START_DATE: "1957-05-19"
