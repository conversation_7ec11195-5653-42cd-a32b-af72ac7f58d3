defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "mdv"
client: "bi"
revision: "20210302"
is_k8s: False
git_branch_override: "remotes/origin/bi_mdv_20210302"
hive_vars:
  GDR_END_DATE: "2020-09-30"
  GDR_START_DATE: "1900-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bi-databricks-etl-iam"
source_files_password: ""
transform_path: "mdv/bi20210302"
upload_bucket: "bi.aetion.com/upload/mdv/20210302"
validation_path: "mdv/bi20210302"
