defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "cognito"
revision: "20211223"
is_k8s: False
alert_user: "@aleksei.beltyukov"
data_cuts:
  - hive_vars:
      DESCR: "AEP 5 Year Bundle"
      GDR_END_DATE: "2021-09-01"
      GDR_START_DATE: "2014-12-31"
      TAG: "5y"
    name: "5y"
    validation_vars:
      GDR_END_DATE: "2021-09-01"
      GDR_START_DATE: "2014-12-31"
deployment_config:
  cognito: "cognito.aetion.com"
git_branch_override: "remotes/origin/cognito_MarketScan_20211223"
iam_arn: "arn:aws:iam::627533566824:instance-profile/cognito-databricks-etl-iam"
source_files_password: ""
transform_path: "marketscan_wo_labs/cognito20211223"
upload_bucket: "cognito.aetion.com/upload/marketscan/20211223"
validation_path: "marketscan_wo_labs/cognito20211223"
steps_spark_config:
  full_job:
    num_workers: 40
