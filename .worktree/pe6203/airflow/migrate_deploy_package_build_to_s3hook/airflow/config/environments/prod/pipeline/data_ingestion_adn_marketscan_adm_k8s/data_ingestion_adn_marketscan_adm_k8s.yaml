defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /k8s_adm_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_adm"
client: "adn"
revision: "********"
is_k8s: True
alert_user: "@mikhail.skiba"
dataset_group: "LABS"
deployment_config:
  dds: "dds.aetion.com"
dynamic_flat_tables: True
dynamic_patient_table: True
git_branch_override: "adn-maketscan_adm_14-********"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
native_data_for_adm_url: "s3://adn.aetion.com/etl/marketscan/********"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: True
sampled_data_percentage: ".5"
service_account: "spark-operator-client-adn"
source_files_password: ""
spark_memory_overhead_factor: ".2"
transform_path: "marketscan_adm/adn/1.4"
upload_bucket: "adn.aetion.com/upload/marketscan_adm/********"
use_smart_sampling: False
validation_path: "adm/1.4"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
steps_spark_config:
  adm_full_job:
    autoscale:
      enabled: True
      initial_executors: 40
      max_executors: 80
      min_executors: 40
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: False
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "i3en.6xlarge"
  full_job:
    autoscale:
      enabled: True
      initial_executors: 40
      max_executors: 80
      min_executors: 40
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.memory.offHeap.enabled: False
      spark.sql.broadcastTimeout: -1
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 20000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "i3en.6xlarge"
  full_patient_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 140000
      spark.task.cpus: 3
    worker_conf:
      instance_type: "r5ad.8xlarge"
  full_shard_job:
    autoscale:
      enabled: True
      initial_executors: 1
      max_executors: 60
      min_executors: 1
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      spark.task.cpus: 2
    worker_conf:
      instance_type: "r5ad.8xlarge"
  default:
    autoscale:
      enabled: True
      initial_executors: 10
      max_executors: 30
      min_executors: 10
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.sql.shuffle.partitions: 10000
      spark.task.cpus: 1
    worker_conf:
      instance_type: "r5ad.xlarge"
