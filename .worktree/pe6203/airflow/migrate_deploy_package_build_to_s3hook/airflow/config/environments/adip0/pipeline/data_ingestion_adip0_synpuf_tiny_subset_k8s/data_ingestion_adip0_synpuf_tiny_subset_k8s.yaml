defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "adip0"
dataset: "synpuf_tiny_subset"
revision: "********"
tag: "default"
rdc_dbc_name: synpuf_tiny_subset-default
upload_bucket: "s3://adip0.app.dev.aetion.com/upload/synpuf_tiny_subset/********"
deployment_config:
  aep-dev: "adip0.app.dev.aetion.com"
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
transform_path: "synpuf_tiny_subset/current"
validation_path: "synpuf_tiny_subset/current"
celeborn_feature:
  enabled: true
copy_spec: false
domain: "app.dev.aetion.com"
dynamic_flat_tables: false
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
is_k8s: true
rde_revision_check: "********"
regenerate_all_flat_tables: true
service_account: "spark-operator-client-common"
source_files_password: ""
steps_spark_config:
  full_job:
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "45g"
      spark_memory_overhead: "45g"
    worker_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "45g"
      spark_memory_overhead: "45g"
  full_shard_job:
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "45g"
      spark_memory_overhead: "45g"
    worker_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "45g"
      spark_memory_overhead: "45g"
  default:
    hadoop_conf:
      fs.s3a.aws.credentials.provider: "com.amazonaws.auth.WebIdentityTokenCredentialsProvider"
    spark_conf:
      spark.driver.userClassPathFirst: false
      spark.executor.userClassPathFirst: false
