defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "jmdc"
dataset: "jmdc_payer"
revision: "201906"
tag: "DEFAULT"
rdc_dbc_name: jmdc_payer-DEFAULT
upload_bucket: "jmdc.aetion.com/upload/jmdc_payer/201906"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2000-05-01"
transform_path: "jmdc_payer/201812"
validation_path: "jmdc_payer/201812"
iam_arn: "arn:aws:iam::627533566824:instance-profile/jmdc-databricks-etl-iam"
is_k8s: false
source_files_password: ""
