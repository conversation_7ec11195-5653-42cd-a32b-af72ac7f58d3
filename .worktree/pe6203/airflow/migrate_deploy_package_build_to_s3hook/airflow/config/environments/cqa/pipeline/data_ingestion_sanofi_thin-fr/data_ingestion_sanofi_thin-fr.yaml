defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "sanofi"
dataset: "thin_fr"
revision: "20191206"
tag: sanofi
rdc_dbc_name: thin_fr-sanofi
upload_bucket: "s3://sanofi.aetion.com/upload/thin-fr/20191206"
hive_vars:
  GDR_END_DATE: "2019-07-02"
  GDR_START_DATE: "1968-01-01"
transform_path: "thin-fr/sanofi/20191206"
validation_path: "thin-fr/sanofi/20191206"
git_branch_override: "remotes/origin/thin-france-20191206"
iam_arn: "arn:aws:iam::627533566824:instance-profile/sanofi-databricks-etl-iam"
is_k8s: false
source_files_password: ""
