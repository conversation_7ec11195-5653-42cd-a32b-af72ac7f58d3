defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@katharine.fuzesi"
client: "abbvie"
dataset: "optum_cdm"
revision: "20211201"
tag: abbvie
rdc_dbc_name: optum_cdm-abbvie
upload_bucket: "abbvie.aetion.com/upload/optum-cdm/20211201"
deployment_config:
  abbvie: "abbvie.aetion.com"
hive_vars:
  GDR_END_DATE: "2021-06-30"
  GDR_START_DATE: "2007-01-01"
validation_vars:
  GDR_END_DATE: "2021-06-30"
  GDR_START_DATE: "2007-01-01"
transform_path: "optum/cdm/abbvie/current"
validation_path: "optum/cdm/abbvie"
git_branch_override: "remotes/origin/optum-cdm-20211201-fix"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
is_k8s: false
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 60
    spark_conf:
      spark.executor.cores: 8
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.network.timeout: 800
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.parquet.enableVectorizedReader: false
  default:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.8xlarge"
    spark_conf:
      spark.executor.cores: 8
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.io.compression.codec: "snappy"
      spark.network.timeout: 800
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.parquet.enableVectorizedReader: false
