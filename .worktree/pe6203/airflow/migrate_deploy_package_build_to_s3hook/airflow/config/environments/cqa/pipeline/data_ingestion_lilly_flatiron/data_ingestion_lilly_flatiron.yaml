defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "flatiron"
client: "lilly"
revision: "20210107"
is_k8s: False
hive_vars:
  GDR_END_DATE: "2020-11-30"
  GDR_START_DATE: "1990-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/lilly-databricks-etl-iam"
source_files_password: ""
transform_path: "flatiron/lilly/20210107/nsclc"
upload_bucket: "lilly.aetion.com/upload/flatiron/20210107"
validation_path: "flatiron/lilly/20210107/nsclc"
steps_spark_config:
  full_job:
    node_type_id: "rd-fleet.xlarge"
  default:
    num_workers: 4
