defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "healthverity"
dataset: "healthverity_b1"
revision: "20210601"
tag: "default"
rdc_dbc_name: healthverity_b1-default
upload_bucket: "healthverity.aetion.com/upload/healthverity_b1/20210601"
hive_vars:
  GDR_START_DATE: "2018-12-01"
  GDR_END_DATE: "2021-06-01"
  REVISION: "20210601"
transform_path: "healthverity/healthverity_b1/20210503"
validation_path: "healthverity/healthverity_b1/20210405"
iam_arn: "arn:aws:iam::627533566824:instance-profile/healthverity-databricks-etl-iam"
is_k8s: false
pre_partitioned_data_url: "healthverity.aetion.com/etl/healthverity/20210601/"
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 80
  default:
    aws_attributes:
      availability: "ON_DEMAND"
      ebs_volume_count: 1
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
      spot_bid_price_percent: 100
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 40
    spark_conf:
      spark.network.timeout: 1200
      spark.sql.adaptive.enabled: true
      spark.sql.adaptive.skewJoin.enabled: true
      spark.sql.adaptive.localShuffleReader.enabled: true
      spark.sql.adaptive.coalescePartitions.enabled: true
      spark.sql.legacy.timeParserPolicy: "LEGACY"
      spark.sql.parquet.binaryAsString: true
      spark.driver.extraJavaOptions: "-Xloggc:/dev/null"
      spark.driver.maxResultSize: 0
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: 2097152000
      spark.io.compression.codec: "snappy"
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.broadcastTimeout: 1200
      spark.sql.shuffle.partitions: 1000
      spark.sql.parquet.enableVectorizedReader: false
      spark.task.maxDirectResultSize: 2097152000
