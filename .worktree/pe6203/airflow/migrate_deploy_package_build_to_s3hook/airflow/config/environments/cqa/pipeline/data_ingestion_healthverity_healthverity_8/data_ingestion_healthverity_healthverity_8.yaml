defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "healthverity"
dataset: "healthverity_8"
revision: "20200429"
tag: "default"
rdc_dbc_name: healthverity_8-default
upload_bucket: "healthverity.aetion.com/upload/healthverity_8/20200429"
hive_vars:
  GDR_END_DATE: "2020-04-29"
  GDR_START_DATE: "2018-12-01"
transform_path: "healthverity/healthverity_b2/20200429"
validation_path: "healthverity/healthverity_b2/20200429"
git_branch_override: "remotes/origin/healthverity_b2_20200411"
iam_arn: "arn:aws:iam::627533566824:instance-profile/healthverity-databricks-etl-iam"
is_k8s: false
source_files_password: ""
steps_spark_config:
  default:
    aws_attributes:
      availability: "ON_DEMAND"
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
      spot_bid_price_percent: 100
      zone_id: "us-east-1b"
    node_type_id: "rd-fleet.xlarge"
    num_workers: 30
    spark_conf:
      spark.io.compression.codec: "snappy"
      spark.sql.broadcastTimeout: 800
