defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "cprd_nested"
client: "servier"
revision: "201907"
is_k8s: False
git_branch_override: "remotes/origin/servier-cprd-201907"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "1987-11-21"
iam_arn: "arn:aws:iam::627533566824:instance-profile/servier-databricks-etl-iam"
source_files_password: ""
transform_path: "cprd/servier201907_nested"
upload_bucket: "servier.aetion.com/upload/cprd_nested/201907"
validation_path: "cprd/servier201907_nested"
steps_spark_config:
  full_shard_job:
    spark_conf:
      spark.sql.parquet.enableVectorizedReader: False
  unarchiver_job_only:
    aws_attributes:
      ebs_volume_count: 1
      ebs_volume_size: 2048
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    init_scripts:
      - s3:
          destination: "s3://databricks.aetion.com/artifacts/automated_data_ingestion/prod/unarchiver_cluster_init.sh"
          region: ""
    num_workers: 3
    spark_conf:
      spark.executor.cores: "1"
    spark_env_vars:
      export TMPDIR: "/local_disk0/tmp"
