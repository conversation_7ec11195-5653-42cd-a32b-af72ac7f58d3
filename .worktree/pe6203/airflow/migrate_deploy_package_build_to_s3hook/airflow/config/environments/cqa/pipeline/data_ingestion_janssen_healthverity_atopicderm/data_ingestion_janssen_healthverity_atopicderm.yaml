defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "healthverity_atopicderm"
client: "janssen"
revision: "20210128"
is_k8s: False
git_branch_override: "remotes/origin/janssen-healthverity-atopicderm-20210128"
hive_vars:
  GDR_END_DATE: "2020-06-30"
  GDR_START_DATE: "2017-03-01"
  REVISION: "20210128"
iam_arn: "arn:aws:iam::627533566824:instance-profile/janssen-databricks-etl-iam"
source_files_password: ""
transform_path: "healthverity/atopic_derm/20210128"
upload_bucket: "janssen.aetion.com/upload/healthverity_atopicderm/20210128"
validation_path: "healthverity/atopic_derm/20210128"
steps_spark_config:
  full_job:
    spark_conf:
      spark.default.parallelism: 1200
  full_shard_job:
    spark_conf:
      spark.default.parallelism: 1200
