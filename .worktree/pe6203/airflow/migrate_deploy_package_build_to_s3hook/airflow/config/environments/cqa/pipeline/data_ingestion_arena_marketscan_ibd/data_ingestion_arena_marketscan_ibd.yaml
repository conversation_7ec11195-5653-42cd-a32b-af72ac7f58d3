defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "arena"
dataset: "marketscan_ibd"
revision: "20191018"
tag: "ARENA"
rdc_dbc_name: marketscan_ibd-ARENA
upload_bucket: "arena.aetion.com/upload/marketscan_ibd/20191018"
transform_path: "marketscan_ibd/arena20191018"
validation_path: "marketscan_ibd/arena20191018"
data_cuts:
  - hive_vars:
      GDR_END_DATE: "2018-12-31"
      GDR_START_DATE: "2013-12-31"
      LAB_END_DATE: "2018-12-31"
      LAB_START_DATE: "2013-12-31"
    name: "default"
iam_arn: "arn:aws:iam::627533566824:instance-profile/arena-databricks-etl-iam"
is_k8s: false
source_files_password: ""
