defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "adn"
dataset: "marketscan_random"
revision: "201910"
tag: 10M_RANDOM_SAMPLE
rdc_dbc_name: marketscan_random-10M_RANDOM_SAMPLE
upload_bucket: "adn.aetion.com/upload/marketscan_random/201910"
hive_vars:
  GDR_END_DATE: "2017-12-31"
  GDR_START_DATE: "2012-12-31"
  LAB_END_DATE: "2017-12-31"
  LAB_START_DATE: "2012-12-31"
transform_path: "marketscan_random/adn201910"
validation_path: "marketscan_random/adn201910"
git_branch_override: "remotes/origin/marketscan_10mrandom"
iam_arn: "arn:aws:iam::627533566824:instance-profile/adn-databricks-etl-iam"
is_k8s: false
source_files_password: ""
