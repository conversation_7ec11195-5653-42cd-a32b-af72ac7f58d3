defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "abbvie"
revision: "20210519"
is_k8s: False
git_branch_override: "remotes/origin/abbvie_marketscan_20210519"
hive_vars:
  EV_END_DATE: "2021-04-30"
  EV_START_DATE: "2020-03-31"
  GDR_END_DATE: "2021-04-30"
  GDR_START_DATE: "1999-12-31"
  LAB_END_DATE: "2021-04-30"
  LAB_START_DATE: "1999-12-31"
  REVISION: "20210519"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
source_files_password: ""
transform_path: "marketscan/abbvie20210519"
upload_bucket: "abbvie.aetion.com/upload/marketscan/20210519"
validation_path: "marketscan/abbvie20210519"
steps_spark_config:
  default:
    spark_conf:
      spark.driver.maxResultSize: "50g"
      spark.sql.parquet.enableVectorizedReader: False
      spark.sql.shuffle.partitions: 800
