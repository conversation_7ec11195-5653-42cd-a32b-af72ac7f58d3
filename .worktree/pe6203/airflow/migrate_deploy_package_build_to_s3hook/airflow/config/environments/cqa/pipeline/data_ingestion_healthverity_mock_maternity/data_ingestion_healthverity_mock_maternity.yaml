defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "mock_maternity"
client: "healthverity"
revision: "v1"
is_k8s: False
git_branch_override: "remotes/origin/healthverity-materninty-mockdata"
hive_vars:
  GDR_END_DATE: "2020-10-31"
  GDR_START_DATE: "2018-12-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/healthverity-databricks-etl-iam"
transform_path: "healthverity/materninty/mockdata"
upload_bucket: "healthverity.aetion.com/upload/mock_maternity/v1"
validation_path: "healthverity/materninty/mockdata"
steps_spark_config:
  full_job:
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 40
