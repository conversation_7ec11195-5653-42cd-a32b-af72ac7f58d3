defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "flatiron_mbreast"
client: "lilly"
revision: "20200106"
is_k8s: False
hive_vars:
  GDR_END_DATE: "2019-11-30"
  GDR_START_DATE: "1990-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/lilly-databricks-etl-iam"
source_files_password: ""
transform_path: "flatiron/20200106/mbreast"
upload_bucket: "lilly.aetion.com/upload/flatiron_mbreast/20200106"
validation_path: "flatiron/20200106/mbreast"
git_branch_override: "remotes/origin/lilly-flatiron-mbreast-20200106"
steps_spark_config:
  default:
    num_workers: 4
  full_job:
    node_type_id: "rd-fleet.xlarge"
