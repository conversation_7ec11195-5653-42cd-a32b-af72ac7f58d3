defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "mdv_oncology"
client: "lilly"
revision: "20191126"
is_k8s: False
git_branch_override: "remotes/origin/lilly-mdv-oncology-20191126"
hive_vars:
  ENR_END_DATE: "2019-09-30"
  ENR_START_DATE: "1900-01-01"
  GDR_END_DATE: "2019-09-30"
  GDR_START_DATE: "1900-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/lilly-databricks-etl-iam"
source_files_password: ""
transform_path: "mdv/lilly20191126/oncology"
upload_bucket: "lilly.aetion.com/upload/mdv_oncology/20191126"
validation_path: "mdv/lilly20191126/oncology"
