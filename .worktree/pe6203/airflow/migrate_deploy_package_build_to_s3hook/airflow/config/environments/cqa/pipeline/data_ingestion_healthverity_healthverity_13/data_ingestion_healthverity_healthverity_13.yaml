defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "healthverity"
dataset: "healthverity_13"
revision: "20200928tune"
tag: "default"
rdc_dbc_name: healthverity_13-default
upload_bucket: "healthverity.aetion.com/upload/healthverity_13/20200928tune"
hive_vars:
  GDR_END_DATE: "2020-09-28"
  GDR_START_DATE: "2018-12-01"
transform_path: "healthverity/healthverity_13/20200928"
validation_path: "healthverity/healthverity_13/20200928"
git_branch_override: "remotes/origin/healthverity_all_tune"
iam_arn: "arn:aws:iam::627533566824:instance-profile/healthverity-databricks-etl-iam"
is_k8s: false
pre_partitioned_data_url: "healthverity.aetion.com/etl/healthverity/20200928/"
source_files_password: ""
steps_spark_config:
  full_job:
    enable_elastic_disk: true
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 60
    spark_conf:
      spark.default.parallelism: 8000
      spark.executor.cores: 4
      spark.sql.shuffle.partitions: 8000
  default:
    aws_attributes:
      availability: "ON_DEMAND"
      ebs_volume_count: 1
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
      spot_bid_price_percent: 100
    node_type_id: "rd-fleet.4xlarge"
    num_workers: 60
    spark_conf:
      spark.default.parallelism: 4000
      spark.driver.maxResultSize: 0
      spark.hadoop.fs.s3a.connection.timeout: 600000
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.io.compression.codec: "snappy"
      spark.network.timeout: 1200
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.broadcastTimeout: 1200
      spark.task.maxDirectResultSize: **********
