defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "cmsnoac"
client: "bwh"
revision: "20200303"
is_k8s: False
git_branch_override: "remotes/origin/dbc-cmsnoac-20200303"
hive_vars:
  GDR_END_DATE: "2017-12-31"
  GDR_START_DATE: "2008-12-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
source_files_password: ""
transform_path: "cms/noac/201910"
upload_bucket: "bwh.aetion.com/upload/cmsnoac/20200303"
validation_path: "cms/noac/201910"
steps_spark_config:
  full_shard_job:
    spark_conf:
      spark.driver.maxResultSize: "200g"
      spark.sql.shuffle.partitions: 1000
  default:
    spark_conf:
      spark.hadoop.fs.s3a.connection.maximum: 500
      spark.hadoop.fs.s3a.connection.timeout: 600000
