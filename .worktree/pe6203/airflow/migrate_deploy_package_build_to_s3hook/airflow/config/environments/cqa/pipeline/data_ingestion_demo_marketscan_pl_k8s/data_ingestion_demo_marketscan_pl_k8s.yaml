defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan_pl"
client: "demo"
revision: "********"
is_k8s: True
alert_user: "@dev-alert-airflow"
copy_spec: False
deployment_config:
  demo: "demo.aetion.com"
domain: "aetion.com"
dynamic_flat_tables: False
dynamic_patient_table: True
git_branch_override: "remotes/origin/marketscan_pl_preprocess"
hive_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2018-09-30"
  LAB_START_DATE: "2013-12-31"
preprocess: True
service_account: "spark-operator-client-demo"
source_files_password: ""
transform_path: "marketscan_pl/demo/********"
upload_bucket: "demo.aetion.com/upload/marketscan_pl/********"
validation_path: "marketscan_pl/demo/********"
validation_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2013-12-31"
