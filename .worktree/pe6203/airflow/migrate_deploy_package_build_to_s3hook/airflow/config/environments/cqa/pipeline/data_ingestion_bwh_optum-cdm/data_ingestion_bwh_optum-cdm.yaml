defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "bwh"
dataset: "optum_cdm"
revision: "201812"
tag: "bwh"
rdc_dbc_name: optum_cdm-bwh
upload_bucket: "bwh.aetion.com/upload/optum-cdm/201812"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2007-01-01"
transform_path: "optum/cdm/bwh/201812"
validation_path: "optum/cdm/bwh201812"
git_branch_override: "remotes/origin/bwh-optum-cdm-pipeline"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
is_k8s: false
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 5
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 20
    spark_conf:
      spark.default.parallelism: 1000
      spark.sql.shuffle.partitions: 1000
