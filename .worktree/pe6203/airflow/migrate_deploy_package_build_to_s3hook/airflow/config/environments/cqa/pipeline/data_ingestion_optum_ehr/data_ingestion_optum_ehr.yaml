defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "ehr"
client: "optum"
revision: "201806"
is_k8s: False
iam_arn: "arn:aws:iam::627533566824:instance-profile/optum-databricks-etl-iam"
source_files_password: ""
transform_path: "humedica/ehrclaims201806"
upload_bucket: "optum.aetion.com/upload/ehr/201806"
validation_path: "humedica/ehrclaims201806"
