defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "cprd"
client: "cprd"
revision: "201812"
is_k8s: False
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "1987-11-21"
iam_arn: "arn:aws:iam::627533566824:instance-profile/cprd-databricks-etl-iam"
source_files_password: "iforget"
transform_path: "cprd/201812"
upload_bucket: "cprd.aetion.com/upload/cprd/201812"
validation_path: "cprd/201812"
steps_spark_config:
  unarchiver_job_only:
    aws_attributes:
      ebs_volume_count: 1
      ebs_volume_size: 2048
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    init_scripts:
      - s3:
          destination: "s3://databricks.aetion.com/artifacts/automated_data_ingestion/prod/unarchiver_cluster_init.sh"
          region: ""
    num_workers: 3
    spark_conf:
      spark.executor.cores: "1"
    spark_env_vars:
      export TMPDIR: "/local_disk0/tmp"
