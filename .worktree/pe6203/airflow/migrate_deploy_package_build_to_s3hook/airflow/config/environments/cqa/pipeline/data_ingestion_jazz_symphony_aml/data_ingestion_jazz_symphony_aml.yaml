defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "symphony_aml"
client: "jazz"
revision: "20230530"
is_k8s: False
alert_user: "@nikolay.kharin"
base_params:
  base_paths:
    - /etl/symphony_aml/20230328/raw
    - /etl/symphony_aml/20230427/raw
  revision_start_date: "2015-01-01"
deployment_config:
  jazz: "jazz.aetion.com"
git_branch_override: "master"
hive_vars:
  GDR_END_DATE: "2023-04-30"
  GDR_START_DATE: "2015-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/jazz-databricks-etl-iam"
regenerate_all_flat_tables: True
source_files_password: ""
transform_path: "symphony_aml/jazz/current"
upload_bucket: "jazz.aetion.com/upload/symphony_aml/20230530"
use_smart_sampling: False
validation_path: "symphony_aml/jazz/current"
validation_vars:
  GDR_END_DATE: "2023-04-30"
  GDR_START_DATE: "2015-01-01"
steps_spark_config:
  full_job:
    node_type_id: "i3.8xlarge"
    num_workers: 120
    spark_conf:
      spark.default.parallelism: 8000
      spark.driver.maxResultSize: "0G"
      spark.executor.cores: 16
      spark.io.compression.codec: "snappy"
      spark.network.timeout: 1200
      spark.sql.broadcastTimeout: 1200
      spark.sql.parquet.enableVectorizedReader: False
  default:
    node_type_id: "rd-fleet.4xlarge"
    spark_conf:
      aetion.dataset.automaticGdrFiltering: False
      spark.default.parallelism: 4000
      spark.driver.maxResultSize: "0g"
      spark.executor.cores: 8
      spark.sql.autoBroadcastJoinThreshold: 209715200
      spark.sql.broadcastTimeout: 12000
      spark.sql.shuffle.partitions: 2000
      spark.task.maxDirectResultSize: 2097152000
