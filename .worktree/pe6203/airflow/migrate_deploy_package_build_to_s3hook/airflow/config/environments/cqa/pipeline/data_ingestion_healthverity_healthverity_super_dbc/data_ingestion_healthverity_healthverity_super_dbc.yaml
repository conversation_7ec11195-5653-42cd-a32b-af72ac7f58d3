defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@mikhail.koryshov"
client: "healthverity"
dataset: "healthverity_super_dbc"
revision: "20220725"
tag: "default"
rdc_dbc_name: healthverity_super_dbc-default
upload_bucket: "healthverity.aetion.com/upload/healthverity_super_dbc/20220725"
hive_vars:
  GDR_END_DATE: "2022-07-25"
  GDR_START_DATE: "2018-12-01"
validation_vars:
  GDR_END_DATE: "2022-07-25"
  GDR_START_DATE: "2018-12-01"
transform_path: "healthverity/super_dbc_hv/current"
validation_path: "healthverity/super_dbc_hv/current"
git_branch_override: "remotes/origin/covid_super_dbc"
iam_arn: "arn:aws:iam::627533566824:instance-profile/healthverity-databricks-etl-iam"
is_k8s: false
pre_partitioned_data_url: "healthverity.aetion.com/etl/healthverity/20220725/"
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      ebs_volume_count: 3
      ebs_volume_size: 100
      ebs_volume_type: "GENERAL_PURPOSE_SSD"
    driver_node_type_id: "rd-fleet.8xlarge"
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 100
    spark_conf:
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: true
      spark.default.parallelism: 8000
      spark.driver.cores: 4
      spark.driver.maxResultSize: 0
      spark.executor.cores: 4
      spark.executor.extraJavaOptions:
        "-Xloggc:/dev/null -XX:+UseG1GC -XX:+UseStringDeduplication
        -XX:ParallelGCThreads=20 -XX:ConcGCThreads=5 -XX:InitiatingHeapOccupancyPercent=70
        -XX:MaxGCPauseMillis=200"
      spark.hadoop.fs.s3a.multipart.size: 504857600
      spark.hadoop.fs.s3a.multipart.threshold: **********
      spark.network.timeout: 1200
      spark.rpc.numRetries: 10
      spark.rpc.retry.wait: 10
      spark.sql.autoBroadcastJoinThreshold: 262144000
      spark.sql.broadcastTimeout: 1200
      spark.sql.objectHashAggregate.sortBased.fallbackThreshold: 1024
      spark.sql.shuffle.partitions: 16000
      spark.sql.sources.bucketing.maxBuckets: 200000
      spark.task.maxDirectResultSize: **********
  full_patient_job:
    spark_conf:
      spark.executor.cores: "3"
  full_shard_job:
    spark_conf:
      spark.executor.cores: "6"
  default:
    driver_node_type_id: "rd-fleet.8xlarge"
    node_type_id: "rd-fleet.8xlarge"
    num_workers: 80
    spark_conf:
      spark.databricks.adaptive.autoOptimizeShuffle.enabled: true
      spark.driver.cores: 4
      spark.driver.maxResultSize: "40g"
      spark.executor.cores: 8
      spark.sql.autoBroadcastJoinThreshold: 262144000
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
