defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@Damian"
client: "demo"
dataset: "marketscan_tiny_adm"
revision: "20241206"
tag: "default"
rdc_dbc_name: marketscan_tiny_adm-default
upload_bucket: "demo.aetion.com/upload/marketscan_tiny_adm/20241206"
deployment_config:
  dds: "dds.aetion.com"
hive_vars:
  MIN_DATE: "2015-10-01"
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
validation_vars:
  MIN_DATE: "2015-10-01"
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
transform_path: "marketscan_tiny_adm/demo/1.4"
validation_path: "adm/1.4"
native_data_for_adm_url: "s3://demo.aetion.com/etl/marketscan_tiny/********"
dynamic_flat_tables: true
dynamic_patient_table: true
git_branch_override: "master"
is_k8s: true
prophecy_pipeline_prefix_override: "marketscan"
regenerate_all_flat_tables: true
service_account: "spark-operator-client-demo"
skip_single_shard: false
use_smart_sampling: false
