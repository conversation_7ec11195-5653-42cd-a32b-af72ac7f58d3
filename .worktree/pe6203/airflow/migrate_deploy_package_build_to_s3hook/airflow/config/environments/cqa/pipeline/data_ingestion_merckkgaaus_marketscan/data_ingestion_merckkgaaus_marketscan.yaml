defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "marketscan"
client: "merckkgaaus"
revision: "20200204"
is_k8s: False
git_branch_override: "remotes/origin/merch-ms-20200204"
hive_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2007-12-31"
iam_arn: "arn:aws:iam::627533566824:instance-profile/merckkgaa-databricks-etl-iam"
source_files_password: ""
transform_path: "marketscan/merckkgaaus20200204"
upload_bucket: "merckkgaaus.aetion.com/upload/marketscan/20200204"
validation_path: "marketscan/merckkgaaus20200204"
