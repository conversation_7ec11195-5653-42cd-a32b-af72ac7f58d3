defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "jmdc_hospital"
client: "jmdc"
revision: "201906"
is_k8s: False
git_branch_override: "remotes/origin/jmdc-hospital-201906"
hive_vars:
  GDR_END_DATE: ""
  GDR_START_DATE: ""
iam_arn: "arn:aws:iam::627533566824:instance-profile/jmdc-databricks-etl-iam"
source_files_password: ""
transform_path: ""
upload_bucket: "jmdc.aetion.com/upload/jmdc_hospital/201906"
validation_path: ""
