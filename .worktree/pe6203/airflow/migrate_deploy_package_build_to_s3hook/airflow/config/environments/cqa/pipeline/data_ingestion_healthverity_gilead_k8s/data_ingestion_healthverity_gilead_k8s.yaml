defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@albert.pitarch"
client: "healthverity"
dataset: "gilead"
revision: "**********"
tag: "default"
rdc_dbc_name: gilead-default
upload_bucket: "healthverity.aetion.com/upload/gilead/**********"
deployment_config:
  healthverity: "realtgilead.aetion.com"
hive_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "2015-01-01"
validation_vars:
  GDR_END_DATE: "2024-06-30"
  GDR_START_DATE: "2015-01-01"
transform_path: "healthverity/gilead/current"
validation_path: "healthverity/gilead/current"
git_branch_override: "hv-gilead-k8s"
is_k8s: true
partition_full_parquet_url: "healthverity.aetion.com/upload/HV005186/********/"
pre_partitioned_data_url: "healthverity.aetion.com/etl/gilead/********/"
pyspark_memory_overhead_factor: ".4"
regenerate_all_flat_tables: true
service_account: "spark-operator-client-healthverity"
source_files_password: ""
steps_spark_config:
  full_job:
    autoscale:
      enabled: true
      initial_executors: 40
      max_executors: 80
      min_executors: 40
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    spark_conf:
      aetion.dataset.automaticNdcFormatting: false
      spark.decommission.enabled: true
      spark.default.parallelism: 100000
      spark.driver.extraJavaOptions: "-XX:+DisableExplicitGC -XX:+UseG1GC -XX:MaxGCPauseMillis=1000"
      spark.driver.maxResultSize: 0
      spark.eventLog.dir: "s3a://spark-history-server.infra.prod.aetion.com/spark-event-logs"
      spark.eventLog.enabled: true
      spark.eventLog.rolling.enabled: true
      spark.eventLog.rolling.maxFileSize: "128m"
      spark.executor.extraJavaOptions:
        "-XX:+UseG1GC -XX:MaxGCPauseMillis=1000 -XX:ReservedCodeCacheSize=512m
        -XX:+UseCodeCacheFlushing -XX:-UseContainerSupport -Xss4m -Djavax.net.ssl.sessionCacheSize=10000
        -Dscala.reflect.runtime.disable.typetag.cache=true -Dcom.google.cloud.spark.bigquery.repackaged.io.netty.tryReflectionSetAccessible=true
        -Dlog4j2.formatMsgNoLookups=true"
      spark.hadoop.fs.s3a.connection.maximum: 300
      spark.hadoop.fs.s3a.server-side-encryption-algorithm: "AES256"
      spark.hadoop.fs.s3a.threads.max: 200
      spark.memory.offHeap.enabled: false
      spark.scheduler.mode: "FAIR"
      spark.shuffle.io.clientThreads: 256
      spark.shuffle.io.maxRetries: 12
      spark.shuffle.io.numConnectionsPerPeer: 8
      spark.shuffle.io.serverThreads: 256
      spark.shuffle.io.threads: 512
      spark.shuffle.manager: "SORT"
      spark.shuffle.spill: true
      spark.sql.adaptive.coalescePartitions.enabled: true
      spark.sql.adaptive.enabled: true
      spark.sql.adaptive.localShuffleReader.enabled: true
      spark.sql.adaptive.optimizerEnabled: true
      spark.sql.adaptive.skewJoin.enabled: true
      spark.sql.autoBroadcastJoinThreshold: "-1"
      spark.sql.broadcastTimeout: "2000000"
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
      spark.sql.legacy.timeParserPolicy: "LEGACY"
      spark.sql.parquet.binaryAsString: true
      spark.sql.shuffle.partitions: 80000
      spark.storage.decommission.enabled: true
      spark.storage.decommission.rddBlocks.enabled: true
      spark.storage.decommission.shuffleBlocks.enabled: true
      spark.storage.decommission.shuffleBlocks.maxThreads: 32
      spark.storage.memoryFraction: "0.5"
      spark.task.cpus: 1
      spark.task.reaper.enabled: true
      spark.task.reaper.killTimeout: "60s"
    worker_conf:
      instance_type: "r5ad.8xlarge"
      pyspark_memory_overhead: "94g"
  full_patient_job:
    autoscale:
      enabled: true
      initial_executors: 100
      max_executors: 100
      min_executors: 100
    spark_conf:
      spark.shuffle.io.clientThreads: 256
      spark.shuffle.io.serverThreads: 256
      spark.shuffle.io.threads: 512
      spark.sql.parquet.columnarReaderBatchSize: 64
      spark.task.cpus: 5
    worker_conf:
      instance_type: "r5ad.8xlarge"
      pyspark_memory_overhead: "94g"
  full_shard_job:
    autoscale:
      enabled: true
      initial_executors: 1
      max_executors: 140
      min_executors: 1
    driver_conf:
      instance_type: "r5ad.8xlarge"
      pyspark_memory_overhead: "94g"
    spark_conf:
      spark.driver.maxResultSize: 0
      spark.executor.extraJavaOptions: "-XX:+UseG1GC -XX:MaxGCPauseMillis=500"
      spark.shuffle.io.clientThreads: 256
      spark.shuffle.io.serverThreads: 256
      spark.shuffle.io.threads: 512
      spark.shuffle.spill: true
      spark.sql.shuffle.partitions: 120000
      spark.task.cpus: 4
    worker_conf:
      instance_type: "r5ad.8xlarge"
      pyspark_memory_overhead: "94g"
  default:
    autoscale:
      enabled: false
      initial_executors: 10
      max_executors: 10
      min_executors: 10
    driver_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
    num_workers: 10
    spark_conf:
      spark.driver.maxResultSize: "0"
      spark.sql.autoBroadcastJoinThreshold: "-1"
      spark.sql.broadcastTimeout: "20000"
      spark.sql.legacy.parquet.datetimeRebaseModeInRead: "LEGACY"
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "CORRECTED"
    worker_conf:
      instance_type: "r5a.4xlarge"
      pyspark_memory_overhead: "26500m"
