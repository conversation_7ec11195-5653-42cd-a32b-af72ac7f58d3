defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
dataset: "rpdrmax"
client: "bwh"
revision: "20200121"
is_k8s: False
validation_path: "rpdrmax/20200121"
git_branch_override: "remotes/origin/bwh-rpdrmax-20200121"
hive_vars:
  GDR_END_DATE: "2020-01-01"
  GDR_START_DATE: "1999-01-01"
iam_arn: "arn:aws:iam::627533566824:instance-profile/bwhdope-databricks-etl-iam"
source_files_password: ""
transform_path: "rpdrmax/20200121"
upload_bucket: "bwh.aetion.com/upload/rpdrmax/20200121"
