defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "abbvie"
dataset: "allpayer_hcv"
revision: "201907"
tag: abbvie
rdc_dbc_name: allpayer_hcv-abbvie
upload_bucket: "abbvie.aetion.com/upload/allpayer-hcv/201907"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2007-01-01"
transform_path: "humedica/allpayer-hcv201907"
validation_path: "humedica/allpayer-hcv201907"
git_branch_override: "remotes/origin/default-allpayer-optum"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
is_k8s: false
source_files_password: ""
