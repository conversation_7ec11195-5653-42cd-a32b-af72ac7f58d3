defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@peter.imrich"
client: "adn"
dataset: "marketscan"
revision: "********02"
tag: "pl"
rdc_dbc_name: marketscan-pl
upload_bucket: "adn.aetion.com/upload/marketscan/********02"
deployment_config:
  demo: "demo.aetion.com"
hive_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2013-12-31"
  LAB_END_DATE: "2018-09-30"
  LAB_START_DATE: "2013-12-31"
validation_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2013-12-31"
transform_path: "marketscan/adn/current"
validation_path: "marketscan/adn/current"
git_branch_override: "master"
iam_arn: "arn:aws:iam::************:instance-profile/adn-databricks-etl-iam"
is_k8s: true
pre_partitioned_data_url: "adn.aetion.com/etl/marketscan/********"
regenerate_all_flat_tables: true
service_account: "spark-operator-client-adn"
source_files_password: ""
