defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
client: "abbvie"
dataset: "allpayer_imm"
revision: "201906"
tag: abbvie
rdc_dbc_name: allpayer_imm-abbvie
upload_bucket: "abbvie.aetion.com/upload/allpayer-imm/201906"
hive_vars:
  GDR_END_DATE: "2018-09-30"
  GDR_START_DATE: "2007-01-01"
transform_path: "humedica/allpayer-imm201906"
validation_path: "humedica/allpayer-imm201906"
iam_arn: "arn:aws:iam::627533566824:instance-profile/abbvie-databricks-etl-iam"
is_k8s: false
source_files_password: ""
