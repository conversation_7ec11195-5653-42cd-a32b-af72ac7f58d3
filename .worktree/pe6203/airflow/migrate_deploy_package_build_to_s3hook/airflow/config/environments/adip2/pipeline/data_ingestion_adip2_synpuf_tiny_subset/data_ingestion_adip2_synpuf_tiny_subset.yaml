defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "adip2"
dataset: "synpuf_tiny_subset"
revision: "20221004"
tag: "default"
rdc_dbc_name: synpuf_tiny_subset-default
upload_bucket: "s3://adip2.app.dev.aetion.com/upload/synpuf_tiny_subset/20221004"
deployment_config:
  aep-dev: "adip2.app.dev.aetion.com"
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
transform_path: "synpuf_tiny_subset/current"
validation_path: "synpuf_tiny_subset/current"
copy_spec: false
domain: "app.dev.aetion.com"
dynamic_flat_tables: false
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
iam_arn: "arn:aws:iam::179392497636:instance-profile/adip2-dev1-b-databricks-etl-iam"
is_k8s: false
regenerate_all_flat_tables: true
skip_single_shard: true
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "auto"
    node_type_id: "i3.2xlarge"
    num_workers: 2
    spark_conf:
      spark.default.parallelism: 4
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
      spark.sql.shuffle.partitions: 4
  default:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "auto"
    node_type_id: "i3.2xlarge"
    num_workers: 2
    spark_conf:
      spark.default.parallelism: 4
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
      spark.sql.shuffle.partitions: 4
