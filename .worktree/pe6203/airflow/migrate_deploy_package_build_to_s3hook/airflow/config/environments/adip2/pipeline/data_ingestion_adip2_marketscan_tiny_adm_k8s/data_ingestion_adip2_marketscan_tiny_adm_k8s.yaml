defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /step_group_cluster_config/tiny_k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "adip2"
dataset: "marketscan_tiny_adm"
revision: "20240716"
tag: "default"
rdc_dbc_name: marketscan_tiny_adm-default
upload_bucket: "s3://adip2.app.dev.aetion.com/upload/marketscan_tiny_adm/20240716"
deployment_config:
  aep-dev: "adip2.app.dev.aetion.com"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
  MIN_DATE: "2015-10-01"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
  MIN_DATE: "2015-10-01"
transform_path: "marketscan_tiny_adm/demo/1.4"
validation_path: "marketscan_tiny_adm/1.4"
native_data_for_adm_url: "s3://adip2.app.dev.aetion.com/etl/marketscan_tiny/********"
dataset_group: "LABS"
domain: "app.dev.aetion.com"
dynamic_flat_tables: true
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
is_k8s: true
prophecy_pipeline_prefix_override: "marketscan"
sampled_data_percentage: "50"
service_account: "spark-operator-client-common"
skip_single_shard: true
source_files_password: ""
use_smart_sampling: false
steps_spark_config:
  unarchiver_job_only:
    autoscale:
      initial_executors: 1
      max_executors: 20
      min_executors: 1
    driver_conf:
      instance_type: "r5a.2xlarge"
    worker_conf:
      instance_type: "r5a.2xlarge"
steps:
  unarchiver:
    step_group: "unarchiver_job_only"
  generate_full_enums_and_dictionaries:
    step_group: "generate_full_enums_and_dictionaries_only"
