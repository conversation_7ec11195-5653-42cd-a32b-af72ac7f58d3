defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "adip2"
dataset: "marketscan_tiny_adm"
revision: "20240716"
tag: "default"
rdc_dbc_name: marketscan_tiny_adm-default
upload_bucket: "s3://adip2.app.dev.aetion.com/upload/marketscan_tiny_adm/20240716"
deployment_config:
  aep-dev: "adip2.app.dev.aetion.com"
hive_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
  MIN_DATE: "2015-10-01"
validation_vars:
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
  MIN_DATE: "2015-10-01"
transform_path: "marketscan_tiny_adm/demo/1.4"
validation_path: "marketscan_tiny_adm/1.4"
native_data_for_adm_url: "s3://adip2.app.dev.aetion.com/etl/marketscan_tiny/20240208"
dataset_group: "LABS"
domain: "app.dev.aetion.com"
dynamic_flat_tables: true
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
iam_arn: "arn:aws:iam::179392497636:instance-profile/adip2-dev1-b-databricks-etl-iam"
is_k8s: false
prophecy_pipeline_prefix_override: "marketscan"
sampled_data_percentage: "50"
skip_single_shard: true
source_files_password: ""
use_smart_sampling: false
