defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "${default_adip_overrides.client}"
dataset: "synpuf_subset"
revision: "********"
tag: "default"
rdc_dbc_name: synpuf_subset-default
upload_bucket: "s3://${default_adip_overrides.client}.${default_adip_overrides.domain}/upload/synpuf_subset/********"
deployment_config:
  aep-${clusterRequest.environment}: "${default_adip_overrides.s3_bucket}"
hive_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
validation_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
transform_path: "synpuf_subset/********"
validation_path: "synpuf_subset/********"
copy_spec: false
domain: "${default_adip_overrides.domain}"
dynamic_flat_tables: false
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
iam_arn: "arn:aws:iam::${default_adip_overrides.account_id}:instance-profile/${default_adip_overrides.profile}"
is_k8s: true
regenerate_all_flat_tables: false
skip_single_shard: false
source_files_password: ""
service_account: "spark-operator-client-${default_adip_overrides.service_account_client_name}"
steps_spark_config:
  default:
    autoscale:
      min_executors: 1
      initial_executors: 1
      max_executors: 4
    worker_conf:
      cores: 15
      memory: "70g"
      memory_on_disk: "128G"
  full_job:
    spark_conf:
      spark.sql.shuffle.partitions: "200"
