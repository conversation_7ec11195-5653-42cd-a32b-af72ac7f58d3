defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "${default_adip_overrides.client}"
dataset: "synpuf_subset"
revision: "********"
tag: "default"
rdc_dbc_name: synpuf_subset-default
upload_bucket: "s3://${default_adip_overrides.client}.${default_adip_overrides.domain}/upload/synpuf_subset/********"
deployment_config:
  aep-${clusterRequest.environment}: "${default_adip_overrides.s3_bucket}"
hive_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
validation_vars:
  GDR_END_DATE: "2010-12-31"
  GDR_START_DATE: "2008-01-01"
transform_path: "synpuf_subset/********"
validation_path: "synpuf_subset/********"
copy_spec: false
domain: "${default_adip_overrides.domain}"
dynamic_flat_tables: false
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
iam_arn: "arn:aws:iam::${default_adip_overrides.account_id}:instance-profile/${default_adip_overrides.profile}"
is_k8s: false
regenerate_all_flat_tables: false
skip_single_shard: false
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "${default_adip_overrides.databricks_job_cluster_zone_id}"
    spark_conf:
      spark.default.parallelism: 6000
      spark.executor.cores: 8
      spark.sql.shuffle.partitions: 6000
  default:
    spark_conf:
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
