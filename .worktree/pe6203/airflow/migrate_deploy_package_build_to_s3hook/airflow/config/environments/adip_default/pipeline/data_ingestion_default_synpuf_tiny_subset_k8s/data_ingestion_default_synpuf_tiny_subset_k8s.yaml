defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "${default_adip_overrides.client}"
dataset: "synpuf_tiny_subset"
revision: "********"
tag: "default"
rdc_dbc_name: synpuf_tiny_subset-default
upload_bucket: "s3://${default_adip_overrides.client}.${default_adip_overrides.domain}/upload/synpuf_tiny_subset/********"
deployment_config:
  aep-${clusterRequest.environment}: "${default_adip_overrides.s3_bucket}"
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
transform_path: "synpuf_tiny_subset/current"
validation_path: "synpuf_tiny_subset/current"
celeborn_feature:
  enabled: "${default_adip_overrides.celeborn_enabled}"
copy_spec: false
domain: "${default_adip_overrides.domain}"
dynamic_flat_tables: false
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
is_k8s: true
rde_revision_check: "********"
regenerate_all_flat_tables: true
service_account: "spark-operator-client-${default_adip_overrides.service_account_client_name}"
skip_single_shard: true
source_files_password: ""
