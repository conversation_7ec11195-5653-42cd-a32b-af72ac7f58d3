defaults:
  - /default_pipelines/data_ingestion_pipeline@_here_
  - /step_group_cluster_config/tiny_databricks_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "${default_adip_overrides.client}"
dataset: "synpuf_tiny_subset"
revision: "20221004"
tag: "default"
rdc_dbc_name: synpuf_tiny_subset-default
upload_bucket: "s3://${default_adip_overrides.client}.${default_adip_overrides.domain}/upload/synpuf_tiny_subset/20221004"
deployment_config:
  aep-${clusterRequest.environment}: "${default_adip_overrides.s3_bucket}"
hive_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
validation_vars:
  GDR_END_DATE: "2011-02-16"
  GDR_START_DATE: "2008-01-01"
transform_path: "synpuf_tiny_subset/current"
validation_path: "synpuf_tiny_subset/current"
copy_spec: false
domain: "${default_adip_overrides.domain}"
dynamic_flat_tables: false
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
iam_arn: "arn:aws:iam::${default_adip_overrides.account_id}:instance-profile/${default_adip_overrides.profile}"
is_k8s: false
regenerate_all_flat_tables: true
skip_single_shard: true
source_files_password: ""
steps_spark_config:
  full_job:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "${default_adip_overrides.databricks_job_cluster_zone_id}"
    node_type_id: "${default_adip_overrides.databricks_job_cluster_node_type_id}"
    num_workers: 2
    spark_conf:
      spark.default.parallelism: 4
      spark.sql.shuffle.partitions: 4
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
  default:
    aws_attributes:
      availability: "ON_DEMAND"
      zone_id: "${default_adip_overrides.databricks_job_cluster_zone_id}"
    node_type_id: "${default_adip_overrides.databricks_job_cluster_node_type_id}"
    num_workers: 2
    spark_conf:
      spark.default.parallelism: 4
      spark.sql.shuffle.partitions: 4
      spark.sql.legacy.parquet.datetimeRebaseModeInWrite: "LEGACY"
