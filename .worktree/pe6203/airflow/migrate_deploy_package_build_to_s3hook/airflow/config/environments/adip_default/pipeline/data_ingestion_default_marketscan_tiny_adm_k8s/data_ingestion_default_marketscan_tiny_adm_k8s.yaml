defaults:
  - /default_pipelines/data_ingestion_adm_pipeline@_here_
  - /step_group_cluster_config/tiny_k8s_step_group_config@steps_spark_config
  - _self_
alert_user: "@dev-alert-airflow"
client: "${default_adip_overrides.client}"
dataset: "marketscan_tiny_adm"
revision: "20240716"
tag: "default"
rdc_dbc_name: marketscan_tiny_adm-default
upload_bucket: "s3://${default_adip_overrides.client}.${default_adip_overrides.domain}/upload/marketscan_tiny_adm/20240716"
deployment_config:
  aep-${clusterRequest.environment}: "${default_adip_overrides.s3_bucket}"
hive_vars:
  MIN_DATE: "2015-10-01"
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
validation_vars:
  MIN_DATE: "2015-10-01"
  GDR_END_DATE: "2018-12-31"
  GDR_START_DATE: "2015-10-01"
transform_path: "marketscan_tiny_adm/demo/1.4"
validation_path: "marketscan_adm/1.4"
native_data_for_adm_url: "s3://${default_adip_overrides.s3_bucket}/etl/marketscan_tiny/********"
dataset_group: "LABS"
domain: "${default_adip_overrides.domain}"
dynamic_flat_tables: true
dynamic_patient_table: true
fail_stopgap: false
full_shard_stopgap: false
is_k8s: true
prophecy_pipeline_prefix_override: "marketscan"
sampled_data_percentage: "50"
service_account: "spark-operator-client-${default_adip_overrides.service_account_client_name}"
skip_single_shard: true
source_files_password: ""
use_smart_sampling: false
